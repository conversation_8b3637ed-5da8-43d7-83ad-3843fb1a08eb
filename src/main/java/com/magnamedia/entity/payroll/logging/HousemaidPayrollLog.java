package com.magnamedia.entity.payroll.logging;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.serialize.IdSerializer;
import com.magnamedia.entity.*;
import com.magnamedia.entity.rooms.BedAssignment;
import com.magnamedia.entity.HousemaidPayrollBean;
import com.magnamedia.extra.LabelValueEnum;
import com.magnamedia.extra.payroll.init.HousemaidPayrollInitializer;
import com.magnamedia.helper.*;
import com.magnamedia.module.type.HousemaidUnpaidStatus;
import com.magnamedia.repository.BedAssignmentRepository;
import com.magnamedia.repository.HousemaidDocumentRepository;
import com.magnamedia.repository.NewVisaRequestRepository;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newversion.PayrollGenerationHelperService;
import com.magnamedia.service.payroll.generation.newversion.ProRatedSalariesService;
import org.hibernate.annotations.ColumnDefault;
import org.joda.time.LocalDate;

import javax.persistence.*;
import java.sql.Date;
import java.util.List;
import java.util.Map;

@Entity @org.hibernate.envers.Audited
public class HousemaidPayrollLog extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private Housemaid housemaid;

    @Column(columnDefinition = "boolean default false")
    private Boolean forFinalSettlement = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    MonthlyPaymentRule monthlyPaymentRule;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    PayrollAccountantTodo payrollAccountantTodo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    PayrollAuditTodo payrollAuditTodo;

    @Column(columnDefinition = "boolean default false")
    private Boolean transferred = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean payslipGenerated = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean payslipSent = false;

    @Enumerated(EnumType.STRING)
    private HousemaidUnpaidStatus housemaidUnpaidStatus;

    @Enumerated(EnumType.STRING)
    private HousemaidPayrollLogStatus logStatus = HousemaidPayrollLogStatus.PENDING;

    @ColumnDefault("0")
    private Long onVacationDays;

    @Column(columnDefinition = "boolean default false")
    private Boolean willBeIncluded = false;

    @Column
    private String paidOnDate;

    @Column
    private String paidOnStatus;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    HousemaidPayrollLog maidParentLog;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private HousemaidPayrollBean housemaidPayrollBean;

    // Required for WPS file
    private String sn;
    private String recordType;
    private String employeeUniqueId;
    private String employeeName;
    private String agentId;
    private String employeeAccountWithAgent;
    private Date payStartDate;
    private Date payEndDate;
    private int daysInPeriod;
    private Double totalSalary;

    // Required for local transfer file
    private String receiverName;
    private String destinationOfTransfer;
    private String mobileNumber;


    // Required for pay cash salaries
    private String status;
    private String photo;
    private String apartment;
    private String nationality;
    private Date payrollMonth;

    public HousemaidPayrollLog() {
    }

    public HousemaidPayrollLog(HousemaidPayrollLog log) {
        if (log != null) {
            this.housemaid = log.housemaid;
            this.monthlyPaymentRule = log.monthlyPaymentRule;
            this.payrollAuditTodo = log.payrollAuditTodo;
            this.transferred = log.transferred;
            this.payslipGenerated = log.payslipGenerated;
            this.payslipSent = log.payslipSent;
            this.housemaidUnpaidStatus = log.housemaidUnpaidStatus;
            this.logStatus = log.logStatus;
            this.onVacationDays = log.onVacationDays;
            this.willBeIncluded = log.willBeIncluded;
            this.paidOnDate = log.paidOnDate;
            this.paidOnStatus = log.paidOnStatus;
            this.maidParentLog = log.maidParentLog;
            this.recordType = log.recordType;
            this.employeeUniqueId = log.employeeUniqueId;
            this.employeeName = log.employeeName;
            this.agentId = log.agentId;
            this.employeeAccountWithAgent = log.employeeAccountWithAgent;
            this.payStartDate = log.payStartDate;
            this.payEndDate = log.payEndDate;
            this.daysInPeriod = log.daysInPeriod;
            this.totalSalary = log.totalSalary;
            this.receiverName = log.receiverName;
            this.destinationOfTransfer = log.destinationOfTransfer;
            this.mobileNumber = log.mobileNumber;
            this.status = log.status;
            this.photo = log.photo;
            this.apartment = log.apartment;
            this.nationality = log.nationality;
            this.payrollMonth = log.payrollMonth;
            this.accommodationSalary = log.accommodationSalary;
            this.primarySalary = log.primarySalary;
            this.basicSalary = log.basicSalary;
            this.monthlyLoan = log.monthlyLoan;
            this.overTime = log.overTime;
            this.holiday = log.holiday;
            this.airfareFee = log.airfareFee;
            this.foodAllowance = log.foodAllowance;
            this.housingAllowance = log.housingAllowance;
            this.startDateDeduction = log.startDateDeduction;
            this.managerAdditions = log.managerAdditions;
            this.loanRepayment = log.loanRepayment;
            this.remainingLoan = log.remainingLoan;
            this.unpaidDeduction = log.unpaidDeduction;
            this.unpaidDeductionRepayment = log.unpaidDeductionRepayment;
            this.additionToCoverDeductionLimit = log.additionToCoverDeductionLimit;
            this.totalDeduction = log.totalDeduction;
            this.totalAddition = log.totalAddition;
            this.groupOneDays = log.groupOneDays;
            this.groupTwoDays = log.groupTwoDays;
            this.groupThreeDays = log.groupThreeDays;
            this.groupFourDays = log.groupFourDays;
            this.groupFiveDays = log.groupFiveDays;
            this.groupSixDays = log.groupSixDays;
            this.totalProRatedSalary = log.totalProRatedSalary;
            this.mohreProRatedSalary = log.mohreProRatedSalary;
            this.proratedCashAdvance = log.proratedCashAdvance;
            this.vacationSalary = log.vacationSalary;
            this.totalLiveOutProRatedSalary = log.totalLiveOutProRatedSalary;
            this.mohreLiveOutProRatedSalary = log.mohreLiveOutProRatedSalary;
            this.totalEarnings = log.totalEarnings;
        }
    }

    // Used for WPS file
    public HousemaidPayrollLog(Housemaid housemaid, String recordType, String employeeName, HousemaidPayrollInitializer initializer, Double totalSalary, Date payrollMonth,
                               Double startDateDeduction, Double managerAdditions, Double loanRepayment, Double remainingLoan, Double additionToCoverDeductionLimit,
                               Double totalDeduction, Double totalAddition, Double unpaidDeduction, Double unpaidDeductionRepayment, String receiverName, String destinationOfTransfer, String mobileNumber) {
        this.housemaid = housemaid;
        this.status = housemaid.getRealStatus();
        this.recordType = recordType;
        this.employeeName = employeeName;
        //if(newRequest != null) {
        this.employeeUniqueId = initializer.getEmployeeId(housemaid.getId());//.getEmployeeUniqueId();
        this.agentId = initializer.getAgentId(housemaid.getId());//.getAgentId();
        this.employeeAccountWithAgent = initializer.getAccountWithAgent(housemaid.getId());//.getEmployeeAccountWithAgent();
        // }
        this.payStartDate = new Date(new LocalDate(payrollMonth).withDayOfMonth(1).toDate().getTime());
        this.payEndDate = new Date(new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate().getTime());
        this.daysInPeriod = DateUtil.getDaysBetween(this.payStartDate, this.payEndDate) + 1;
        this.totalSalary = totalSalary == null ? 0d : totalSalary;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());

        this.receiverName = receiverName;
        this.destinationOfTransfer = destinationOfTransfer;
        this.mobileNumber = mobileNumber;

        fillPayslipFields(housemaid, startDateDeduction, managerAdditions, loanRepayment, remainingLoan, additionToCoverDeductionLimit, totalDeduction, totalAddition, unpaidDeduction, unpaidDeductionRepayment);
        fillProratedAmount(housemaid, initializer);
    }

    // Used for WPS file For FINAL SETTLEMENT
    public HousemaidPayrollLog(Housemaid housemaid, String recordType, String employeeName, PayrollHousemaidFinalSettlement finalSettlement, Date payrollMonth,
                               String receiverName, String destinationOfTransfer, String mobileNumber) {
        Double finalSettlementValue = finalSettlement == null ? 0.0 : (finalSettlement.getFinalValue() != null ? finalSettlement.getFinalValue() : finalSettlement.getCalculatedFinalSettlementValue());
        this.housemaid = housemaid;
        this.forFinalSettlement = true;
        this.status = housemaid.getRealStatus();
        this.recordType = recordType;
        this.employeeName = employeeName;

        NewRequest newRequest = Setup.getRepository(NewVisaRequestRepository.class).findFirstByHousemaidOrderByCreationDateDesc(housemaid);
        if (newRequest != null) {
            this.employeeUniqueId = newRequest.getEmployeeUniqueId();
            this.agentId = newRequest.getAgentId();
            this.employeeAccountWithAgent = newRequest.getEmployeeAccountWithAgent();
        }
        this.payStartDate = new Date(new LocalDate(payrollMonth).withDayOfMonth(1).toDate().getTime());
        this.payEndDate = new Date(new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate().getTime());
        this.daysInPeriod = DateUtil.getDaysBetween(this.payStartDate, this.payEndDate) + 1;
        this.totalSalary = finalSettlementValue;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());

        this.receiverName = receiverName;
        this.destinationOfTransfer = destinationOfTransfer;
        this.mobileNumber = mobileNumber;
    }

    // Used for WPS file For CC Switched To Mv
    public HousemaidPayrollLog(Housemaid housemaid, String recordType, String employeeName, CcMaidSwitchedToMv maidSwitchedToMv, Date payrollMonth,
                               String receiverName, String destinationOfTransfer, String mobileNumber) {
        this.housemaid = housemaid;
        this.forFinalSettlement = true;
        this.status = housemaid.getRealStatus();
        this.recordType = recordType;
        this.employeeName = employeeName;

        NewRequest newRequest = Setup.getRepository(NewVisaRequestRepository.class).findFirstByHousemaidOrderByCreationDateDesc(housemaid);
        if (newRequest != null) {
            this.employeeUniqueId = newRequest.getEmployeeUniqueId();
            this.agentId = newRequest.getAgentId();
            this.employeeAccountWithAgent = newRequest.getEmployeeAccountWithAgent();
        }
        this.payStartDate = new Date(new LocalDate(payrollMonth).withDayOfMonth(1).toDate().getTime());
        this.payEndDate = new Date(new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate().getTime());
        this.daysInPeriod = DateUtil.getDaysBetween(this.payStartDate, this.payEndDate) + 1;
        this.totalSalary = maidSwitchedToMv.getFinalAmountToBeTransferred();
        this.totalAddition = maidSwitchedToMv.getLastCcSalary() + maidSwitchedToMv.getAdditionsAmount();
        this.totalEarnings = maidSwitchedToMv.getLastCcSalary();
        this.unpaidDeductionRepayment = maidSwitchedToMv.getDeductionsAmount();
        this.loanRepayment = maidSwitchedToMv.getRepaymentAmount();
        this.paidOnStatus = "CC Switching to MV";
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());

        this.receiverName = receiverName;
        this.destinationOfTransfer = destinationOfTransfer;
        this.mobileNumber = mobileNumber;
    }

    // Used for WPS file (OVERLOAD)
    public HousemaidPayrollLog(Housemaid housemaid, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, String recordType, String employeeName, Double totalSalary, Date payrollMonth,
                               Double startDateDeduction, Double managerAdditions, Double loanRepayment, Double remainingLoan, Double additionToCoverDeductionLimit,
                               Double totalDeduction, Double totalAddition, Double unpaidDeduction, Double unpaidDeductionRepayment, String receiverName, String destinationOfTransfer, String mobileNumber) {
        this.housemaid = housemaid;
        if (monthlyPaymentRule != null)
            this.monthlyPaymentRule = monthlyPaymentRule;
        if (auditTodo != null)
            this.payrollAuditTodo = auditTodo;
        if (todo != null)
            this.payrollAccountantTodo = todo;
        this.status = housemaid.getRealStatus();
        this.recordType = recordType;
        this.employeeName = employeeName;
        this.employeeUniqueId = Setup.getApplicationContext().getBean(PayrollGenerationHelperService.class).getEmployeeUniqueId(housemaid);
        this.agentId = Setup.getApplicationContext().getBean(PayrollGenerationHelperService.class).getAgentId(housemaid);
        this.employeeAccountWithAgent = Setup.getApplicationContext().getBean(PayrollGenerationHelperService.class).getEmployeeAccountWithAgent(housemaid);
        this.payStartDate = new Date(new LocalDate(payrollMonth).withDayOfMonth(1).toDate().getTime());
        this.payEndDate = new Date(new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate().getTime());
        this.daysInPeriod = DateUtil.getDaysBetween(this.payStartDate, this.payEndDate) + 1;
        this.totalSalary = totalSalary == null ? 0d : totalSalary;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());

        this.receiverName = receiverName;
        this.destinationOfTransfer = destinationOfTransfer;
        this.mobileNumber = mobileNumber;

        fillPayslipFields(housemaid, startDateDeduction, managerAdditions, loanRepayment, remainingLoan, additionToCoverDeductionLimit, totalDeduction, totalAddition, unpaidDeduction, unpaidDeductionRepayment);
        fillProratedAmountNew(housemaid, payrollMonth, monthlyPaymentRule.isSecondaryMonthlyRule());
    }

    //Used for local transfer file
    public HousemaidPayrollLog(HousemaidPayrollInitializer initializer, Housemaid housemaid, String receiverName, String destinationOfTransfer, String mobileNumber, Double totalSalary, Date payrollMonth,
                               Double startDateDeduction, Double managerAdditions, Double loanRepayment, Double remainingLoan, Double additionToCoverDeductionLimit,
                               Double totalDeduction, Double totalAddition, Double unpaidDeduction, Double unpaidDeductionRepayment, String recordType, String employeeName) {
        this.housemaid = housemaid;
        this.status = housemaid.getRealStatus();
        this.receiverName = receiverName;
        this.destinationOfTransfer = destinationOfTransfer;
        this.mobileNumber = mobileNumber;
        this.totalSalary = totalSalary == null ? 0d : totalSalary;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());

        this.employeeName = employeeName;
        this.employeeUniqueId = initializer.getEmployeeId(housemaid.getId());//.getEmployeeUniqueId();
        this.agentId = initializer.getAgentId(housemaid.getId());//.getAgentId();
        this.employeeAccountWithAgent = initializer.getAccountWithAgent(housemaid.getId());//.getEmployeeAccountWithAgent();
        this.recordType = recordType;
        this.payStartDate = new Date(new LocalDate(payrollMonth).withDayOfMonth(1).toDate().getTime());
        this.payEndDate = new Date(new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate().getTime());
        this.daysInPeriod = DateUtil.getDaysBetween(this.payStartDate, this.payEndDate) + 1;

        fillPayslipFields(housemaid, startDateDeduction, managerAdditions, loanRepayment, remainingLoan, additionToCoverDeductionLimit, totalDeduction, totalAddition, unpaidDeduction, unpaidDeductionRepayment);
        fillProratedAmount(housemaid, initializer);
    }

    //Used for local transfer file (OVERLOAD)
    public HousemaidPayrollLog(Housemaid housemaid, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, String receiverName, String destinationOfTransfer, String mobileNumber, Double totalSalary, Date payrollMonth,
                               Double startDateDeduction, Double managerAdditions, Double loanRepayment, Double remainingLoan, Double additionToCoverDeductionLimit,
                               Double totalDeduction, Double totalAddition, Double unpaidDeduction, Double unpaidDeductionRepayment, String recordType, String employeeName) {
        this.housemaid = housemaid;
        if (monthlyPaymentRule != null)
            this.monthlyPaymentRule = monthlyPaymentRule;
        if (auditTodo != null)
            this.payrollAuditTodo = auditTodo;
        if (todo != null)
            this.payrollAccountantTodo = todo;
        this.status = housemaid.getRealStatus();
        this.receiverName = receiverName;
        this.destinationOfTransfer = destinationOfTransfer;
        this.mobileNumber = mobileNumber;
        this.totalSalary = totalSalary == null ? 0d : totalSalary;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());

        this.employeeName = employeeName;
        this.employeeUniqueId = Setup.getApplicationContext().getBean(PayrollGenerationHelperService.class).getEmployeeUniqueId(housemaid);
        this.agentId = Setup.getApplicationContext().getBean(PayrollGenerationHelperService.class).getAgentId(housemaid);
        this.employeeAccountWithAgent = Setup.getApplicationContext().getBean(PayrollGenerationHelperService.class).getEmployeeAccountWithAgent(housemaid);
        this.recordType = recordType;
        this.payStartDate = new Date(new LocalDate(payrollMonth).withDayOfMonth(1).toDate().getTime());
        this.payEndDate = new Date(new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate().getTime());
        this.daysInPeriod = DateUtil.getDaysBetween(this.payStartDate, this.payEndDate) + 1;

        fillPayslipFields(housemaid, startDateDeduction, managerAdditions, loanRepayment, remainingLoan, additionToCoverDeductionLimit, totalDeduction, totalAddition, unpaidDeduction, unpaidDeductionRepayment);
        fillProratedAmountNew(housemaid, payrollMonth, monthlyPaymentRule.isSecondaryMonthlyRule());
    }

    //Used for pay cash salaries
    public HousemaidPayrollLog(HousemaidPayrollInitializer initializer, Housemaid housemaid, String employeeName, Double totalSalary, Date payrollMonth,
                               Double startDateDeduction, Double managerAdditions, Double loanRepayment, Double remainingLoan, Double additionToCoverDeductionLimit,
                               Double totalDeduction, Double totalAddition, Double unpaidDeduction, Double unpaidDeductionRepayment) {
        this.housemaid = housemaid;
        this.status = housemaid.getRealStatus();
        this.employeeName = employeeName;
        this.totalSalary = totalSalary == null ? 0d : totalSalary;

        this.nationality = housemaid.getNationality() != null ? housemaid.getNationality().getName() : "";
        List<HousemaidDocument> documents = Setup.getRepository(HousemaidDocumentRepository.class)
                .findByHousemaidAndTypeOrderByCreationDateDesc(housemaid, PicklistHelper.getItem("housemaidDocType", "photo"));
        if (documents != null && !documents.isEmpty()) {
            this.photo = documents.get(0).getAttachment("photo").getUuid();
        }

        List<BedAssignment> beds = Setup.getRepository(BedAssignmentRepository.class)
                .findByHousemaidAndStatus(housemaid, BedAssignment.BedAssignmentStatus.ACTIVE);

        if (!beds.isEmpty()) {
            if (beds.get(0).getRoom() != null) {
                this.apartment = beds.get(0).getRoom().getRoomNumber();
            }
        }
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());

        fillPayslipFields(housemaid, startDateDeduction, managerAdditions, loanRepayment, remainingLoan, additionToCoverDeductionLimit, totalDeduction, totalAddition, unpaidDeduction, unpaidDeductionRepayment);
        fillProratedAmount(housemaid, initializer);
    }

    //Used for pay cash salaries (OVERLOAD)
    public HousemaidPayrollLog(Housemaid housemaid, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, String employeeName, Double totalSalary, Date payrollMonth,
                               Double startDateDeduction, Double managerAdditions, Double loanRepayment, Double remainingLoan, Double additionToCoverDeductionLimit,
                               Double totalDeduction, Double totalAddition, Double unpaidDeduction, Double unpaidDeductionRepayment) {
        this.housemaid = housemaid;
        if (monthlyPaymentRule != null)
            this.monthlyPaymentRule = monthlyPaymentRule;
        if (auditTodo != null)
            this.payrollAuditTodo = auditTodo;
        if (todo != null)
            this.payrollAccountantTodo = todo;
        this.status = housemaid.getRealStatus();
        this.employeeName = employeeName;
        this.totalSalary = totalSalary == null ? 0d : totalSalary;

        this.nationality = housemaid.getNationality() != null ? housemaid.getNationality().getName() : "";
        List<HousemaidDocument> documents = Setup.getRepository(HousemaidDocumentRepository.class)
                .findByHousemaidAndTypeOrderByCreationDateDesc(housemaid, PicklistHelper.getItem("housemaidDocType", "photo"));
        if (documents != null && !documents.isEmpty()) {
            this.photo = documents.get(0).getAttachment("photo").getUuid();
        }

        List<BedAssignment> beds = Setup.getRepository(BedAssignmentRepository.class)
                .findByHousemaidAndStatus(housemaid, BedAssignment.BedAssignmentStatus.ACTIVE);

        if (!beds.isEmpty()) {
            if (beds.get(0).getRoom() != null) {
                this.apartment = beds.get(0).getRoom().getRoomNumber();
            }
        }
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());

        fillPayslipFields(housemaid, startDateDeduction, managerAdditions, loanRepayment, remainingLoan, additionToCoverDeductionLimit, totalDeduction, totalAddition, unpaidDeduction, unpaidDeductionRepayment);
        fillProratedAmountNew(housemaid, payrollMonth, monthlyPaymentRule.isSecondaryMonthlyRule());
    }

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public Boolean getForFinalSettlement() {
        return forFinalSettlement;
    }

    public void setForFinalSettlement(Boolean forFinalSettlement) {
        this.forFinalSettlement = forFinalSettlement;
    }

    public PayrollAccountantTodo getPayrollAccountantTodo() {
        return payrollAccountantTodo;
    }

    public void setPayrollAccountantTodo(PayrollAccountantTodo payrollAccountantTodo) {
        this.payrollAccountantTodo = payrollAccountantTodo;
    }

    public Boolean getTransferred() {
        return transferred;
    }

    public void setTransferred(Boolean transferred) {
        this.transferred = transferred;
        if (this.transferred != null && this.transferred) {
            this.setStatus(housemaid.getRealStatus());
        }
    }

    public Boolean getPayslipGenerated() {
        return payslipGenerated;
    }

    public void setPayslipGenerated(Boolean payslipGenerated) {
        this.payslipGenerated = payslipGenerated;
    }

    public Boolean getPayslipSent() {
        return payslipSent;
    }

    public void setPayslipSent(Boolean payslipSent) {
        this.payslipSent = payslipSent;
    }

    public HousemaidUnpaidStatus getHousemaidUnpaidStatus() {
        return housemaidUnpaidStatus;
    }

    public void setHousemaidUnpaidStatus(HousemaidUnpaidStatus housemaidUnpaidStatus) {
        this.housemaidUnpaidStatus = housemaidUnpaidStatus;
    }

    public Long getOnVacationDays() {
        return onVacationDays;
    }

    public void setOnVacationDays(Long onVacationDays) {
        this.onVacationDays = onVacationDays;
    }

    public Boolean getWillBeIncluded() {
        return willBeIncluded;
    }

    public void setWillBeIncluded(Boolean willBeIncluded) {
        this.willBeIncluded = willBeIncluded;
    }

    public String getPaidOnDate() {
        return paidOnDate;
    }

    public void setPaidOnDate(String paidOnDate) {
        this.paidOnDate = paidOnDate;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public String getEmployeeUniqueId() {
        return employeeUniqueId;
    }

    public void setEmployeeUniqueId(String employeeUniqueId) {
        this.employeeUniqueId = employeeUniqueId;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getEmployeeAccountWithAgent() {
        return employeeAccountWithAgent;
    }

    public void setEmployeeAccountWithAgent(String employeeAccountWithAgent) {
        this.employeeAccountWithAgent = employeeAccountWithAgent;
    }

    public Date getPayStartDate() {
        return payStartDate;
    }

    public void setPayStartDate(Date payStartDate) {
        this.payStartDate = payStartDate;
    }

    public Date getPayEndDate() {
        return payEndDate;
    }

    public void setPayEndDate(Date payEndDate) {
        this.payEndDate = payEndDate;
    }

    public int getDaysInPeriod() {
        return daysInPeriod;
    }

    public void setDaysInPeriod(int daysInPeriod) {
        this.daysInPeriod = daysInPeriod;
    }

    public Double getTotalSalary() {
        return NumberFormatter.twoDecimalPoints(totalSalary);
    }

    public void setTotalSalary(Double totalSalary) {
        this.totalSalary = totalSalary;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getDestinationOfTransfer() {
        return destinationOfTransfer;
    }

    public void setDestinationOfTransfer(String destinationOfTransfer) {
        this.destinationOfTransfer = destinationOfTransfer;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getStatus() {
        return status != null ? status :
                housemaid.getRealStatus();
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getApartment() {
        return apartment;
    }

    public void setApartment(String apartment) {
        this.apartment = apartment;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public Date getPayrollMonth() {
        return payrollMonth;
    }

    public void setPayrollMonth(Date payrollMonth) {
        this.payrollMonth = payrollMonth;
    }

    // Required for payslips
    private Double accommodationSalary;
    private Double primarySalary;
    private Double basicSalary;
    private Double monthlyLoan;
    private Double overTime;
    private Double holiday;
    private Double airfareFee;
    private Double foodAllowance;
    private Double housingAllowance;
    private Double startDateDeduction;
    private Double managerAdditions;
    private Double loanRepayment;
    private Double remainingLoan;
    private Double unpaidDeductionRepayment;
    private Double unpaidDeduction;
    private Double additionToCoverDeductionLimit;
    private Double totalDeduction;
    private Double totalAddition;

    private void fillPayslipFields(Housemaid housemaid, Double startDateDeduction, Double managerAdditions, Double loanRepayment, Double remainingLoan, Double additionToCoverDeductionLimit,
                                   Double totalDeduction, Double totalAddition, Double unpaidDeduction, Double unpaidDeductionRepayment) {
        this.accommodationSalary = valueOrZero(housemaid.getAccommodationSalary());
        this.primarySalary = valueOrZero(housemaid.getPrimarySalary());
        this.monthlyLoan = valueOrZero(housemaid.getMonthlyLoan());
        this.overTime = valueOrZero(housemaid.getOverTime());
        this.holiday = valueOrZero(housemaid.getHoliday());
        this.airfareFee = valueOrZero(housemaid.getAirfareFee());
        this.basicSalary = valueOrZero(housemaid.getBasicSalary());
        this.foodAllowance = valueOrZero(housemaid.getFoodAllowance());
        this.housingAllowance = valueOrZero(housemaid.getHousingAllowance());
        this.startDateDeduction = valueOrZero(startDateDeduction);
        this.managerAdditions = valueOrZero(managerAdditions);
        this.loanRepayment = valueOrZero(loanRepayment);
        this.remainingLoan = valueOrZero(remainingLoan);
        this.additionToCoverDeductionLimit = valueOrZero(additionToCoverDeductionLimit);
        this.totalDeduction = valueOrZero(totalDeduction);
        this.totalAddition = valueOrZero(totalAddition);
        this.unpaidDeduction = valueOrZero(unpaidDeduction);
        this.unpaidDeductionRepayment = valueOrZero(unpaidDeductionRepayment);
    }

    public Double valueOrZero(Double value) {
        return value == null ? 0d : value;
    }

    public Double getAccommodationSalary() {
        return accommodationSalary;
    }

    public void setAccommodationSalary(Double accommodationSalary) {
        this.accommodationSalary = accommodationSalary;
    }

    public Double getPrimarySalary() {
        return primarySalary;
    }

    public void setPrimarySalary(Double primarySalary) {
        this.primarySalary = primarySalary;
    }

    public Double getBasicSalary() {
        return basicSalary;
    }

    public void setBasicSalary(Double basicSalary) {
        this.basicSalary = basicSalary;
    }

    public Double getMonthlyLoan() {
        return monthlyLoan;
    }

    public void setMonthlyLoan(Double monthlyLoan) {
        this.monthlyLoan = monthlyLoan;
    }

    public Double getOverTime() {
        return overTime;
    }

    public void setOverTime(Double overTime) {
        this.overTime = overTime;
    }

    public Double getHoliday() {
        return holiday;
    }

    public void setHoliday(Double holiday) {
        this.holiday = holiday;
    }

    public Double getAirfareFee() {
        return airfareFee;
    }

    public void setAirfareFee(Double airfareFee) {
        this.airfareFee = airfareFee;
    }

    public Double getFoodAllowance() {
        return foodAllowance;
    }

    public void setFoodAllowance(Double foodAllowance) {
        this.foodAllowance = foodAllowance;
    }

    public Double getHousingAllowance() {
        return housingAllowance;
    }

    public void setHousingAllowance(Double housingAllowance) {
        this.housingAllowance = housingAllowance;
    }

    public Double getStartDateDeduction() {
        return startDateDeduction;
    }

    public void setStartDateDeduction(Double startDateDeduction) {
        this.startDateDeduction = startDateDeduction;
    }

    public Double getManagerAdditions() {
        return managerAdditions == null ? 0d : managerAdditions;
    }

    public void setManagerAdditions(Double managerAdditions) {
        this.managerAdditions = managerAdditions;
    }

    public Double getLoanRepayment() {
        return loanRepayment;
    }

    public void setLoanRepayment(Double loanRepayment) {
        this.loanRepayment = loanRepayment;
    }

    public Double getRemainingLoan() {
        return remainingLoan;
    }

    public void setRemainingLoan(Double remainingLoan) {
        this.remainingLoan = remainingLoan;
    }

    public Double getUnpaidDeductionRepayment() {
        return unpaidDeductionRepayment;
    }

    public void setUnpaidDeductionRepayment(Double unpaidDeductionRepayment) {
        this.unpaidDeductionRepayment = unpaidDeductionRepayment;
    }

    public Double getUnpaidDeduction() {
        return unpaidDeduction;
    }

    public void setUnpaidDeduction(Double unpaidDeduction) {
        this.unpaidDeduction = unpaidDeduction;
    }

    public Double getAdditionToCoverDeductionLimit() {
        return additionToCoverDeductionLimit;
    }

    public void setAdditionToCoverDeductionLimit(Double additionToCoverDeductionLimit) {
        this.additionToCoverDeductionLimit = additionToCoverDeductionLimit;
    }

    public HousemaidPayrollBean getHousemaidPayrollBean() {
        return housemaidPayrollBean;
    }

    public void setHousemaidPayrollBean(HousemaidPayrollBean housemaidPayrollBean) {
        this.housemaidPayrollBean = housemaidPayrollBean;
    }

    // Prorated amounts
    private int groupOneDays;
    private int groupTwoDays;
    private int groupThreeDays;

    private int groupFourDays;

    private int groupFiveDays;

    private int groupSixDays;

    private Double totalProRatedSalary; // grp1
    private Double mohreProRatedSalary; // grp2

    private Double vacationSalary; // grp4

    private Double totalLiveOutProRatedSalary; // grp5

    private Double mohreLiveOutProRatedSalary; // grp6


    private Double proratedCashAdvance;
    private Double totalEarnings;

    public int getGroupFourDays() {
        return groupFourDays;
    }

    public void setGroupFourDays(int groupFourDays) {
        this.groupFourDays = groupFourDays;
    }

    public Double getVacationSalary() {
        return vacationSalary;
    }

    public void setVacationSalary(Double vacationSalary) {
        this.vacationSalary = vacationSalary;
    }

    public int getGroupFiveDays() {
        return groupFiveDays;
    }

    public void setGroupFiveDays(int groupFiveDays) {
        this.groupFiveDays = groupFiveDays;
    }

    public int getGroupSixDays() {
        return groupSixDays;
    }

    public void setGroupSixDays(int groupSixDays) {
        this.groupSixDays = groupSixDays;
    }

    public Double getTotalLiveOutProRatedSalary() {
        return totalLiveOutProRatedSalary;
    }

    public void setTotalLiveOutProRatedSalary(Double totalLiveOutProRatedSalary) {
        this.totalLiveOutProRatedSalary = totalLiveOutProRatedSalary;
    }

    public Double getMohreLiveOutProRatedSalary() {
        return mohreLiveOutProRatedSalary;
    }

    public void setMohreLiveOutProRatedSalary(Double mohreLiveOutProRatedSalary) {
        this.mohreLiveOutProRatedSalary = mohreLiveOutProRatedSalary;
    }

    private void fillProratedAmount(Housemaid housemaid, HousemaidPayrollInitializer initializer) {

        LocalDate payrollMonth = new LocalDate(HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer().getMonthlyPaymentRule().getPayrollMonth());

        Map<String, Object> breakDown = Setup.getApplicationContext().getBean(ProRatedSalariesService.class).getSalaryBreakDown(housemaid,
                payrollMonth,
                new java.sql.Date(payrollMonth.withDayOfMonth(1).toDate().getTime()),
                new java.sql.Date(payrollMonth.dayOfMonth().withMaximumValue().toDate().getTime()),
                HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer().getGroup1Days(housemaid.getId()),
                initializer.getPreviousVacationDays(housemaid.getId()),
                initializer.getVacationDays(housemaid.getId()),
                initializer.getLastVacationDatesMap());

        this.groupOneDays = (Integer)breakDown.get("group1Days");
        this.groupTwoDays = (Integer)breakDown.get("group2Days");
        this.groupThreeDays = (Integer)breakDown.get("group3Days");
        this.groupFourDays = (Integer)breakDown.get("group4Days");
        this.groupFiveDays = (Integer) breakDown.get("group5Days");
        this.groupSixDays = (Integer) breakDown.get("group6Days");
        this.totalProRatedSalary = 1d * Math.round((Double)breakDown.get("group1Salary"));
        this.mohreProRatedSalary = 1d * Math.round((Double)breakDown.get("group2Salary"));
        this.vacationSalary = 1d * Math.round((Double)breakDown.get("group4Salary"));
        this.totalLiveOutProRatedSalary = 1d * Math.round((Double)breakDown.get("group5Salary"));
        this.mohreLiveOutProRatedSalary = 1d * Math.round((Double)breakDown.get("group6Salary"));
        this.totalEarnings = 1d * Math.round(this.totalProRatedSalary + this.mohreProRatedSalary + this.vacationSalary + this.totalLiveOutProRatedSalary + this.mohreLiveOutProRatedSalary);
        this.proratedCashAdvance = 1d * Math.round((Double)breakDown.get("proratedCashAdvance"));

    }

    private void fillProratedAmountNew(Housemaid housemaid, Date payrollMonth, Boolean isSecondary) {
        if (isSecondary == null || !isSecondary) {
            PayrollGenerationHelperService payrollGenerationHelperService = Setup.getApplicationContext().getBean(PayrollGenerationHelperService.class);

            LocalDate payrollMonthL = new LocalDate(payrollMonth);

            Map<String, Object> breakDown = payrollGenerationHelperService.getSalaryBreakDownForHousemaid(housemaid, payrollMonthL);

            this.groupOneDays = (Integer) breakDown.get("group1Days");
            this.groupTwoDays = (Integer) breakDown.get("group2Days");
            this.groupThreeDays = (Integer) breakDown.get("group3Days");
            this.groupFourDays = (Integer) breakDown.get("group4Days");
            this.groupFiveDays = (Integer) breakDown.get("group5Days");
            this.groupSixDays = (Integer) breakDown.get("group6Days");
            this.totalProRatedSalary = 1d * Math.round((Double) breakDown.get("group1Salary"));
            this.mohreProRatedSalary = 1d * Math.round((Double) breakDown.get("group2Salary"));
            this.vacationSalary = 1d * Math.round((Double) breakDown.get("group4Salary"));
            this.totalLiveOutProRatedSalary = 1d * Math.round((Double) breakDown.get("group5Salary"));
            this.mohreLiveOutProRatedSalary = 1d * Math.round((Double) breakDown.get("group6Salary"));
            this.totalEarnings = 1d * Math.round(this.totalProRatedSalary + this.mohreProRatedSalary + this.vacationSalary + this.totalLiveOutProRatedSalary + this.mohreLiveOutProRatedSalary);
            this.proratedCashAdvance = 1d * Math.round((Double) breakDown.get("proratedCashAdvance"));
            this.onVacationDays = (long) breakDown.getOrDefault("vacationDays", 0L);
        } else {
            this.groupOneDays = 0;
            this.groupTwoDays = 0;
            this.groupThreeDays = 0;
            this.groupFourDays = 0;
            this.groupFiveDays = 0;
            this.groupSixDays = 0;
            this.totalProRatedSalary = 0.0;
            this.mohreProRatedSalary = 0.0;
            this.vacationSalary = 0.0;
            this.totalLiveOutProRatedSalary = 0.0;
            this.mohreLiveOutProRatedSalary = 0.0;
            this.totalEarnings = 0.0;
            this.proratedCashAdvance = 0.0;
            this.onVacationDays = 0L;
        }
    }

    public Double getTotalDeduction() {
        if (totalDeduction == null)
            totalDeduction = 0.0;
        return totalDeduction;
    }

    public void setTotalDeduction(Double totalDeduction) {
        this.totalDeduction = totalDeduction;
    }

    public Double getTotalAddition() {
        return totalAddition == null ? 0d : totalAddition;
    }

    public void setTotalAddition(Double totalAddition) {
        this.totalAddition = totalAddition;
    }

    public int getGroupOneDays() {
        return groupOneDays;
    }

    public void setGroupOneDays(int groupOneDays) {
        this.groupOneDays = groupOneDays;
    }

    public int getGroupTwoDays() {
        return groupTwoDays;
    }

    public void setGroupTwoDays(int groupTwoDays) {
        this.groupTwoDays = groupTwoDays;
    }

    public int getGroupThreeDays() {
        return groupThreeDays;
    }

    public void setGroupThreeDays(int groupThreeDays) {
        this.groupThreeDays = groupThreeDays;
    }

    public Double getTotalProRatedSalary() {
        return totalProRatedSalary;
    }

    public void setTotalProRatedSalary(Double totalProRatedSalary) {
        this.totalProRatedSalary = totalProRatedSalary;
    }

    public Double getMohreProRatedSalary() {
        return mohreProRatedSalary;
    }

    public void setMohreProRatedSalary(Double mohreProRatedSalary) {
        this.mohreProRatedSalary = mohreProRatedSalary;
    }

    public Double getProratedCashAdvance() {
        return proratedCashAdvance;
    }

    public void setProratedCashAdvance(Double proratedCashAdvance) {
        this.proratedCashAdvance = proratedCashAdvance;
    }

    public Double getTotalEarnings() {
        if (totalEarnings == null)
            totalEarnings = 0.0;
        return totalEarnings;
    }

    public void setTotalEarnings(Double totalEarnings) {
        this.totalEarnings = totalEarnings;
    }

    public String getPaidOnStatus() {
        Boolean manuallyExcluded = false;
        if (this.housemaidUnpaidStatus != null && this.housemaidUnpaidStatus.equals(HousemaidUnpaidStatus.UNPAID_VISA_PAYMENT)
                && !transferred
                && (paidOnStatus == null || paidOnStatus.contains("No expected release date")
                || paidOnStatus.equals("Client's payment wasn't received"))
                && monthlyPaymentRule != null) {
            Map<String, Object> resultMap = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getReceivedPaymentForHousemaid(monthlyPaymentRule, housemaid);
            if (resultMap != null) { // check if resultMap is not null then there is a received payment but we need to check cases
                Integer cases = (Integer) resultMap.get("cases");
                Payment payment = (Payment) resultMap.get("payment");
                java.util.Date paymentReceiveDate = payment.getDateChangedToReceived();
                manuallyExcluded = paidOnStatus != null && paidOnStatus.contains("Manually Excluded");
                if (cases <= 2) {
                    Date expectedReleaseDate = Setup.getApplicationContext().getBean(YayaAppContentHelper.class).getNextSalaryDateAsDate();
                    paidOnStatus = "Expected release date is " + DateUtil.formatDaySuffixMonth(expectedReleaseDate) + " / Client's payment received on " + DateUtil.formatDaySuffixMonth(paymentReceiveDate) + ".";
                } else
                    paidOnStatus = "No expected release date / Client's payment received on " + DateUtil.formatDaySuffixMonth(paymentReceiveDate) + ", but the amount of the payment is less than the maid's salary.";
            } else
                paidOnStatus = "No expected release date. Client's payment has not been received.";
        }

        if (manuallyExcluded)
            paidOnStatus = "Manually Excluded / " + paidOnStatus;
        return paidOnStatus;
    }

    public void setPaidOnStatus(String paidOnStatus) {
        this.paidOnStatus = paidOnStatus;
    }

    public void setPaidOnStatus() {
        if (this.housemaidUnpaidStatus != null) {
            if (housemaidUnpaidStatus.equals(HousemaidUnpaidStatus.UNPAID_VISA_PAYMENT))
                paidOnStatus = "No expected release date. Client's payment has not been received.";
            else if (housemaidUnpaidStatus.equals(HousemaidUnpaidStatus.EXCLUDED_FROM_PROFILE))
                paidOnStatus = "Excluded Manually";
            else
                paidOnStatus = "Excluded due to her status (" + StringHelper.enumToCapitalizedFirstLetter(housemaidUnpaidStatus.toString() + ")");
        }
    }

    public HousemaidPayrollLog getMaidParentLog() {
        return maidParentLog;
    }

    public void setMaidParentLog(HousemaidPayrollLog maidParentLog) {
        this.maidParentLog = maidParentLog;
    }

    public HousemaidPayrollLogStatus getLogStatus() {
        return logStatus;
    }

    public void setLogStatus(HousemaidPayrollLogStatus logStatus) {
        this.logStatus = logStatus;
    }

    public MonthlyPaymentRule getMonthlyPaymentRule() {
        return monthlyPaymentRule;
    }

    public void setMonthlyPaymentRule(MonthlyPaymentRule monthlyPaymentRule) {
        this.monthlyPaymentRule = monthlyPaymentRule;
    }

    public PayrollAuditTodo getPayrollAuditTodo() {
        return payrollAuditTodo;
    }

    public void setPayrollAuditTodo(PayrollAuditTodo payrollAuditTodo) {
        this.payrollAuditTodo = payrollAuditTodo;
    }

    public enum HousemaidPayrollLogStatus implements LabelValueEnum {
        PENDING("Pending"),
        FINAL("Final");

        private final String label;

        HousemaidPayrollLogStatus(String label) {
            this.label = label;
        }

        @Override
        public String getLabel() {
            return label;
        }
    }

    @JsonIgnore
    public String getFormattedPayrollMonth() {
        return DateUtil.formatSimpleMonthYear(payrollMonth);
    }

}
