package com.magnamedia.entity.payroll.logging;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.serialize.IdSerializer;
import com.magnamedia.entity.*;
import com.magnamedia.entity.serializer.SalaryAmountSerializer;
import com.magnamedia.extra.LabelValueEnum;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.repository.NewVisaRequestRepository;
import com.magnamedia.module.type.OfficeStaffType;
import com.magnamedia.service.OfficeStaffUpdateService;
import com.magnamedia.workflow.type.PayrollAccountantTodoManagerAction;
import org.hibernate.annotations.ColumnDefault;
import org.joda.time.LocalDate;

import javax.persistence.*;
import java.util.Date;

@Entity @org.hibernate.envers.Audited
public class OfficeStaffPayrollLog extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private OfficeStaff officeStaff;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    MonthlyPaymentRule monthlyPaymentRule;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    PayrollAccountantTodo payrollAccountantTodo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    PayrollAuditTodo payrollAuditTodo;

    @Column(columnDefinition = "boolean default false")
    private Boolean transferred = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean singleOfficeStaff = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean forEmployeeLoan = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean willBeIncluded = false;

    @Column
    private String paidOnDate;

    @Enumerated(EnumType.STRING)
    private OfficeStaffPayrollLogStatus logStatus = OfficeStaffPayrollLogStatus.PENDING;

    @Enumerated(EnumType.STRING)
    private PayrollAccountantTodoManagerAction managerAction = PayrollAccountantTodoManagerAction.PENDING;
    @Enumerated(EnumType.STRING)
    private PayrollAccountantTodoManagerAction ceoAction = PayrollAccountantTodoManagerAction.PENDING;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User ceoActionBy;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private OfficeStaffPayrollBean officeStaffPayrollBean;

    @Column(columnDefinition = "boolean default false")
    private Boolean insideUAE;

    // Required for WPS file
    private String sn;
    private String recordType;
    private String employeeUniqueId;
    private String employeeName;
    private String agentId;
    private String employeeAccountWithAgent;
    private java.sql.Date payrollMonth;
    private Date payStartDate;
    private Date payEndDate;
    private int daysInPeriod;
    @JsonSerialize(using = SalaryAmountSerializer.class)
    private Double totalSalary;
    @JsonSerialize(using = SalaryAmountSerializer.class)
    private Double previouslyUnpaidSalaries;

    // Required for international transfer file
    private String fullNameEnglish;
    private String fullNameArabic;
    @Lob
    private String destinationOfTransfer;
    private String country;
    private String mobileNumber;
    private String currency;
    private String rate;
    @JsonSerialize(using = SalaryAmountSerializer.class)
    private Double amountInAED;
    @JsonSerialize(using = SalaryAmountSerializer.class)
    private Double chargeVAT;
    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column(columnDefinition = "double default 0")
    private Double totalInAED;

    // Required for local transfer file
    private String receiverName;

    // Required for bank transfer
    private String bankName;
    private String iban;
    private String accountHolderName;
    private String accountNumber;
    private String swiftCode;
    @Lob
    private String beneficiaryAddress;

    // Required for pension authority
    String idNumber;
    String routingCode = "*********";
    String routingCodeId = "AE008510";
    String beneficiaryBank = "GPSSA PENSION CONTR. And DISB.";
    Double contributionAmountPercentage = 0d;
    @JsonSerialize(using = SalaryAmountSerializer.class)
    Double contributionAmount;
    Double employeeContributionAmountPercentage = 0d;
    @JsonSerialize(using = SalaryAmountSerializer.class)
    Double employeeContributionAmount;
    Double employerContributionAmountPercentage = 12.5d;
    @JsonSerialize(using = SalaryAmountSerializer.class)
    Double employerContributionAmount;


    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column(columnDefinition = "double default 0")
    private Double staffTotalSalary;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column(columnDefinition = "double default 0")
    private Double staffBasicSalary;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column(columnDefinition = "double default 0")
    private Double staffHousing;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column(columnDefinition = "double default 0")
    private Double staffTransportation;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column(columnDefinition = "double default 0")
    private Double staffFixPension;

    @Lob
    private String markUnpaidNotes;

    @Column
    private String paymentType;

    @Column
    private Double totalSalaryInADCBReport;

    @Column
    private String paymentCurrency;

    @Column
    private String beneficiaryAddress1;

    @Column
    private String beneficiaryAddress2;

    @Column
    private String beneficiaryAddress3;

    @Column
    private String beneficiaryAccount;

    @Column
    private String beneficiaryBankName;

    @Column
    private String beneficiaryBankAddress1;

    @Column
    private String beneficiaryBankAddress2;

    @Column
    private String pensionIBAN;


    @ColumnDefault("0")
    private Double loanRepayment;

    public OfficeStaffPayrollLog() {
    }

    // Used for WPS file
    public OfficeStaffPayrollLog(OfficeStaff officeStaff, String recordType, String employeeName, NewRequest newRequest,
                                 Date payStartDate, Date payEndDate, Double totalSalary, java.sql.Date payrollMonth, Double previouslyUnpaidSalaries) {
        this.officeStaff = officeStaff;
        this.recordType = recordType;
        this.employeeName = employeeName;
        if(newRequest != null) {
            this.employeeUniqueId = newRequest.getEmployeeUniqueId();
            this.agentId = newRequest.getAgentId();
            this.employeeAccountWithAgent = newRequest.getEmployeeAccountWithAgent();
        }
        this.payStartDate = new LocalDate(payrollMonth).withDayOfMonth(1).toDate();
        this.payEndDate = new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate();
        this.daysInPeriod = DateUtil.getDaysBetween(this.payStartDate, this.payEndDate) + 1;
        this.totalSalary = totalSalary == null ? 0d: totalSalary;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());
        this.previouslyUnpaidSalaries = previouslyUnpaidSalaries;
    }

    // Used for WPS file (OVERLOAD)
    public OfficeStaffPayrollLog(OfficeStaff officeStaff, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, String recordType, String employeeName, NewRequest newRequest,
                                 Date payStartDate, Date payEndDate, Double totalSalary, java.sql.Date payrollMonth, Double previouslyUnpaidSalaries, String currency, Double loanRepayment) {
        this.officeStaff = officeStaff;
        if(monthlyPaymentRule != null)
            this.monthlyPaymentRule = monthlyPaymentRule;
        if(auditTodo != null)
            this.payrollAuditTodo = auditTodo;
        if(todo != null)
            this.payrollAccountantTodo = todo;
        this.recordType = recordType;
        this.employeeName = employeeName;
        if(newRequest != null) {
            this.employeeUniqueId = newRequest.getEmployeeUniqueId();
            this.agentId = newRequest.getAgentId();
            this.employeeAccountWithAgent = newRequest.getEmployeeAccountWithAgent();
        }
        this.payStartDate = new LocalDate(payrollMonth).withDayOfMonth(1).toDate();
        this.payEndDate = new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate();
        this.daysInPeriod = DateUtil.getDaysBetween(this.payStartDate, this.payEndDate) + 1;
        this.totalSalary = totalSalary == null ? 0d: totalSalary;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());
        this.previouslyUnpaidSalaries = previouslyUnpaidSalaries;
        // for local transfer
        this.receiverName = employeeName;
        this.destinationOfTransfer = officeStaff.getCountry() != null ? officeStaff.getCountry().getName() : "";
        this.mobileNumber = officeStaff.getPhoneNumber() != null ? officeStaff.getPhoneNumber() : "";
        this.currency = currency;
        this.loanRepayment = loanRepayment;
    }

    // Used for international transfer file
    public OfficeStaffPayrollLog(OfficeStaff officeStaff, String fullNameEnglish, String fullNameArabic, String destinationOfTransfer,
                                 String mobileNumber, Double totalSalary, String currency, java.sql.Date payrollMonth, Double previouslyUnpaidSalaries, String country) {
        this.officeStaff = officeStaff;
        this.employeeName = officeStaff.getName();
        this.fullNameEnglish = fullNameEnglish;
        this.fullNameArabic = fullNameArabic;
        this.destinationOfTransfer = destinationOfTransfer;
        this.mobileNumber = mobileNumber;
        this.totalSalary = totalSalary == null ? 0d: totalSalary;
        this.currency = currency;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());
        this.previouslyUnpaidSalaries = previouslyUnpaidSalaries;
        this.country = country;
    }

    // Used for international transfer file (OVERLOAD)
    public OfficeStaffPayrollLog(OfficeStaff officeStaff, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, String fullNameEnglish, String fullNameArabic, String destinationOfTransfer,
                                 String mobileNumber, Double totalSalary, String currency, java.sql.Date payrollMonth, Double previouslyUnpaidSalaries, String country, Double loanRepayment) {
        this.officeStaff = officeStaff;
        if(monthlyPaymentRule != null)
            this.monthlyPaymentRule = monthlyPaymentRule;
        if(auditTodo != null)
            this.payrollAuditTodo = auditTodo;
        if(todo != null)
            this.payrollAccountantTodo = todo;
        this.employeeName = officeStaff.getName();
        this.fullNameEnglish = fullNameEnglish;
        this.fullNameArabic = fullNameArabic;
        this.destinationOfTransfer = destinationOfTransfer;
        this.mobileNumber = mobileNumber;
        this.totalSalary = totalSalary == null ? 0d: totalSalary;
        this.currency = currency;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());
        this.previouslyUnpaidSalaries = previouslyUnpaidSalaries;
        this.country = country;
        this.loanRepayment = loanRepayment;
    }

    //Used for local transfer file
    public OfficeStaffPayrollLog(OfficeStaff officeStaff, String receiverName, String destinationOfTransfer,
                                 String mobileNumber, Double totalSalary, java.sql.Date payrollMonth, Double previouslyUnpaidSalaries) {
        this.officeStaff = officeStaff;
        this.receiverName = receiverName;
        this.destinationOfTransfer = destinationOfTransfer;
        this.mobileNumber = mobileNumber;
        this.totalSalary = totalSalary == null ? 0d: totalSalary;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());
        this.previouslyUnpaidSalaries = previouslyUnpaidSalaries;
    }

    //Used for local transfer file (OVERLOAD)
    public OfficeStaffPayrollLog(OfficeStaff officeStaff, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, String receiverName, String destinationOfTransfer,
                                 String mobileNumber, Double totalSalary, java.sql.Date payrollMonth, Double previouslyUnpaidSalaries,String currency, Double loanRepayment) {
        this.officeStaff = officeStaff;
        if(monthlyPaymentRule != null)
            this.monthlyPaymentRule = monthlyPaymentRule;
        if(auditTodo != null)
            this.payrollAuditTodo = auditTodo;
        if(todo != null)
            this.payrollAccountantTodo = todo;
        this.receiverName = receiverName;
        this.destinationOfTransfer = destinationOfTransfer;
        this.mobileNumber = mobileNumber;
        this.totalSalary = totalSalary == null ? 0d: totalSalary;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());
        this.previouslyUnpaidSalaries = previouslyUnpaidSalaries;
        this.currency = currency;
        this.loanRepayment = loanRepayment;
    }

    // Used for bank transfer
    public OfficeStaffPayrollLog(OfficeStaff officeStaff, String fullNameEnglish, String fullNameArabic, String destinationOfTransfer, String mobileNumber, Double totalSalary, String currency,
                                 String iban, String accountHolderName, String accountNumber, String bankName, String swiftCode, String beneficiaryAddress, java.sql.Date payrollMonth, Double previouslyUnpaidSalaries, String country) {
        this.officeStaff = officeStaff;
        this.employeeName = officeStaff.getName();
        this.fullNameEnglish = fullNameEnglish;
        this.fullNameArabic = fullNameArabic;
        this.destinationOfTransfer = destinationOfTransfer;
        this.mobileNumber = mobileNumber;
        this.totalSalary = totalSalary == null ? 0d: totalSalary;
        this.currency = currency;
        if ("USD".equals(currency)
                && officeStaff.getSelectedTransferDestination() != null && officeStaff.getSelectedTransferDestination().getIban() != null && officeStaff.getSelectedTransferDestination().getIban().toLowerCase().startsWith("ae")) {
            String exchangeRateS = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_USD_TO_AED_EXCHANGE_RATE);
            Double exchangeRate = exchangeRateS == null ? 0.0 : Double.parseDouble(exchangeRateS);
            this.totalInAED = exchangeRate * this.totalSalary;
        }
        this.iban = iban;
        this.accountHolderName = accountHolderName;
        this.accountNumber = accountNumber;
        this.bankName = bankName;
        this.swiftCode = swiftCode;
        this.country = country;
        this.beneficiaryAddress = beneficiaryAddress;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());
        this.previouslyUnpaidSalaries = previouslyUnpaidSalaries;
    }

    // Used for bank transfer & International(OVERLAOD)
    public OfficeStaffPayrollLog(OfficeStaff officeStaff, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, String fullNameEnglish, String fullNameArabic, String destinationOfTransfer, String mobileNumber, Double totalSalary, String currency,
                                 String iban, String accountHolderName, String accountNumber, String bankName, String swiftCode, String beneficiaryAddress, java.sql.Date payrollMonth, Double previouslyUnpaidSalaries, String country, Double loanRepayment) {
        this.officeStaff = officeStaff;
        if (monthlyPaymentRule != null)
            this.monthlyPaymentRule = monthlyPaymentRule;
        if (auditTodo != null)
            this.payrollAuditTodo = auditTodo;
        if (todo != null)
            this.payrollAccountantTodo = todo;
        this.employeeName = officeStaff.getName();
        this.fullNameEnglish = fullNameEnglish;
        this.fullNameArabic = fullNameArabic;
        this.destinationOfTransfer = destinationOfTransfer;
        this.mobileNumber = mobileNumber;
        this.totalSalary = totalSalary == null ? 0d : totalSalary;
        this.currency = currency;
        if ("USD".equals(currency)
                && officeStaff.getSelectedTransferDestination() != null && officeStaff.getSelectedTransferDestination().getIban() != null && officeStaff.getSelectedTransferDestination().getIban().toLowerCase().startsWith("ae")) {
            String exchangeRateS = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_USD_TO_AED_EXCHANGE_RATE);
            Double exchangeRate = exchangeRateS == null ? 0.0 : Double.parseDouble(exchangeRateS);
            this.totalInAED = exchangeRate * this.totalSalary;
        }
        this.iban = iban;
        this.accountHolderName = accountHolderName;
        this.accountNumber = accountNumber;
        this.bankName = bankName;
        this.swiftCode = swiftCode;
        this.country = country;
        this.beneficiaryAddress = beneficiaryAddress;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());
        this.previouslyUnpaidSalaries = previouslyUnpaidSalaries;
        this.loanRepayment = loanRepayment;

        //PAY-1474
        TransferDestination staffDestination = null;
        if (OfficeStaffType.OVERSEAS_STAFF.equals(officeStaff.getEmployeeType()) && officeStaff.getSelectedTransferDestination() != null)
            staffDestination = officeStaff.getSelectedTransferDestination();
        else
            staffDestination = OfficeStaffType.OVERSEAS_STAFF.equals(officeStaff.getEmployeeType()) && officeStaff.getOfficeStaffCandidate() != null ? officeStaff.getOfficeStaffCandidate().getSelectedTransferDestination() : null;

        if (staffDestination != null) {
            this.beneficiaryBankName = staffDestination.getBankName();
            this.insideUAE = staffDestination.getInsideUAE();
            if (staffDestination.getInsideUAE()) {
                this.totalSalaryInADCBReport = this.totalSalary * 3.6735;
                this.paymentCurrency = "AED";
                this.beneficiaryAddress1 = "UAE";
                this.beneficiaryBankAddress1 = "UAE";
                if ("Abu Dhabi Commercial Bank".equalsIgnoreCase(staffDestination.getBankName())) {
                    this.paymentType = "IFT";
                } else {
                    this.paymentType = "EFD";
                }
            } else {
                this.totalSalaryInADCBReport = this.totalSalary;
                this.paymentType = "EFI";
                this.paymentCurrency = "USD";
                this.beneficiaryBankAddress1 = staffDestination.getBranchCountryName();
                this.beneficiaryBankAddress2 = staffDestination.getBranchCityName();
                if (staffDestination.getCountry() != null && staffDestination.getCity() != null) {
                    this.beneficiaryAddress1 = staffDestination.getCountry().getName() + ", " + staffDestination.getCity().getName();
                    //officeStaff.getCountry().getTag()
                    this.beneficiaryAddress3 = "/" + Setup.getApplicationContext().getBean(OfficeStaffUpdateService.class).getIsoCode(staffDestination.getCountry()) + "/" + staffDestination.getCity().getName();
                }
                this.beneficiaryAddress2 = staffDestination.getFullAddress();
            }
        }

        if (iban != null && !iban.isEmpty()) {
            this.beneficiaryAccount = iban;
        } else {
            this.beneficiaryAccount = accountNumber;
        }

    }

    // Used for pension authority
    public OfficeStaffPayrollLog(OfficeStaff officeStaff, String employeeName, Double totalSalary, String currency, java.sql.Date payrollMonth, Double employeeContributionAmountPercentage, Double contributionAmountPercentage, Double totalContributionAmount) {
        this.officeStaff = officeStaff;
        this.idNumber = officeStaff.getEidNumber();
        this.employeeName = employeeName;
        this.totalSalary = totalSalary == null ? 0d: totalSalary;
        this.currency = currency;

        //employee
        this.employeeContributionAmountPercentage = employeeContributionAmountPercentage;
        this.employeeContributionAmount = (this.totalSalary * this.employeeContributionAmountPercentage) / 100d;
        this.employeeContributionAmount = Math.round(this.employeeContributionAmount * 100) / 100.0;

        //Employer
        this.employerContributionAmount = (this.totalSalary * this.employerContributionAmountPercentage) / 100d;
        this.employerContributionAmount = Math.round(this.employerContributionAmount * 100) / 100.0;

        //total
        this.contributionAmountPercentage = contributionAmountPercentage;
        //if the office staff has a fixed pension amount then use it and do not calculate anything
        this.contributionAmount =totalContributionAmount;
        this.contributionAmount = Math.round(this.contributionAmount * 100) / 100.0;

        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());

        // PAY-1159
        this.staffTotalSalary = officeStaff.getSalary();
        this.staffBasicSalary = officeStaff.getBasicSalary();
        this.staffHousing = officeStaff.getHousingAllowance();
        this.staffTransportation = officeStaff.getTrasnportation();
        this.staffFixPension = officeStaff.getFixedPensionAmount();
        this.pensionIBAN = officeStaff.getPensionIBAN();

    }

    public OfficeStaff getOfficeStaff() {
        return officeStaff;
    }

    public void setOfficeStaff(OfficeStaff officeStaff) {
        this.officeStaff = officeStaff;
    }

    public PayrollAccountantTodo getPayrollAccountantTodo() {
        return payrollAccountantTodo;
    }

    public void setPayrollAccountantTodo(PayrollAccountantTodo payrollAccountantTodo) {
        this.payrollAccountantTodo = payrollAccountantTodo;
    }

    public Boolean getTransferred() {
        return transferred;
    }

    public void setTransferred(Boolean transferred) {
        this.transferred = transferred;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public String getEmployeeUniqueId() {
        return employeeUniqueId;
    }

    public void setEmployeeUniqueId(String employeeUniqueId) {
        this.employeeUniqueId = employeeUniqueId;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getEmployeeAccountWithAgent() {
        return employeeAccountWithAgent;
    }

    public void setEmployeeAccountWithAgent(String employeeAccountWithAgent) {
        this.employeeAccountWithAgent = employeeAccountWithAgent;
    }

    public Date getPayStartDate() {
        return payStartDate;
    }

    public void setPayStartDate(Date payStartDate) {
        this.payStartDate = payStartDate;
    }

    public Date getPayEndDate() {
        return payEndDate;
    }

    public void setPayEndDate(Date payEndDate) {
        this.payEndDate = payEndDate;
    }

    public int getDaysInPeriod() {
        return daysInPeriod;
    }

    public void setDaysInPeriod(int daysInPeriod) {
        this.daysInPeriod = daysInPeriod;
    }

    public Double getTotalSalary() {
        return NumberFormatter.twoDecimalPoints(totalSalary);
    }

    public void setTotalSalary(Double totalSalary) {
        this.totalSalary = totalSalary;
    }

    public String getFullNameEnglish() {
        return fullNameEnglish;
    }

    public void setFullNameEnglish(String fullNameEnglish) {
        this.fullNameEnglish = fullNameEnglish;
    }

    public String getFullNameArabic() {
        return fullNameArabic;
    }

    public void setFullNameArabic(String fullNameArabic) {
        this.fullNameArabic = fullNameArabic;
    }

    public String getDestinationOfTransfer() {
        return destinationOfTransfer;
    }

    public void setDestinationOfTransfer(String destinationOfTransfer) {
        this.destinationOfTransfer = destinationOfTransfer;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public Double getAmountInAED() {
        return NumberFormatter.twoDecimalPoints(amountInAED);
    }

    public void setAmountInAED(Double amountInAED) {
        this.amountInAED = amountInAED;
    }

    public Double getChargeVAT() {
        return NumberFormatter.twoDecimalPoints(chargeVAT);
    }

    public void setChargeVAT(Double chargeVAT) {
        this.chargeVAT = chargeVAT;
    }

    public Double getTotalInAED() {
        return NumberFormatter.threeDecimalPoints(totalInAED);
    }

    public void setTotalInAED(Double totalInAED) {
        this.totalInAED = totalInAED;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public String getAccountHolderName() {
        return accountHolderName;
    }

    public void setAccountHolderName(String accountHolderName) {
        this.accountHolderName = accountHolderName;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getSwiftCode() {
        return swiftCode;
    }

    public void setSwiftCode(String swiftCode) {
        this.swiftCode = swiftCode;
    }

    public String getBeneficiaryAddress() {
        return beneficiaryAddress;
    }

    public void setBeneficiaryAddress(String beneficiaryAddress) {
        this.beneficiaryAddress = beneficiaryAddress;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }

    public String getRoutingCode() {
        return routingCode;
    }

    public void setRoutingCode(String routingCode) {
        this.routingCode = routingCode;
    }

    public String getRoutingCodeId() {
        return routingCodeId;
    }

    public void setRoutingCodeId(String routingCodeId) {
        this.routingCodeId = routingCodeId;
    }

    public String getBeneficiaryBank() {
        return beneficiaryBank;
    }

    public void setBeneficiaryBank(String beneficiaryBank) {
        this.beneficiaryBank = beneficiaryBank;
    }

    public Double getContributionAmountPercentage() {
        return NumberFormatter.twoDecimalPoints(contributionAmountPercentage);
    }

    public void setContributionAmountPercentage(Double contributionAmountPercentage) {
        this.contributionAmountPercentage = contributionAmountPercentage;
    }

    public Double getContributionAmount() {
        return NumberFormatter.twoDecimalPoints(contributionAmount);
    }

    public void setContributionAmount(Double contributionAmount) {
        this.contributionAmount = contributionAmount;
    }

    public Double getEmployeeContributionAmountPercentage() {
        return NumberFormatter.twoDecimalPoints(employeeContributionAmountPercentage);
    }

    public void setEmployeeContributionAmountPercentage(Double employeeContributionAmountPercentage) {
        this.employeeContributionAmountPercentage = employeeContributionAmountPercentage;
    }

    public Double getEmployeeContributionAmount() {
        return NumberFormatter.twoDecimalPoints(employeeContributionAmount);
    }

    public void setEmployeeContributionAmount(Double employeeContributionAmount) {
        this.employeeContributionAmount = employeeContributionAmount;
    }

    public Double getEmployerContributionAmountPercentage() {
        return NumberFormatter.twoDecimalPoints(employerContributionAmountPercentage);
    }

    public void setEmployerContributionAmountPercentage(Double employerContributionAmountPercentage) {
        this.employerContributionAmountPercentage = employerContributionAmountPercentage;
    }

    public Double getEmployerContributionAmount() {
        return NumberFormatter.twoDecimalPoints(employerContributionAmount);
    }

    public void setEmployerContributionAmount(Double employerContributionAmount) {
        this.employerContributionAmount = employerContributionAmount;
    }

    public java.sql.Date getPayrollMonth() {
        return payrollMonth != null ? payrollMonth : new java.sql.Date(this.getCreationDate().getTime());
    }

    public void setPayrollMonth(java.sql.Date payrollMonth) {
        this.payrollMonth = payrollMonth;
    }

    public Boolean getSingleOfficeStaff() {
        return singleOfficeStaff;
    }

    public void setSingleOfficeStaff(Boolean singleOfficeStaff) {
        this.singleOfficeStaff = singleOfficeStaff;
    }

    public Boolean getForEmployeeLoan() {
        return forEmployeeLoan;
    }

    public void setForEmployeeLoan(Boolean forEmployeeLoan) {
        this.forEmployeeLoan = forEmployeeLoan;
    }

    public Boolean getWillBeIncluded() {
        return willBeIncluded;
    }

    public void setWillBeIncluded(Boolean willBeIncluded) {
        this.willBeIncluded = willBeIncluded;
    }

    public Double getPreviouslyUnpaidSalaries() {
        return previouslyUnpaidSalaries;
    }

    public void setPreviouslyUnpaidSalaries(Double previouslyUnpaidSalaries) {
        this.previouslyUnpaidSalaries = previouslyUnpaidSalaries;
    }

    public String getPaidOnDate() {
        return paidOnDate;
    }

    public void setPaidOnDate(String paidOnDate) {
        this.paidOnDate = paidOnDate;
    }

    public PayrollAccountantTodoManagerAction getManagerAction() {
        return managerAction;
    }

    public void setManagerAction(PayrollAccountantTodoManagerAction managerAction) {
        this.managerAction = managerAction;
    }

    public PayrollAccountantTodoManagerAction getCeoAction() {
        return ceoAction;
    }

    public void setCeoAction(PayrollAccountantTodoManagerAction ceoAction) {
        this.ceoAction = ceoAction;
    }

    public User getCeoActionBy() {
        return ceoActionBy;
    }

    public void setCeoActionBy(User ceoActionBy) {
        this.ceoActionBy = ceoActionBy;
    }

    @JsonIgnore
    public String getAmount(){
        return totalSalary != null ? NumberFormatter.formatNumber(totalSalary) : "0";
    }

    @JsonIgnore
    public String getBeneficiaryName(){
        return this.accountHolderName;
    }

    @JsonIgnore
    public String getPensionAmount(){
        return contributionAmount != null ? NumberFormatter.formatNumber(contributionAmount) : "0";
    }

    public OfficeStaffPayrollBean getOfficeStaffPayrollBean() {
        return officeStaffPayrollBean;
    }

    public void setOfficeStaffPayrollBean(OfficeStaffPayrollBean officeStaffPayrollBean) {
        this.officeStaffPayrollBean = officeStaffPayrollBean;
    }

    public String getMonthYear(){
        return DateUtil.formatSimpleMonthYear(getPayrollMonth());
    }

    public String getPaymentMethod(){
        return this.getPayrollAccountantTodo() == null ? "" : getPayrollAccountantTodo().getTaskName();
    }

    public Boolean getCurrentlyExcludedFromPayroll (){
        if (officeStaff != null)
            return officeStaff.getExcludedFromPayroll();
        return true;
    }

    public MonthlyPaymentRule getMonthlyPaymentRule() {
        return monthlyPaymentRule;
    }

    public void setMonthlyPaymentRule(MonthlyPaymentRule monthlyPaymentRule) {
        this.monthlyPaymentRule = monthlyPaymentRule;
    }

    public PayrollAuditTodo getPayrollAuditTodo() {
        return payrollAuditTodo;
    }

    public void setPayrollAuditTodo(PayrollAuditTodo payrollAuditTodo) {
        this.payrollAuditTodo = payrollAuditTodo;
    }

    public OfficeStaffPayrollLogStatus getLogStatus() {
        return logStatus;
    }

    public void setLogStatus(OfficeStaffPayrollLogStatus logStatus) {
        this.logStatus = logStatus;
    }

    public Double getStaffTotalSalary() {
        return staffTotalSalary;
    }

    public void setStaffTotalSalary(Double staffTotalSalary) {
        this.staffTotalSalary = staffTotalSalary;
    }

    public Double getStaffBasicSalary() {
        return staffBasicSalary;
    }

    public void setStaffBasicSalary(Double staffBasicSalary) {
        this.staffBasicSalary = staffBasicSalary;
    }

    public Double getStaffHousing() {
        return staffHousing;
    }

    public void setStaffHousing(Double staffHousing) {
        this.staffHousing = staffHousing;
    }

    public Double getStaffTransportation() {
        return staffTransportation;
    }

    public void setStaffTransportation(Double staffTransportation) {
        this.staffTransportation = staffTransportation;
    }

    public Double getStaffFixPension() {
        return staffFixPension;
    }

    public void setStaffFixPension(Double staffFixPension) {
        this.staffFixPension = staffFixPension;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getMarkUnpaidNotes() {
        return markUnpaidNotes;
    }

    public void setMarkUnpaidNotes(String markUnpaidNotes) {
        this.markUnpaidNotes = markUnpaidNotes;
    }

    public String getPensionIBAN() {
        if(this.officeStaff != null)
            return this.officeStaff.getPensionIBAN();
        return pensionIBAN;
    }

    public void setPensionIBAN(String pensionIBAN) {
        this.pensionIBAN = pensionIBAN;
    }

    public enum OfficeStaffPayrollLogStatus implements LabelValueEnum {
        PENDING("Pending"),
        FINAL("Final");

        private final String label;

        OfficeStaffPayrollLogStatus(String label) {
            this.label = label;
        }

        @Override
        public String getLabel() {
            return label;
        }
    }

    public Double getLoanRepayment() {
        if (loanRepayment == null)
            loanRepayment = 0D;
        return Double.valueOf(Math.round(loanRepayment));
    }

    public void setLoanRepayment(Double loanRepayment) {
        this.loanRepayment = loanRepayment;
    }

    public String getPaymentType() {
        if (paymentType == null) {
            return "";
        }
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public Double getTotalSalaryInADCBReport() {
        if (totalSalaryInADCBReport == null) {
            return 0.0;
        }
        return totalSalaryInADCBReport;
    }

    public void setTotalSalaryInADCBReport(Double totalSalaryInADCBReport) {
        this.totalSalaryInADCBReport = totalSalaryInADCBReport;
    }

    public String getPaymentCurrency() {
        if (paymentCurrency == null) {
            return "";
        }
        return paymentCurrency;
    }

    public void setPaymentCurrency(String paymentCurrency) {
        this.paymentCurrency = paymentCurrency;
    }

    public String getBeneficiaryAddress1() {
        if (beneficiaryAddress1 == null) {
            return "";
        }
        return beneficiaryAddress1;
    }

    public void setBeneficiaryAddress1(String beneficiaryAddress1) {
        this.beneficiaryAddress1 = beneficiaryAddress1;
    }

    public String getBeneficiaryAddress2() {
        if (beneficiaryAddress2 == null) {
            return "";
        }
        return beneficiaryAddress2;
    }

    public void setBeneficiaryAddress2(String beneficiaryAddress2) {
        this.beneficiaryAddress2 = beneficiaryAddress2;
    }

    public String getBeneficiaryAddress3() {
        if (beneficiaryAddress3 == null) {
            return "";
        }
        return beneficiaryAddress3;
    }

    public void setBeneficiaryAddress3(String beneficiaryAddress3) {
        this.beneficiaryAddress3 = beneficiaryAddress3;
    }

    public String getBeneficiaryAccount() {
        if (beneficiaryAccount == null) {
            return "";
        }
        return beneficiaryAccount;
    }

    public void setBeneficiaryAccount(String beneficiaryAccount) {
        this.beneficiaryAccount = beneficiaryAccount;
    }

    public String getBeneficiaryBankName() {
        if (beneficiaryBankName == null) {
            return "";
        }
        return beneficiaryBankName;
    }

    public void setBeneficiaryBankName(String beneficiaryBankName) {
        this.beneficiaryBankName = beneficiaryBankName;
    }

    public String getBeneficiaryBankAddress1() {
        if (beneficiaryBankAddress1 == null) {
            return "";
        }
        return beneficiaryBankAddress1;
    }

    public void setBeneficiaryBankAddress1(String beneficiaryBankAddress1) {
        this.beneficiaryBankAddress1 = beneficiaryBankAddress1;
    }

    public String getBeneficiaryBankAddress2() {
        if (beneficiaryBankAddress2 == null) {
            return "";
        }
        return beneficiaryBankAddress2;
    }

    public void setBeneficiaryBankAddress2(String beneficiaryBankAddress2) {
        this.beneficiaryBankAddress2 = beneficiaryBankAddress2;
    }

    public Boolean getInsideUAE() {
        return insideUAE;
    }

    public void setInsideUAE(Boolean insideUAE) {
        this.insideUAE = insideUAE;
    }
}
