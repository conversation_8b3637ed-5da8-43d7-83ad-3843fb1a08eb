package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.google.api.client.util.Strings;
import com.magnamedia.controller.OfficestaffPayrollController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.serialize.IdJsonSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.accessmgmt.ExternalAccess;
import com.magnamedia.entity.accessmgmt.OfficeStaffAccess;
import com.magnamedia.entity.serializer.*;
import com.magnamedia.extra.LocationEnum;
import com.magnamedia.extra.OfficeStaffLastSalary;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.payroll.generation.OfficeStaffFinalSettlementService;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.NotAudited;
import org.hibernate.validator.constraints.Email;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Period;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * on Al-Habash Created on 2017-08-16
 */
@Entity @org.hibernate.envers.Audited
public class OfficeStaff extends BaseEntity {

    //    public final static Set<OfficeStaffStatus> rejectedStatuses
//            = new HashSet<>(Arrays.asList(OfficeStaffStatus.TERMINATED, OfficeStaffStatus.TERMINATED_FROM_MOL, OfficeStaffStatus.INACTIVE));
//    @Column(updatable = false,
//            insertable = false)

    @OneToOne(mappedBy = "officeStaff", fetch = FetchType.LAZY)
    @JsonIgnore
    private OfficeStaffFinalSettlement finalSettlement;

    @Column
    private String jazzHRProfile;

    @Enumerated(EnumType.STRING)
    private ReceiveMoneyMethod receiveMoneyMethod;

    private Boolean selfReceiver;

    @OneToMany(mappedBy = "officeStaff", fetch = FetchType.LAZY)
    List<OfficeStaffDocument> documents;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    OfficeStaffCandidate officeStaffCandidate;

    @Transient
    private Boolean createCancel = Boolean.FALSE;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem terminationReason;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private OfficeStaff whoRequestedTermination;

    @ManyToMany
    @JoinTable(name = "EXTERNAL_ACCESS_EMPLOYEES",
            joinColumns = @JoinColumn(name = "EMPLOYEES_ID"),
            inverseJoinColumns = @JoinColumn(name = "EXTERNAL_ACCESSES_ID"))
    @JsonIgnore
    @Deprecated
    private List<ExternalAccess> employeeAccesses;

    @OneToMany(mappedBy = "employee",
            fetch = FetchType.LAZY)
    private List<OfficeStaffAccess> externalAccesses;

    @Label
    private String name;

    @Column
    private String firstName;

    @Column
    private String middleName;

    @Column
    private String lastName;

    @Transient
    private String firstLastName;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem nationality;



    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem jobTitle;

    @Column(updatable = false,
            insertable = false)
    private Boolean isActive;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem manager;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private OfficeStaff employeeManager;


    @Enumerated(EnumType.STRING)
    private OfficeStaffStatus status;

    @Enumerated(EnumType.STRING)
    private OfficeStaffTerminationType terminationType;

    @Column
    private Date noticePeriodStartDate;

    @Column
    private Date noticePeriodEndDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeTagsSerializer.class)
    private PicklistItem country;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem city;

    @Column
    private String cityName;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column
    private Double basicSalary;

    //Jirra ACC-785
    @Transient
    private Double basicSalarySearch;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column(columnDefinition = "double default 0")
    private Double primarySalary = 0.0;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column(columnDefinition = "double default 0")
    private Double monthlyLoan = 0.0;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column(columnDefinition = "double default 0")
    private Double compensation = 0.0;

    @Column
    private Date oldStartingDate;

    @Column
    private Date oldTerminationDate;

    @Column
    private Date startingDate;

    @Column
    private String pnl;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column
    private Double housingAllowance = 0.0;

    @Column
    private Double internetAllowance;

    @Column
    private Double salaryBeforeRehiring;

    @Column(columnDefinition = "double default 0")
    private Double oldInternetAllowance = 0d;

    @NotAudited
    @Column
    private String phoneNumber;

    @NotAudited
    @Column
    private String fullNameInArabic;

    @Column
    private Date terminationDate;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column
    private Double trasnportation = 0.0;

    @Column(columnDefinition = "boolean default false")
    private Boolean excludedFromPayroll = false;

    @Column
    @Lob
    private String excludedFromPayrollNotes;

    @NotAudited
    @Enumerated(EnumType.STRING)
    private OfficeStaffType employeeType;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem salaryPayementMethod;

    @Enumerated(EnumType.STRING)
    private SalaryCurrency salaryCurrency;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = CustomIdLabelCodeSerializer.class)
    private PicklistItem salaryTransfereDestination;

    @NotAudited
    @Lob
    @Column
    private String salaryTransferDestinationOther;

    @Column
    private Double defaulMonthlyRepayment;

    @JsonSerialize(using = IdLabelSerializer.class)
    @OneToOne
    private User user;

//	@NotAudited
//	@OneToMany(cascade = CascadeType.ALL,
//			   mappedBy = "officeStaff",
//			   fetch = FetchType.LAZY)
//	private List<ScheduledAnnualVacation> scheduledAnnualVacations;

    @ElementCollection(targetClass= DayOfWeek.class)
    @Enumerated(EnumType.STRING)
    @Column(name="weeklyOffDay")
    protected List<DayOfWeek> weeklyOffDayList;

    @Column(columnDefinition = "double default 0")
    private Double consumedOffDaysBalance = 0D;

//    @Column(columnDefinition = "bigint(20) default 0")
//    private Long workingPublicHolidaysBalance = 0L;

    @Column(columnDefinition = "double default 0")
    private Double paidOffDaysBalance = 0D;

    @NotAudited
    @OneToMany(cascade = CascadeType.ALL,
            mappedBy = "officeStaff",
            fetch = FetchType.LAZY)
    private List<PaidOffDays> paidOffDays;

    @NotAudited
    @OneToMany(cascade = CascadeType.ALL,
            mappedBy = "officeStaff",
            fetch = FetchType.LAZY)
    private List<TravelDays> travelDays;

    @NotAudited
    @OneToMany(cascade = CascadeType.ALL,
            mappedBy = "officeStaff",
            fetch = FetchType.LAZY)
    private List<WorkingPublicHoliday> workingPublicHolidays;

    @NotAudited
    @OneToMany(cascade = CascadeType.ALL,
            mappedBy = "officestaff",
            fetch = FetchType.LAZY)
    private List<Repayment> repaymentsList;

    @Transient
    private Double loanBalance;

    @Transient
    private Double repayments;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdJsonSerializer.class)
    private NewRequest visaNewRequest;

    //     * Jirra ACC-1472
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private AirfareTicketType airfareTicketType;

    @NotAudited
    @Column
    private String localReceiverName;

    @NotAudited
    @Column
    private String localReceiverArabicName;

    @NotAudited
    @Column
    private String localReceiverPhoneNumber;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem team;

    @Column
    private String insuranceNumber;

    @Column
    private OfficeStaffLastSalary lastSalary;

    //pay roll new fields
    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem updatedSalaryTransfereDestination;

    @NotAudited
    @Lob
    private String updatedOtherSalaryTransfereDestination;

    @NotAudited
    @Column
    private String updatedArabicName;

    @NotAudited
    @Column
    private String updatedEnglishName;

    @NotAudited
    @Column
    private String updatedMobileNumber;

    @NotAudited
    @Column
    private String updatedEmail;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem updatedCity;

    @Column
    private String qid;

    @Column
    private String employeeAccountQnb;

    //Jirra ACC-1258
    @Column
    private String accountName;

    // jira acc-1472
    @Transient
    private boolean canAddTicketType;

    @NotAudited
    //@Pattern(regexp = "^([_a-zA-Z0-9-]+(\\.[_a-zA-Z0-9-]+)*@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*(\\.[a-zA-Z]{1,6}))?$", message = "Please enter a valid email!")
    @Email(message = "Please enter a valid email!")
    private String email;

    @NotAudited
    @Lob
    @Column
    @JsonSerialize(using = SalaryAmountSerializer.class)
    private String fullAddress;

    @NotAudited
    @Column
    private String updatedFullAddress;
    //...PAY-899...//
    @Column
    @JsonSerialize(using = SalaryAmountSerializer.class)
    private String emergencyContactName;

    @Column
    @JsonSerialize(using = SalaryAmountSerializer.class)
    private String emergencyContactPhoneNumber;


    @Column
    private String iban;

    @Column
    private String accountHolderName;

    @Column
    private String accountNumber;

    @Column
    private String eidNumber;

    @Column(columnDefinition = "boolean default false")
    private Boolean visaRequired = false;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column(columnDefinition = "double default 0")
    @NotNull
    private Double salary = 0d;

    @NotAudited
    @Lob
    @Column
    private String nearestCenter;

    @OneToMany(mappedBy = "officeStaff", fetch = FetchType.LAZY)
    private List<TransferDestination> transferDestinations;

    @OneToOne(fetch = FetchType.LAZY)
    private TransferDestination selectedTransferDestination;

    @Column
    private Date lastPayrollLockDate;

    @Column(columnDefinition = "boolean default false")
    private Boolean withMolNumber = false;

    @Column(columnDefinition = "boolean default false")
    private boolean confirmedHighSalaryByAuditor = false;

    @Column(columnDefinition = "boolean default false")
    private boolean confirmedTerminationCompensationByAuditor = false;

    @NotAudited
    @Enumerated(EnumType.STRING)
    private CommunicationMethod preferredCommunicationMethod;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Expense pnlExpense;

    @Column
    private java.sql.Date birthDate;

    @Column(columnDefinition = "boolean default true")
    Boolean receiveBirthdaysForDirectTeamMembers = true;

    @Column(columnDefinition = "boolean default true")
    Boolean receiveBirthdaysForInDirectTeamMembers = true;

    @Column(columnDefinition = "boolean default true")
    Boolean receiveBirthdaysForInDirect2TeamMembers = true;

    @NotAudited
//    @Pattern(regexp = "^([_a-zA-Z0-9-]+(\\.[_a-zA-Z0-9-]+)*@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*(\\.[a-zA-Z]{1,6}))?$", message = "Please enter a valid email!")
    @Email(message = "Please enter a valid email!")
    private String newEmail;

    @Column
    @Lob
    private String terminationNotes;

    @Column
    private Double fixedPensionAmount;

    @Column(columnDefinition = "boolean default false")
    private Boolean doNotDeductContribution = false;

    @Transient
    public String moneyReceiverName;

    //used for filter API only
    @Transient
    public String startDateFilter;
    @Transient
    public java.sql.Date startDateFrom;
    @Transient
    public java.sql.Date startDateTo;
    @Transient
    public String terminationDateFilter;
    @Transient
    public java.sql.Date terminationDateFrom;
    @Transient
    public java.sql.Date terminationDateTo;

    @NotAudited
    @Formula("(EXISTS (select 1 from OFFICESTAFFDOCUMENTS o inner join ATTACHMENTS a on a.OWNER_TYPE = 'OfficeStaffDocument' and a.OWNER_ID = o.ID where o.OFFICE_STAFF_ID = ID and a.TAG in ('EMIRATES_ID_FRONT_SIDE', 'EMIRATES_ID_BACK_SIDE', 'EMIRATE_ID', 'eid_front_side', 'eid_front', 'eid_front', 'eid_back_side', 'eid_back') ))")
    private Boolean hasEidAttachments;

    @NotAudited
    @Formula("(EXISTS (select 1 from NEWREQUESTS n inner join ATTACHMENTS a on a.OWNER_TYPE = 'NewRequest' and a.OWNER_ID = n.ID where n.OFFICE_STAFF_ID = ID and a.TAG in ('EMIRATES_ID_FRONT_SIDE', 'EMIRATES_ID_BACK_SIDE', 'EMIRATE_ID', 'eid_front_side', 'eid_front', 'eid_front', 'eid_back_side', 'eid_back') ))")
    private Boolean visaRequestHasEidAttachments;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem location;

    @Column
    private Date potentialStartDate;

    @Column
    private Boolean onOrBefore31thOct2023;

    @Column
    private Date expatStartDate;
    @Column
    private Date switchingToExpatDate;

    @Column(columnDefinition = "boolean default false")
    private Boolean overseasToExpatSwitched = false;

    @Column
    private Double salaryAsOverseas;

    @Enumerated(EnumType.STRING)
    private SalaryCurrency initialCurrency;

    @Enumerated(EnumType.STRING)
    private LocationEnum locationEnum;
    @Column
    private String zohoProfile;
    @Column
    private String zohoJobTitle;
    @Column
    private String zohoExactJobTitle;

    @ManyToMany(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeNameListSerializer.class)
    private List<PicklistItem> departments;

    public List<OfficeStaffDocument> getDocuments() {
        if(documents == null)
            documents = new ArrayList<>();
        return documents;
    }

    public void setDocuments(List<OfficeStaffDocument> documents) {
        this.documents = documents;
    }

    public OfficeStaffCandidate getOfficeStaffCandidate() {
        return officeStaffCandidate;
    }

    public void setOfficeStaffCandidate(OfficeStaffCandidate officeStaffCandidate) {
        this.officeStaffCandidate = officeStaffCandidate;
    }

    public PicklistItem getTeam() {
        return team;
    }

    public void setTeam(PicklistItem team) {
        this.team = team;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public PicklistItem getNationality() {
        return nationality;
    }

    public void setNationality(PicklistItem nationality) {
        this.nationality = nationality;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public PicklistItem getManager() {
        return manager;
    }

    public void setManager(PicklistItem manager) {
        this.manager = manager;
    }

    public OfficeStaffStatus getStatus() {
        return status;
    }

    public void setStatus(OfficeStaffStatus status) {
        this.status = status;
    }

    public PicklistItem getCountry() {
        return country;
    }

    public void setCountry(PicklistItem country) {
        this.country = country;
    }

    public PicklistItem getCity() {
        return city;
    }

    public void setCity(PicklistItem city) {
        this.city = city;
    }

    public String getCityAsString() {
        if (city != null) {
            return city.getName();
        }
        return cityName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    @NotAudited
    @Enumerated(EnumType.STRING)
    private WorkingPattern workingPattern;

    public Double getBasicSalary() {
//        if (this.basicSalary == null)
//            this.basicSalary =
//                    (this.monthlyLoan != null ? this.monthlyLoan : 0.0)
//                            + (this.primarySalary != null ? this.primarySalary : 0.0);
        if(basicSalary == null) return 0d;
        return basicSalary;
    }
    public PicklistItem getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(PicklistItem jobTitle) {
        this.jobTitle = jobTitle;
    }

    public void setBasicSalary(Double basicSalary) {
        this.basicSalary = basicSalary;
    }

    @JsonSerialize(using = SalaryAmountSerializer.class)
    public Double getRosterSalary() {
        //Jirra ACC-279
        return (this.basicSalary != null ? this.basicSalary : 0.0)
                + (this.housingAllowance != null ? this.housingAllowance : 0.0)
                + (this.trasnportation != null ? this.trasnportation : 0.0)
                + (this.internetAllowance != null ? this.internetAllowance : 0.0);
    }

    public Double getBasicSalarySearch() {
        return basicSalarySearch;
    }

    public void setBasicSalarySearch(Double basicSalarySearch) {
        this.basicSalarySearch = basicSalarySearch;
    }

    public Date getStartingDate() {

        return startingDate;
    }

    public void setStartingDate(Date startingDate) {

        this.startingDate = startingDate;
    }

    public String getPnl() {
        return pnl;
    }

    public void setPnl(String pnl) {
        //this.pnl = pnl;
        this.pnl = pnl;
    }

    public Double getHousingAllowance() {
        if(housingAllowance == null) return 0d;
        return housingAllowance;
    }

    public void setHousingAllowance(Double housingAllowance) {
        this.housingAllowance = housingAllowance;
    }

    public Double getInternetAllowance() {
        return internetAllowance;
    }

    public void setInternetAllowance(Double internetAllowance) {
        this.internetAllowance = internetAllowance;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getFullNameInArabic() {
        return fullNameInArabic;
    }

    public void setFullNameInArabic(String fullNameInArabic) {
        this.fullNameInArabic = fullNameInArabic;
    }

    public Date getTerminationDate() {
        return terminationDate;
    }

    public void setTerminationDate(Date terminationDate) {
        this.terminationDate = terminationDate;
    }

    public Double getTrasnportation() {
        if(trasnportation == null) return 0d;
        return trasnportation;
    }

    public void setTrasnportation(Double trasnportation) {
        this.trasnportation = trasnportation;
    }

    public OfficeStaffType getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(OfficeStaffType employeeType) {
        this.employeeType = employeeType;
    }

    public PicklistItem getSalaryPayementMethod() {
        if(this.getSelectedTransferDestination() != null) {
            return this.getSelectedTransferDestination().getSalaryPayementMethod();
        }
        return salaryPayementMethod;
    }

    public void setSalaryPayementMethod(PicklistItem salaryPayementMethod) {
        this.salaryPayementMethod = salaryPayementMethod;
    }

    public SalaryCurrency getSalaryCurrency() {
        return salaryCurrency;
    }

    public void setSalaryCurrency(SalaryCurrency salaryCurrency) {
        this.salaryCurrency = salaryCurrency;
    }

    public PicklistItem getSalaryTransfereDestination() {
        return salaryTransfereDestination;
    }

    public void setSalaryTransfereDestination(
            PicklistItem salaryTransfereDestination) {
        this.salaryTransfereDestination = salaryTransfereDestination;
    }

    public Boolean getExcludedFromPayroll() {
        return excludedFromPayroll;
    }

    public void setExcludedFromPayroll(Boolean excludedFromPayroll) {
        this.excludedFromPayroll = excludedFromPayroll;
    }

    public String getExcludedFromPayrollNotes() {
        return excludedFromPayrollNotes;
    }

    public void setExcludedFromPayrollNotes(String excludedFromPayrollNotes) {
        this.excludedFromPayrollNotes = excludedFromPayrollNotes;
    }

    public Double getDefaulMonthlyRepayment() {
        return defaulMonthlyRepayment;
    }

    public void setDefaulMonthlyRepayment(Double defaulMonthlyRepayment) {
        this.defaulMonthlyRepayment = defaulMonthlyRepayment;
    }

//	public List<ScheduledAnnualVacation> getScheduledAnnualVacations() {
//		return scheduledAnnualVacations;
//	}
//
//	public void setScheduledAnnualVacations(
//		List<ScheduledAnnualVacation> scheduledAnnualVacations) {
//		this.scheduledAnnualVacations = scheduledAnnualVacations;
//	}

    public List<Repayment> getRepaymentsList() {
        return repaymentsList;
    }

    public void setRepaymentsList(List<Repayment> repaymentsList) {
        this.repaymentsList = repaymentsList;
    }

    public Double getLoanBalance() {
//        Set<HousemaidStatus> rejectedStatuses = new HashSet<HousemaidStatus>(Arrays.asList(HousemaidStatus.REJECTED, HousemaidStatus.UNREACHABLE, HousemaidStatus.UNREACHABLE_AFTER_EXIT, HousemaidStatus.PASSED_EXIT));
        //if (loanBalance == null) {
//            if (rejectedStatuses.contains(this.getStatus())) {
//                this.loanBalance = 0.0;
//            } else {
        EmployeeLoanRepository employeeLoanRepository = Setup.getRepository(
                EmployeeLoanRepository.class);

        Double originalLoan = employeeLoanRepository.sumLoansByOfficeStaff(
                this);
        Double repayment = this.getRepayments();
        if (originalLoan == null) {
            originalLoan = 0.0;
        }
        if (repayment == null) {
            repayment = 0.0;
        }
        this.loanBalance = originalLoan - repayment;
//            }
        //}
        return this.loanBalance;
    }



    public void setLoanBalance(Double loanBalance) {

        this.loanBalance = loanBalance;

    }

    //Jirra ACC-1085
    public Double getRepayments() {
        repayments = 0.0;
        RepaymentRepository repaymentRepository = Setup.getRepository(
                RepaymentRepository.class);
        repayments = repaymentRepository.sumEditableLoansRepaymentsByOfficeStaff(this);
        return repayments != null ? repayments : 0.0;
    }



    public void setRepayments(Double repayments) {
        this.repayments = repayments;
    }

    public NewRequest getVisaNewRequest() {
        return visaNewRequest;
    }

    public void setVisaNewRequest(NewRequest visaNewRequest) {
        this.visaNewRequest = visaNewRequest;
    }

    public String getLocalReceiverName() {
        return localReceiverName;
    }

    public void setLocalReceiverName(String localReceiverName) {
        this.localReceiverName = localReceiverName;
    }

    public String getLocalReceiverArabicName() {
        return localReceiverArabicName;
    }

    public void setLocalReceiverArabicName(String localReceiverArabicName) {
        this.localReceiverArabicName = localReceiverArabicName;
    }

    public String getLocalReceiverPhoneNumber() {
        return localReceiverPhoneNumber;
    }

    public void setLocalReceiverPhoneNumber(String localReceiverPhoneNumber) {
        this.localReceiverPhoneNumber = localReceiverPhoneNumber;
    }

    public String getInsuranceNumber() {
        return insuranceNumber;
    }

    public void setInsuranceNumber(String insuranceNumber) {
        this.insuranceNumber = insuranceNumber;
    }

    public OfficeStaffLastSalary getLastSalary() {
        return lastSalary;
    }

    public void setLastSalary(OfficeStaffLastSalary lastSalary) {
        this.lastSalary = lastSalary;
    }

    public String getSalaryTransferDestinationOther() {
        return salaryTransferDestinationOther;
    }

    public void setSalaryTransferDestinationOther(String salaryTransferDestinationOther) {
        this.salaryTransferDestinationOther = salaryTransferDestinationOther;
    }

    public PicklistItem getUpdatedSalaryTransfereDestination() {
        return updatedSalaryTransfereDestination;
    }

    public void setUpdatedSalaryTransfereDestination(PicklistItem updatedSalaryTransfereDestination) {
        this.updatedSalaryTransfereDestination = updatedSalaryTransfereDestination;
    }

    public String getUpdatedOtherSalaryTransfereDestination() {
        return updatedOtherSalaryTransfereDestination;
    }

    public void setUpdatedOtherSalaryTransfereDestination(String updatedOtherSalaryTransfereDestination) {
        this.updatedOtherSalaryTransfereDestination = updatedOtherSalaryTransfereDestination;
    }

    public String getUpdatedArabicName() {
        if(this.getSelectedTransferDestination() != null) {
            return this.getSelectedTransferDestination().getFullNameInArabic();
        }
        return updatedArabicName;
    }

    public void setUpdatedArabicName(String updatedArabicName) {
        this.updatedArabicName = updatedArabicName;
    }

    public String getUpdatedEnglishName() {
        if(this.getSelectedTransferDestination() != null) {
            return this.getSelectedTransferDestination().getName();
        }
        return updatedEnglishName;
    }

    public void setUpdatedEnglishName(String updatedEnglishName) {
        this.updatedEnglishName = updatedEnglishName;
    }

    public String getUpdatedMobileNumber() {
        if(this.getSelectedTransferDestination() != null) {
            return this.getSelectedTransferDestination().getPhoneNumber();
        }
        return updatedMobileNumber;
    }

    public void setUpdatedMobileNumber(String updatedMobileNumber) {
        this.updatedMobileNumber = updatedMobileNumber;
    }

    public String getQid() {
        return qid;
    }

    public void setQid(String qid) {
        this.qid = qid;
    }

    public String getEmployeeAccountQnb() {
        return employeeAccountQnb;
    }

    public void setEmployeeAccountQnb(String employeeAccountQnb) {
        this.employeeAccountQnb = employeeAccountQnb;
    }

    public Double getPrimarySalary() {
        return primarySalary;
    }

    public void setPrimarySalary(Double primarySalary) {
        this.primarySalary = primarySalary;
    }

    public Double getMonthlyLoan() {
        return monthlyLoan;
    }

    public void setMonthlyLoan(Double monthlyLoan) {
        this.monthlyLoan = monthlyLoan;
    }

    public String getAccountName() {
        if(this.getSelectedTransferDestination() != null
                && this.getSelectedTransferDestination().getReceiveMoneyMethod() == ReceiveMoneyMethod.BANK_TRANSFER) {
            return this.getSelectedTransferDestination().getAccountHolderName();
        }
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    //Jirra ACC-1227
    public Double getEarnedOffDays() {
        if (this.startingDate == null || employeeType.equals(OfficeStaffType.OVERSEAS_STAFF))
            return 0.0;

        Calendar startWorkingCalendar = Calendar.getInstance();
        if(OfficeStaffType.DUBAI_STAFF_EXPAT.equals(employeeType)){
            if(expatStartDate == null)
                return 0.0;
            startWorkingCalendar.setTime(this.expatStartDate);
        }
         else
             startWorkingCalendar.setTime(this.startingDate);
        startWorkingCalendar.add(Calendar.DAY_OF_MONTH, 1);

        LocalDate now = LocalDate.now();

        if (terminationDate != null) {
            now = DateUtil.toLocalDate(terminationDate);
        }

        LocalDate startWorking = LocalDate.of(startWorkingCalendar.get(Calendar.YEAR), startWorkingCalendar.get(Calendar.MONTH) + 1,
                startWorkingCalendar.get(Calendar.DAY_OF_MONTH));

        Period period = Period.between(startWorking, now);
        Integer diffYear = period.getYears();
        Integer diffMonth = period.getMonths();

        if (diffYear.equals(0) && diffMonth < 6)
            diffMonth = 0;
//        Calendar now = Calendar.getInstance();
//        Calendar startWorking = Calendar.getInstance();
//        startWorking.setTime(this.startingDate);
//
//        if (now.get(Calendar.YEAR) > startWorking.get(Calendar.YEAR) && now.get(Calendar.DAY_OF_YEAR) > startWorking.get(Calendar.DAY_OF_YEAR))
//            return 30;
//
//        int diffYear = now.get(Calendar.YEAR) - startWorking.get(Calendar.YEAR);
//        int diffMonth = diffYear * 12 + now.get(Calendar.MONTH) - startWorking.get(Calendar.MONTH);
//
//        if (now.get(Calendar.DAY_OF_MONTH) <= startWorking.get(Calendar.DAY_OF_MONTH))
//            diffMonth--;

        //Jirra ACC-1648
        return (diffYear * 30) + (diffMonth * 2) + (this.getWorkingPublicHolidaysBalance(null, true) * 1.5D);
    }

    public List<DayOfWeek> getWeeklyOffDayList() {
        if(weeklyOffDayList == null)
            weeklyOffDayList = new ArrayList<>();
        return weeklyOffDayList;
    }

    public void setWeeklyOffDayList(List<DayOfWeek> weeklyOffDayList) {
        this.weeklyOffDayList = weeklyOffDayList;
    }

    public Double getConsumedOffDaysBalance() {
        return consumedOffDaysBalance;
    }

    public void setConsumedOffDaysBalance(Double consumedOffDaysBalance) {
        this.consumedOffDaysBalance = consumedOffDaysBalance;
    }

    //Jirra ACC-1227
    @Transient
    public Double getOffDaysBalance() {

        if (consumedOffDaysBalance == null) consumedOffDaysBalance = 0D;
//        if (workingPublicHolidaysBalance == null) workingPublicHolidaysBalance = 0L;
        //Jirra ACC-1648
        return getEarnedOffDays() - consumedOffDaysBalance;
    }

    @JsonIgnore
    public List<PaidOffDays> getPaidOffDays() {
        return paidOffDays != null ? paidOffDays : new ArrayList<>();
    }

    public void setPaidOffDays(List<PaidOffDays> paidOffDays) {
        this.paidOffDays = paidOffDays;
    }

    @JsonIgnore
    public List<TravelDays> getTravelDays() {
        return travelDays;
    }

    public void setTravelDays(List<TravelDays> travelDays) {
        this.travelDays = travelDays;
    }

    @JsonIgnore
    public List<WorkingPublicHoliday> getWorkingPublicHolidays() {
        return workingPublicHolidays;
    }

    public void setWorkingPublicHolidays(List<WorkingPublicHoliday> workingPublicHolidays) {
        this.workingPublicHolidays = workingPublicHolidays;
    }

    //Jirra ACC-1227
    @Transient
    public Long getWorkingPublicHolidaysBalance(Integer year, Boolean inPastOnly) {
        WorkingPublicHolidayRepository workingPublicHolidayRepository = Setup.getRepository(WorkingPublicHolidayRepository.class);
        Long numberOfDays = 0L;
        List<WorkingPublicHoliday> workingPublicHolidayList;

        if (year == null) {
            if (inPastOnly)
                workingPublicHolidayList = workingPublicHolidayRepository
                        .findByOfficeStaffAndEndDateLessThanEqual(this, new Date());
            else
                workingPublicHolidayList = workingPublicHolidayRepository
                        .findByOfficeStaff(this);
        } else {
            LocalDate startDate = LocalDate.of(year + 1, 1, 1);
            LocalDate endDate = LocalDate.of(year - 1, 12, 31);

            if(inPastOnly) {
                LocalDate today = LocalDate.now();
                endDate = endDate.compareTo(today) > 0 ? today : endDate;
            }

            workingPublicHolidayList = workingPublicHolidayRepository
                    .findByOfficeStaffAndStartDateLessThanOrEndDateGreaterThan(this
                            , java.sql.Date.valueOf(startDate.atTime(0, 0).toLocalDate()),
                            java.sql.Date.valueOf(endDate.atTime(0, 0).toLocalDate()));
        }

        if (workingPublicHolidayList != null) {
            for (WorkingPublicHoliday workingPublicHoliday : workingPublicHolidayList) {
                numberOfDays += year == null ? workingPublicHoliday.getNumOfDays() : workingPublicHoliday.getNumOfDays(year);
            }
        }

        return numberOfDays;
    }

//    public void setWorkingPublicHolidaysBalance(Long workingPublicHolidaysBalance) {
//        this.workingPublicHolidaysBalance = workingPublicHolidaysBalance;
//    }


    public Double getPaidOffDaysBalance() {
        return paidOffDaysBalance;
    }

    public void setPaidOffDaysBalance(Double paidOffDaysBalance) {
        this.paidOffDaysBalance = paidOffDaysBalance;
    }

    @BeforeUpdate
    private void preSave() {
        OfficeStaff old = Setup.getApplicationContext()
                .getBean(OfficeStaffRepository.class)
                .getOne(this.getId());
        //Jirra ACC-270
        if (this.employeeType == OfficeStaffType.FT_OFFICE_STAFF ||
                this.employeeType == OfficeStaffType.PT_OFFICE_STAFF)
            throw new BusinessException("Employee Type could not be " +
                    this.employeeType.toString() + " .");

//        if (this.monthlyLoan == null)
//            this.monthlyLoan = old.getMonthlyLoan();
//        if (this.primarySalary == null)
//            this.primarySalary = old.getPrimarySalary();
//        this.basicSalary =
//                (this.monthlyLoan != null ? this.monthlyLoan : 0D)
//                        + (this.primarySalary != null ? this.primarySalary : 0D);
    }

    @BeforeInsert
    private void preInsert() {
        //Jirra ACC-270
        if (this.employeeType == OfficeStaffType.FT_OFFICE_STAFF ||
                this.employeeType == OfficeStaffType.PT_OFFICE_STAFF)
            throw new BusinessException("Emplouee Type could not be " +
                    this.employeeType.toString() + " .");

//        if (this.monthlyLoan == null)
//            this.monthlyLoan = 0.0;
//        if (this.primarySalary == null)
//            this.primarySalary = 0.0;
//        this.basicSalary = this.monthlyLoan + this.primarySalary;
    }

    public AirfareTicketType getAirfareTicketType() {
        return airfareTicketType;
    }

    public void setAirfareTicketType(AirfareTicketType airfareTicketType) {
        this.airfareTicketType = airfareTicketType;
    }

    public boolean getCanAddTicketType() {
        return getCanAddTicketType(employeeType);
    }

    public void setCanAddTicketType(boolean canAddTicketType) {
        this.canAddTicketType = canAddTicketType;
    }

    public boolean getCanAddTicketType(OfficeStaffType employeeType) {
        return employeeType == OfficeStaffType.DUBAI_STAFF_EXPAT;
    }

    @JsonIgnore
    public OfficeStaff getFinalManagerTree() {
        if(this.getEmployeeManager() == null || getEmployeeManager().getId().equals(this.getId())) {
            return this;
        }
        return this.getEmployeeManager().getFinalManagerTree();
    }

    @JsonIgnore
    public OfficeStaff getFinalManagerTreeNotCFO(Long CFO_ID) {
        if(this.getEmployeeManager() == null || getEmployeeManager().getId().equals(this.getId()) || getEmployeeManager().getId().equals(CFO_ID)) {
            return this;
        }
        return this.getEmployeeManager().getFinalManagerTreeNotCFO(CFO_ID);
    }

    @JsonIgnore
    public OfficeStaff getFinalManager() {
        OfficeStaff finalManager = getFinalManagerTree();
        if (finalManager == null || finalManager.getId().equals(this.getId())) {
            return Setup.getRepository(OfficeStaffRepository.class).findOne(Long.parseLong(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CFO_ID)));
        }
        return finalManager;
    }

    @JsonIgnore
    public OfficeStaff getLastManagerOtherThanCFO() {
        Long CFO_ID = Long.valueOf(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CFO_ID));
        OfficeStaff lastManager = getFinalManagerTreeNotCFO(CFO_ID);
        if (lastManager == null || lastManager.getId().equals(this.getId())) {
            return Setup.getRepository(OfficeStaffRepository.class).findOne(Long.parseLong(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CFO_ID)));
        }
        return lastManager;
    }

    public String getFirstName() {
        if(firstName == null && name != null) {
            List<String> nameParts = Arrays.asList(name.split(" "));
            if(!nameParts.isEmpty()) {
                firstName = nameParts.get(0);
            }
        }

        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    @JsonIgnore
    public String getFirstLastName() {
        if (firstName == null || lastName == null) {
            if (name != null) {
                List<String> nameParts = Arrays.asList(name.split(" "));
                if (nameParts.size() >= 2) {
                    return nameParts.get(0) + " " + nameParts.get(nameParts.size() - 1);
                }
            }
            return name;
        } else {
            return firstName + " " + lastName;
        }
    }

    public void copyFromCandidate(PicklistItem nationality, PicklistItem country, String fullNameInArabic, String cityName, String fullAddress,String emergencyContactName, String emergencyContactPhoneNumber,
                                  List<TransferDestination> transferDestinations, List<OfficeStaffDocument> documents, String eidNumber,
                                  String jazzHRProfile, String firstName, String middleName, String lastName, CommunicationMethod preferredCommunicationMethod, java.sql.Date birthDate, String newEmail,Date potentialStartDate,AirfareTicketType airfareTicketType, List<PicklistItem> departments) {
        OfficeStaffDocumentRepository officeStaffDocumentRepository = Setup.getRepository(OfficeStaffDocumentRepository.class);
        TransferDestinationRepository transferDestinationRepository = Setup.getRepository(TransferDestinationRepository.class);
        if (nationality != null)
            this.nationality = nationality;
        if (country != null)
            this.country = country;
        this.name = firstName + " " + (Strings.isNullOrEmpty(middleName) ? "" : middleName) + " " + lastName;
        this.firstName = firstName;
        this.middleName = middleName;
        this.lastName = lastName;
        if (fullNameInArabic != null)
            this.fullNameInArabic = fullNameInArabic;
        if (cityName != null)
            this.cityName = cityName;
        if (fullAddress != null) {
            if (fullAddress.length() > 35) {
                throw new BusinessException("please limit the full address field to 35 characters");
            }
            this.fullAddress = fullAddress;
        }
        //...PAY-899...//
        this.emergencyContactName = emergencyContactName;
        this.emergencyContactPhoneNumber = emergencyContactPhoneNumber;

        if (eidNumber != null)
            this.eidNumber = eidNumber;
        if (jazzHRProfile != null)
            this.jazzHRProfile = jazzHRProfile;
        if (preferredCommunicationMethod != null)
            this.preferredCommunicationMethod = preferredCommunicationMethod;
        if (birthDate != null)
            this.birthDate = birthDate;
        if (newEmail != null) {
            this.newEmail = newEmail;
            this.email = newEmail;
        }
        if(potentialStartDate != null)
            this.potentialStartDate=potentialStartDate;
        if(airfareTicketType != null)
            this.airfareTicketType = airfareTicketType;

        for (TransferDestination transferDestination : transferDestinations) {
            if(transferDestination.getSelfReceiver() != null && transferDestination.getSelfReceiver())
                this.setCity(transferDestination.getCity());
            transferDestination.setOfficeStaff(this);
            transferDestinationRepository.save(transferDestination);
            this.transferDestinations.add(transferDestination);
        }
        this.setSelectedTransferDestination(transferDestinations.get(0));

        //PAY-1486 fix Bug
        if (OfficeStaffType.DUBAI_STAFF_EMARATI.equals(this.getEmployeeType()) ||
                OfficeStaffType.DUBAI_STAFF_EXPAT.equals(this.getEmployeeType())) {
            Setup.getApplicationContext().getBean(OfficestaffPayrollController.class).duplicateIBanDetection(this, this.getSelectedTransferDestination());
        }

        for (OfficeStaffDocument document : documents) {
            document.setOfficeStaff(this);
            officeStaffDocumentRepository.save(document);
            this.documents.add(document);
        }

        for (PicklistItem department : departments) {
            this.getDepartments().add(department);
        }
    }

    public String getUpdatedEmail() {
        return updatedEmail;
    }

    public void setUpdatedEmail(String updatedEmail) {
        this.updatedEmail = updatedEmail;
    }

    public PicklistItem getUpdatedCity() {
        return updatedCity;
    }

    public void setUpdatedCity(PicklistItem updatedCity) {
        this.updatedCity = updatedCity;
    }

    public String getFullAddress() {
        return fullAddress;
    }

    public void setFullAddress(String fullAddress) {
        this.fullAddress = fullAddress;
    }
    //...PAY-899...//
    public String getEmergencyContactName() {
        return emergencyContactName;
    }

    public void setEmergencyContactName(String emergencyContactName) {
        this.emergencyContactName = emergencyContactName;
    }

    public String getEmergencyContactPhoneNumber() {
        return emergencyContactPhoneNumber;
    }

    public void setEmergencyContactPhoneNumber(String emergencyContactPhoneNumber) {
        this.emergencyContactPhoneNumber = emergencyContactPhoneNumber;
    }

    public String getUpdatedFullAddress() {
        return updatedFullAddress;
    }

    public void setUpdatedFullAddress(String updatedFullAddress) {
        this.updatedFullAddress = updatedFullAddress;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public String getAccountHolderName() {
        return accountHolderName;
    }

    public void setAccountHolderName(String accountHolderName) {
        this.accountHolderName = accountHolderName;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getEidNumber() {
        return eidNumber;
    }

    public void setEidNumber(String eidNumber) {
        this.eidNumber = eidNumber;
    }

    public WorkingPattern getWorkingPattern() {
        return workingPattern;
    }

    public void setWorkingPattern(WorkingPattern workingPattern) {
        this.workingPattern = workingPattern;
    }

    public Boolean getVisaRequired() {
        return visaRequired;
    }

    public void setVisaRequired(Boolean visaRequired) {
        this.visaRequired = visaRequired;
    }

    public Double getSalary() {
        double components = 0.0;
        if(basicSalary != null) components += basicSalary;
        if(housingAllowance != null) components += housingAllowance;
        if(trasnportation != null) components += trasnportation;

        if(salary == 0.0 && components > 0.0) {
            salary = components;
        }
        return salary;
    }

    public void setSalary(Double salary) {
        this.salary = salary;
    }

    public boolean getDisableAll() {
        return this.terminationDate != null && this.terminationDate.before(new Date());
    }

    public boolean getDisableStartDate() {
        return this.startingDate != null && this.startingDate.before(new Date());
    }
    public OfficeStaffTerminationType getTerminationType() {
        return terminationType;
    }

    public void setTerminationType(OfficeStaffTerminationType terminationType) {
        this.terminationType = terminationType;
    }

    public Date getNoticePeriodStartDate() {
        return noticePeriodStartDate;
    }

    public void setNoticePeriodStartDate(Date noticePeriodStartDate) {
        this.noticePeriodStartDate = noticePeriodStartDate;
    }

    public Date getNoticePeriodEndDate() {
        return noticePeriodEndDate;
    }

    public void setNoticePeriodEndDate(Date noticePeriodEndDate) {
        this.noticePeriodEndDate = noticePeriodEndDate;
    }

    public Boolean getCreateCancel() {
        return createCancel;
    }

    public void setCreateCancel(Boolean createCancel) {
        this.createCancel = createCancel;
    }

    public PicklistItem getTerminationReason() {
        return terminationReason;
    }

    public void setTerminationReason(PicklistItem terminationReason) {
        this.terminationReason = terminationReason;
    }

    public VisaContractType getContractType() {
        return Setup.getApplicationContext().getBean(OfficeStaffFinalSettlementService.class)
                .getContractType(this);
    }

    public OfficeStaff getEmployeeManager() {
        return employeeManager;
    }

    public void setEmployeeManager(OfficeStaff employeeManager) {
        this.employeeManager = employeeManager;
    }

    public Boolean getSelfReceiver() {
        if(selfReceiver == null) {
            selfReceiver = updatedEnglishName == null;
        }
        return selfReceiver;
    }

    public void setSelfReceiver(Boolean selfReceiver) {
        this.selfReceiver = selfReceiver;
    }

    public ReceiveMoneyMethod getReceiveMoneyMethod() {
        return receiveMoneyMethod;
    }

    public void setReceiveMoneyMethod(ReceiveMoneyMethod receiveMoneyMethod) {
        this.receiveMoneyMethod = receiveMoneyMethod;
    }


    @Column
    private String updatedIban;

    @Column
    private String updatedAccountHolderName;

    @Column
    private String updatedAccountNumber;

    public String getUpdatedIban() {
        return updatedIban;
    }

    public void setUpdatedIban(String updatedIban) {
        this.updatedIban = updatedIban;
    }

    public String getUpdatedAccountHolderName() {
        return updatedAccountHolderName;
    }

    public void setUpdatedAccountHolderName(String updatedAccountHolderName) {
        this.updatedAccountHolderName = updatedAccountHolderName;
    }

    public String getUpdatedAccountNumber() {
        return updatedAccountNumber;
    }

    public void setUpdatedAccountNumber(String updatedAccountNumber) {
        this.updatedAccountNumber = updatedAccountNumber;
    }

    public List<OfficeStaffAccess> getExternalAccesses() {
        if (externalAccesses == null) {
            externalAccesses = new ArrayList<>();
        }
        return externalAccesses;
    }

    public void setExternalAccesses(List<OfficeStaffAccess> externalAccesses) {
        this.externalAccesses = externalAccesses;
    }

    public List<ExternalAccess> getEmployeeAccesses() {
        if (employeeAccesses == null) {
            employeeAccesses = new ArrayList<>();
        }
        return employeeAccesses;
    }

    public void setEmployeeAccesses(List<ExternalAccess> employeeAccesses) {
        this.employeeAccesses = employeeAccesses;
    }

    @JsonIgnore
    public String getAccessString() {
        return String.join(" - ",
                getExternalAccesses()
                        .stream()
                        .map(e
                                -> e.getAccess()
                                .getName())
                        .collect(Collectors.toList()));
    }

    public String getNearestCenter() {
        return nearestCenter;
    }

    public void setNearestCenter(String nearestCenter) {
        this.nearestCenter = nearestCenter;
    }

    public List<TransferDestination> getTransferDestinations() {
        if(transferDestinations == null)
            transferDestinations = new ArrayList<>();
        return transferDestinations;
    }

    public void setTransferDestinations(List<TransferDestination> transferDestinations) {
        this.transferDestinations = transferDestinations;
    }

    public TransferDestination getSelectedTransferDestination() {
        if(selectedTransferDestination == null && !getTransferDestinations().isEmpty()) {
            selectedTransferDestination = getTransferDestinations().get(0);
        }
        return selectedTransferDestination;
    }

    public void setSelectedTransferDestination(TransferDestination selectedTransferDestination) {
        this.selectedTransferDestination = selectedTransferDestination;
    }

    public String getJazzHRProfile() {
        return jazzHRProfile;
    }

    public void setJazzHRProfile(String jazzHRProfile) {
        this.jazzHRProfile = jazzHRProfile;
    }

    public Double getCompensation() {
        return compensation;
    }

    public void setCompensation(Double compensation) {
        this.compensation = compensation;
    }

    public OfficeStaffFinalSettlement getFinalSettlement() {
        return finalSettlement;
    }

    public void setFinalSettlement(OfficeStaffFinalSettlement finalSettlement) {
        this.finalSettlement = finalSettlement;
    }

    public OfficeStaff getWhoRequestedTermination() {
        return whoRequestedTermination;
    }

    public void setWhoRequestedTermination(OfficeStaff whoRequestedTermination) {
        this.whoRequestedTermination = whoRequestedTermination;
    }

    public void setLastPayrollLockDate(Date lastPayrollLockDate) {
        this.lastPayrollLockDate = lastPayrollLockDate;
    }

    public Boolean getWithMolNumber() {
        return withMolNumber;
    }

    public void setWithMolNumber(Boolean withMolNumber) {
        this.withMolNumber = withMolNumber;
    }

    public boolean isConfirmedHighSalaryByAuditor() {
        return confirmedHighSalaryByAuditor;
    }

    public void setConfirmedHighSalaryByAuditor(boolean confirmedHighSalaryByAuditor) {
        this.confirmedHighSalaryByAuditor = confirmedHighSalaryByAuditor;
    }

    public boolean isConfirmedTerminationCompensationByAuditor() {
        return confirmedTerminationCompensationByAuditor;
    }

    public void setConfirmedTerminationCompensationByAuditor(boolean confirmedTerminationCompensationByAuditor) {
        this.confirmedTerminationCompensationByAuditor = confirmedTerminationCompensationByAuditor;
    }

    @JsonIgnore
    public String getSalaryWithCurrency(){
        String salaryWithCurrency = "";
        Double salary = getSalary();

        if (getSalaryCurrency() != null){
            salaryWithCurrency += getSalaryCurrency().name() + " ";
        }

        if (salary != null){
            salaryWithCurrency += NumberFormatter.formatNumber(salary);
        }else{
            salaryWithCurrency = "0";
        }

        return salaryWithCurrency;
    }

    @JsonIgnore
    public String getSalaryWithCurrencyAfter(){
        String salaryWithCurrency = "";
        Double salary = getSalary();

        if (salary != null){
            salaryWithCurrency += NumberFormatter.formatNumber(salary);
        }else{
            salaryWithCurrency = "0";
        }

        if (getSalaryCurrency() != null && !"0".equals(salaryWithCurrency)){
            salaryWithCurrency += " " + getSalaryCurrency().name();
        }

        return salaryWithCurrency;
    }

    @JsonIgnore
    public String getWithCompanySince(){
        String withCompanySince = "";
        if (getStartingDate() != null){
            int diffYears = DateUtil.getDiffYears(getStartingDate(),new Date());
            int diffMonths = DateUtil.getDiffMonths(getStartingDate(),new Date())-12*diffYears;
            int diffDays = DateUtil.getDaysBetween(getStartingDate(),new Date());

            if (diffMonths == 1){
                withCompanySince= diffMonths+" month";
            }else if (diffMonths > 1){
                withCompanySince= diffMonths+ " months";
            }

            if (diffYears == 1){
                withCompanySince= diffYears+" year"+( diffMonths > 0 ? " and "+withCompanySince : "" );
            }else if (diffYears > 1){
                withCompanySince= diffYears+" years"+( diffMonths > 0 ? " and "+withCompanySince : "" );
            }

            if (diffMonths == 0 && diffYears==0 && diffDays>0){
                if (diffDays == 1){
                    withCompanySince = diffDays+" day";
                } else if (diffDays > 1){
                    withCompanySince= diffDays+ " days";
                }
            }

        }
        return withCompanySince;
    }


    public Double getOldInternetAllowance() {
        return oldInternetAllowance;
    }

    public void setOldInternetAllowance(Double oldInternetAllowance) {
        this.oldInternetAllowance = oldInternetAllowance;
    }

    public String getStatusReport(){
        if (status == null)
            return "";
        String removeUnderLine = status.toString().replace("_", " ");
        return Character.toUpperCase(removeUnderLine.charAt(0)) + removeUnderLine.substring(1).toLowerCase();
    }

    public String getStartDateReport(){
        if(startingDate != null)
            return new java.sql.Date(startingDate.getTime()).toString();
        else
            return "";

    }

    public Expense getPnlExpense() {
        if(this.pnlExpense == null)
            pnlExpense = Setup.getRepository(ExpenseRepository.class).findByCode(pnl);
        return pnlExpense;
    }
    public void setPnlExpense(Expense pnlExpense) {
        this.pnlExpense = pnlExpense;
        if(pnlExpense != null)
            this.pnl = pnlExpense.getCode();
    }

    @JsonIgnore
    public List<OfficeStaff> getAllLevelEmployees() {
        MessageTemplateService messageTemplateService = Setup.getApplicationContext().getBean(MessageTemplateService.class);

        List<OfficeStaff> employees = new ArrayList<>();
        if (messageTemplateService.isManager(this)){
            List<OfficeStaff> subEmployees = Setup.getRepository(OfficeStaffRepository.class).findByEmployeeManager(this);

            subEmployees = subEmployees.stream().filter(o -> !o.getId().equals(this.getId())).collect(Collectors.toList());

            if (subEmployees.size() > 0){
                employees.addAll(subEmployees);
                subEmployees.forEach( o ->{
                    employees.addAll(o.getAllLevelEmployees());
                });
            }
        }

        return employees;
    }

    @JsonIgnore
    public List<OfficeStaff> getAllLevelEmployeesForRoster() {

        List<OfficeStaff> employees = new ArrayList<>();
        List<OfficeStaff> subEmployees = Setup.getRepository(OfficeStaffRepository.class).findByEmployeeManager(this);

        subEmployees = subEmployees.stream().filter(o -> !o.getId().equals(this.getId())).collect(Collectors.toList());

        if (subEmployees.size() > 0) {
            employees.addAll(subEmployees);
            subEmployees.forEach(o -> {
                employees.addAll(o.getAllLevelEmployeesForRoster());
            });
        }

        return employees;
    }

    public Boolean getAllowEdit(){
        User currentUser = CurrentRequest.getUser();
        if (currentUser != null && currentUser.hasPosition("payroll_trustee")){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public Boolean getHasSalarySecurity(){
        SalariesAccessRepository repository = Setup.getRepository(SalariesAccessRepository.class);
        User user = CurrentRequest.getUser();

        if (repository.countByUserAndForOfficeStaffsTrue(user) > 0)
            return Boolean.TRUE;
        return Boolean.FALSE;
    }

    public Boolean getHasDocumentsSecurity(){
        SalariesAccessRepository repository = Setup.getRepository(SalariesAccessRepository.class);
        User user = CurrentRequest.getUser();

        if (repository.countByUserAndOfficeStaffDocumentsTrue(user) > 0)
            return Boolean.TRUE;
        return Boolean.FALSE;
    }

    public Boolean getHasMoneyTransferSecurity(){
        SalariesAccessRepository repository = Setup.getRepository(SalariesAccessRepository.class);
        User user = CurrentRequest.getUser();

        if (user != null && repository.countByUserAndChangeMoneyReceiverTrue(user) > 0)
            return Boolean.TRUE;
        return Boolean.FALSE;
    }

    public CommunicationMethod getPreferredCommunicationMethod() {
        return preferredCommunicationMethod;
    }

    public void setPreferredCommunicationMethod(CommunicationMethod preferredCommunicationMethod) {
        this.preferredCommunicationMethod = preferredCommunicationMethod;
    }

    public java.sql.Date getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(java.sql.Date birthDate) {
        this.birthDate = birthDate;
    }

    public String getNewEmail() {
        return newEmail;
    }

    public void setNewEmail(String newEmail) {
        this.newEmail = newEmail;
    }

    public String getTerminationNotes() {
        return terminationNotes;
    }

    public void setTerminationNotes(String terminationNotes) {
        this.terminationNotes = terminationNotes;
    }

    public Date getOldStartingDate() {
        return oldStartingDate;
    }

    public void setOldStartingDate(Date oldStartingDate) {
        this.oldStartingDate = oldStartingDate;
    }

    public Date getOldTerminationDate() {
        return oldTerminationDate;
    }

    public void setOldTerminationDate(Date oldTerminationDate) {
        this.oldTerminationDate = oldTerminationDate;
    }

    public Double getSalaryBeforeRehiring() {
        return salaryBeforeRehiring;
    }

    public void setSalaryBeforeRehiring(Double salaryBeforeRehiring) {
        this.salaryBeforeRehiring = salaryBeforeRehiring;
    }

    public Double getFixedPensionAmount() {
        return fixedPensionAmount;
    }

    public void setFixedPensionAmount(Double fixedPensionAmount) {
        this.fixedPensionAmount = fixedPensionAmount;
    }

    public Boolean getDoNotDeductContribution() {
        return doNotDeductContribution;
    }

    public void setDoNotDeductContribution(Boolean doNotDeductContribution) {
        this.doNotDeductContribution = doNotDeductContribution;
    }

//    @JsonIgnore
//    public double getLastBasicSalary(java.sql.Date date) {
//        HistorySelectQuery<OfficeStaff> query = new HistorySelectQuery<>(OfficeStaff.class);
//        query.filterBy("id", "=", this.getId());
//        query.filterBy("lastModificationDate", "<", date);
//        query.filterByChanged("basicSalary");
//        query.sortBy("lastModificationDate", false);
//
//        List<OfficeStaff> staffs = query.execute();
//        if(!staffs.isEmpty() && staffs.get(0).getBasicSalary() != null && !staffs.get(0).getBasicSalary().equals(0d)) {
//            return staffs.get(0).getBasicSalary();
//        }
//        return this.getBasicSalary() == null ? 0 : this.getBasicSalary();
//    }
//
//    @JsonIgnore
//    public double getLastHousing(java.sql.Date date) {
//        HistorySelectQuery<OfficeStaff> query = new HistorySelectQuery<>(OfficeStaff.class);
//        query.filterBy("id", "=", this.getId());
//        query.filterBy("lastModificationDate", "<", date);
//        query.filterByChanged("basicSalary");
//        query.sortBy("lastModificationDate", false);
//
//        List<OfficeStaff> staffs = query.execute();
//        if(!staffs.isEmpty() && staffs.get(0).getHousingAllowance() != null && !staffs.get(0).getHousingAllowance().equals(0d)) {
//            return staffs.get(0).getHousingAllowance();
//        }
//        return this.getHousingAllowance() == null ? 0 : this.getHousingAllowance();
//    }
//
//    @JsonIgnore
//    public double getLastTransportation(java.sql.Date date) {
//        HistorySelectQuery<OfficeStaff> query = new HistorySelectQuery<>(OfficeStaff.class);
//        query.filterBy("id", "=", this.getId());
//        query.filterBy("lastModificationDate", "<", date);
//        query.filterByChanged("basicSalary");
//        query.sortBy("lastModificationDate", false);
//
//        List<OfficeStaff> staffs = query.execute();
//        if(!staffs.isEmpty() && staffs.get(0).getTrasnportation() != null && !staffs.get(0).getTrasnportation().equals(0d)) {
//            return staffs.get(0).getTrasnportation();
//        }
//        return this.getTrasnportation() == null ? 0 : this.getTrasnportation();
//    }

    @JsonIgnore
    public String getShortName() {
        if (firstName == null || lastName == null) {
            List<String> nameParts = Arrays.asList(name.split(" "));
            if (nameParts.size() >= 2 && nameParts.get(nameParts.size() - 1).length() >= 1) {
                return nameParts.get(0) + " " + nameParts.get(nameParts.size() - 1).substring(0,1);
            }
            return name;
        } else {
            return firstName + " " + (lastName.length() >= 1 ? lastName.substring(0,1) : "");
        }
    }

    @JsonIgnore
    public String getManagerShortName() {
        String managerName = "";
        if(this.getManager() != null) {
            managerName = this.getManager().getName();
            List<String> nameParts = Arrays.asList(managerName.split(" "));
            if (nameParts.size() >= 2 && nameParts.get(nameParts.size() - 1).length() >= 1) {
                return nameParts.get(0) + " " + nameParts.get(nameParts.size() - 1).substring(0, 1);
            }
        }
        return managerName;
    }

    public String getMoneyReceiverName(){
        if(moneyReceiverName != null)
            return moneyReceiverName;
        return selectedTransferDestination != null ? selectedTransferDestination.getName() : this.name;
    }

    public void setMoneyReceiverName(String moneyReceiverName){
        this.moneyReceiverName = moneyReceiverName;
    }

    public java.sql.Date getStartDateFrom() {
        return startDateFrom;
    }

    public void setStartDateFrom(java.sql.Date startDateFrom) {
        this.startDateFrom = startDateFrom;
    }

    public java.sql.Date getStartDateTo() {
        return startDateTo;
    }

    public void setStartDateTo(java.sql.Date startDateTo) {
        this.startDateTo = startDateTo;
    }

    public java.sql.Date getTerminationDateFrom() {
        return terminationDateFrom;
    }

    public void setTerminationDateFrom(java.sql.Date terminationDateFrom) {
        this.terminationDateFrom = terminationDateFrom;
    }

    public java.sql.Date getTerminationDateTo() {
        return terminationDateTo;
    }

    public void setTerminationDateTo(java.sql.Date terminationDateTo) {
        this.terminationDateTo = terminationDateTo;
    }

    public String getStartDateFilter() {
        return startDateFilter;
    }

    public void setStartDateFilter(String startDateFilter) {
        this.startDateFilter = startDateFilter;
    }

    public String getTerminationDateFilter() {
        return terminationDateFilter;
    }

    public void setTerminationDateFilter(String terminationDateFilter) {
        this.terminationDateFilter = terminationDateFilter;
    }

    public Boolean getHasEidAttachments() {
        return hasEidAttachments;
    }

    public void setHasEidAttachments(Boolean hasEidAttachments) {
        this.hasEidAttachments = hasEidAttachments;
    }

    public Boolean getVisaRequestHasEidAttachments() {
        return visaRequestHasEidAttachments;
    }

    public void setVisaRequestHasEidAttachments(Boolean visaRequestHasEidAttachments) {
        this.visaRequestHasEidAttachments = visaRequestHasEidAttachments;
    }

    public Date getPotentialStartDate() {
        return potentialStartDate;
    }

    public void setPotentialStartDate(Date potentialStartDate) {
        this.potentialStartDate = potentialStartDate;
    }

    public PicklistItem getLocation() {
        return location;
    }

    public void setLocation(PicklistItem location) {
        this.location = location;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Boolean getOnOrBefore31thOct2023() {
        return onOrBefore31thOct2023;
    }

    public void setOnOrBefore31thOct2023(Boolean onOrBefore31thOct2023) {
        this.onOrBefore31thOct2023 = onOrBefore31thOct2023;
    }

    @JsonIgnore
    public String getPensionIBAN() {
        NewRequest newRequest = Setup.getRepository(NewVisaRequestRepository.class).findFirstByOfficeStaffOrderByCreationDateDesc(this);
        if (newRequest != null && newRequest.getNewEidNumber() != null) {
            return "AE008510" + newRequest.getNewEidNumber().replace("-", "").replace(" ", "");
        }
        return "";
    }

    public Date getExpatStartDate() {
        return expatStartDate;
    }

    public void setExpatStartDate(Date expatStartDate) {
        this.expatStartDate = expatStartDate;
    }

    public Boolean getOverseasToExpatSwitched() {
        return overseasToExpatSwitched;
    }

    public void setOverseasToExpatSwitched(Boolean overseasToExpatSwitched) {
        this.overseasToExpatSwitched = overseasToExpatSwitched;
    }

    public Double getSalaryAsOverseas() {
        return salaryAsOverseas;
    }

    public void setSalaryAsOverseas(Double salaryAsOverseas) {
        this.salaryAsOverseas = salaryAsOverseas;
    }

    public SalaryCurrency getInitialCurrency() {
        return initialCurrency;
    }

    public void setInitialCurrency(SalaryCurrency initialCurrency) {
        this.initialCurrency = initialCurrency;
    }

    public Date getSwitchingToExpatDate() {
        return switchingToExpatDate;
    }

    public void setSwitchingToExpatDate(Date switchingToExpatDate) {
        this.switchingToExpatDate = switchingToExpatDate;
    }

    public LocationEnum getLocationEnum() {
        return locationEnum;
    }

    public void setLocationEnum(LocationEnum locationEnum) {
        this.locationEnum = locationEnum;
    }

    public Boolean getReceiveBirthdaysForDirectTeamMembers() {
        return receiveBirthdaysForDirectTeamMembers;
    }

    public void setReceiveBirthdaysForDirectTeamMembers(Boolean receiveBirthdaysForDirectTeamMembers) {
        this.receiveBirthdaysForDirectTeamMembers = receiveBirthdaysForDirectTeamMembers;
    }

    public Boolean getReceiveBirthdaysForInDirectTeamMembers() {
        return receiveBirthdaysForInDirectTeamMembers;
    }

    public void setReceiveBirthdaysForInDirectTeamMembers(Boolean receiveBirthdaysForInDirectTeamMembers) {
        this.receiveBirthdaysForInDirectTeamMembers = receiveBirthdaysForInDirectTeamMembers;
    }

    public Boolean getReceiveBirthdaysForInDirect2TeamMembers() {
        return receiveBirthdaysForInDirect2TeamMembers;
    }

    public void setReceiveBirthdaysForInDirect2TeamMembers(Boolean receiveBirthdaysForInDirect2TeamMembers) {
        this.receiveBirthdaysForInDirect2TeamMembers = receiveBirthdaysForInDirect2TeamMembers;
    }

    public void setFirstLastName(String firstLastName) {
        this.firstLastName = firstLastName;
    }

    public String getZohoProfile() {
        return zohoProfile;
    }

    public void setZohoProfile(String zohoProfile) {
        this.zohoProfile = zohoProfile;
    }

    public String getZohoJobTitle() {
        return zohoJobTitle;
    }

    public void setZohoJobTitle(String zohoJobTitle) {
        this.zohoJobTitle = zohoJobTitle;
    }

    public String getZohoExactJobTitle() {
        return zohoExactJobTitle;
    }

    public void setZohoExactJobTitle(String zohoExactJobTitle) {
        this.zohoExactJobTitle = zohoExactJobTitle;
    }

    public List<PicklistItem> getDepartments() {
        if(departments == null)
            departments = new ArrayList<>();
        return departments;
    }

    public void setDepartments(List<PicklistItem> departments) {
        this.departments = departments;
    }

    @JsonIgnore
    public String getDepartmentNames() {
        if (departments == null || departments.isEmpty()) {
            return "";
        }

        return departments.stream()
                .map(PicklistItem::getName)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(" - "));
    }

    @JsonIgnore
    public Attachment getProfilePictureAttachment(){
        if (documents != null && !documents.isEmpty()) {
            for (OfficeStaffDocument doc : documents) {
                if (OfficeStaffDocumentType.PROFILE_PHOTO.equals(doc.getType())) {
                    return doc.getAttachment("PROFILE_PHOTO");
                }
            }
        }
        return null;
    }
}
