package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.projection.IdLabel;
import com.magnamedia.entity.AirfareTicketType;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.serializer.IdNameAmountSerializer;
import com.magnamedia.extra.LocationEnum;
import com.magnamedia.module.type.OfficeStaffStatus;
import com.magnamedia.module.type.OfficeStaffTodoType;
import com.magnamedia.module.type.WorkingPattern;
import org.springframework.beans.factory.annotation.Value;

import java.time.DayOfWeek;
import java.util.Date;
import java.util.List;

public interface OfficeStaffProfile {
    Long getId();
    boolean getDisableAll();
    boolean getDisableStartDate();
    IdLabel getEmployeeManager();
    String getFirstName();
    String getLastName();
    OfficeStaffStatus getStatus();
    String getPnl();
    IdLabel getJobTitle();
    String getEmail();
    String getPhoneNumber();
    Date getStartingDate();
    Date getExpatStartDate();
    List<DayOfWeek> getWeeklyOffDayList();
    IdLabel getNationality();
    WorkingPattern getWorkingPattern();
    String getJazzHRProfile();
    IdLabel getTeam();
    Expense getPnlExpense();
    java.sql.Date getBirthDate();
    Boolean getHasSalarySecurity();
    String getEmergencyContactName();
    String getEmergencyContactPhoneNumber();
    String getFullAddress();
    @JsonSerialize(using = IdNameAmountSerializer.class)
    AirfareTicketType getAirfareTicketType();
    IdLabel getLocation();
    User getUser();

    LocationEnum getLocationEnum();

    Date getPotentialStartDate();

    Boolean getOnOrBefore31thOct2023();

    String getZohoJobTitle();

    String getZohoExactJobTitle();

    String getZohoProfile();

    List<PicklistItem> getDepartments();
}
