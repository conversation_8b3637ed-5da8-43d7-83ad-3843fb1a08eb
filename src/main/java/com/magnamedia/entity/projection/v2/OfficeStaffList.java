package com.magnamedia.entity.projection.v2;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.serializer.IdLabelCodeNameListSerializer;
import com.magnamedia.module.type.OfficeStaffStatus;
import com.magnamedia.module.type.OfficeStaffType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.rest.core.config.Projection;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Creation Date 09/05/2020
 */
@Projection(types = {OfficeStaff.class})
public interface OfficeStaffList {

    static class Counter
    {
        static int i=1;
        public static void resetCounter()
        {
            i=1;
        }
    }

    void setRow(Integer value);

    default Integer getRow(){
        return Counter.i++;
    }


    Long getId();

    String getName();

    @Value("#{target.status != null ? target.status.toString() : \"\"}")
    String getStatus();

    @Value("#{target.jobTitle != null ? target.jobTitle.getName() : \"\"}")
    String getJobTitle();

    @Value("#{target.team != null ? target.team.getName() : \"\"}")
    String getTeam();

    @Value("#{target.employeeType != null ? target.employeeType.toString() : \"\"}")
    String getEmployeeType();

    //@Value("#{new java.sql.Date(target.getStartingDate().getTime())}")
    Date getStartingDate();

    @Value("#{target.employeeManager != null ? target.employeeManager.getName() : \"\"}")
    String getManager();

    String getMoneyReceiverName();

    Date getTerminationDate();

    String getZohoExactJobTitle();


    @JsonSerialize(using = IdLabelCodeNameListSerializer.class)
    List<PicklistItem> getDepartments();

}
