package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.google.api.client.util.Strings;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.serialize.IdSerializer;
import com.magnamedia.entity.serializer.IdLabelCodeNameListSerializer;
import com.magnamedia.entity.serializer.IdLabelCodeTagsSerializer;
import com.magnamedia.extra.LocationEnum;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.helper.StringHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.CommunicationMethod;
import com.magnamedia.module.type.OfficeStaffType;
import com.magnamedia.module.type.SalaryCurrency;
import com.magnamedia.module.type.WorkingPattern;
import com.magnamedia.repository.JobTitleManagerRepository;
import com.magnamedia.repository.OfficeStaffDocumentRepository;
import com.magnamedia.repository.TransferDestinationRepository;
import org.hibernate.envers.NotAudited;
import org.hibernate.validator.constraints.Email;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.DayOfWeek;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 5/9/2020
 **/
@Entity @org.hibernate.envers.Audited
public class OfficeStaffCandidate extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private AirfareTicketType airfareTicketType;


    @Column(columnDefinition = "boolean default false")
    private Boolean mockCandidate = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean rehireProcessCompleted = false;

    @NotAudited
    @Column
    private String name;

    @NotAudited
    @Column
    private String fullNameInArabic;

    @Column
    private String applicantId;

    @Column
    private String firstName;

    @Column
    private String middleName;

    @Column
    private String lastName;

    @Transient
    private String firstLastName;

    @Column
    private String source;

    @Column
    private String employeeName;

    @Column
    private String jazzHRJobTitle;

    @OneToMany(mappedBy = "officeStaffCandidate", fetch = FetchType.LAZY)
    List<OfficeStaffDocument> documents = new ArrayList<>();

    @Column
    private String jazzHRProfile;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem jobTitle;

    @Column
    private String phoneNumber;

    @Column
//    @Pattern(regexp = "^([_a-zA-Z0-9-]+(\\.[_a-zA-Z0-9-]+)*@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*(\\.[a-zA-Z]{1,6}))?$", message = "Please enter a valid email!")
    @Email(message = "Please enter a valid email!")
    private String email;

    @NotAudited
    @Enumerated(EnumType.STRING)
    private OfficeStaffType employeeType;

    @NotAudited
    @Enumerated(EnumType.STRING)
    private WorkingPattern workingPattern;

    @ElementCollection(targetClass= DayOfWeek.class)
    @Enumerated(EnumType.STRING)
    @Column(name="weeklyOffDay")
    protected List<DayOfWeek> weeklyOffDayList;

    @Column(columnDefinition = "boolean default false")
    private Boolean visaRequired = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean rehired = false;

    @Column
    private Long rehiredId;

    @Column
    private Date startingDate;

    @Column(columnDefinition = "double default 0")
    @NotNull
    private Double salary = 0d;

    @Enumerated(EnumType.STRING)
    private SalaryCurrency salaryCurrency;

    @Column(columnDefinition = "double default 0")
    @NotNull
    private Double basicSalary = 0d;

    @Column(columnDefinition = "double default 0")
    @NotNull
    private Double housing = 0d;

    @Column(columnDefinition = "double default 0")
    @NotNull
    private Double transportation = 0d;

    @OneToMany(mappedBy = "candidate", fetch = FetchType.LAZY)
    private List<OfficeStaffTodo> todoList;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem nationality;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeTagsSerializer.class)
    private PicklistItem country;

    @Column
    private String cityName;

    @NotAudited
    @Lob
    @Column
    private String fullAddress;
    //...PAY-899...//
    @Column
    private String emergencyContactName;

    @Column
    private String emergencyContactPhoneNumber ;

    @Column
    private String eidNumber;

    @NotAudited
    @Lob
    @Column
    private String nearestCenter;

    @OneToMany(mappedBy = "officeStaffCandidate", fetch = FetchType.LAZY)
    private List<TransferDestination> transferDestinations = new ArrayList<>();

    @OneToOne(fetch = FetchType.LAZY)
    private TransferDestination selectedTransferDestination;

    @Column(columnDefinition = "boolean default false")
    private Boolean isActionTaken = false;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem team;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private OfficeStaff employeeManager;

    @Enumerated(EnumType.STRING)
    private CommunicationMethod preferredCommunicationMethod;

    @Column
    private java.sql.Date birthDate;

    @Column
//    @Pattern(regexp = "^([_a-zA-Z0-9-]+(\\.[_a-zA-Z0-9-]+)*@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*(\\.[a-zA-Z]{1,6}))?$", message = "Please enter a valid email!")
    @Email(message = "Please enter a valid email!")
    private String newEmail;

    @Lob
    @Column
    private String notes;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem city;

    @Column
    private String zohoExactJobTitle;

    @ManyToMany(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeNameListSerializer.class)
    private List<PicklistItem> departments;

    @Column
    private Date expatStartDate;
    @Column(columnDefinition = "boolean default false")
    private Boolean overseasToExpatSwitched = false;

    @Column
    private String rejectionNotes;

    @Column
    private Date potentialStartDate;

    @Enumerated(EnumType.STRING)
    private LocationEnum locationEnum;


    @Column
    private String zohoApplicantId;
    @Column
    private String zohoJobTitle;
    @Column
    private String zohoProfile;

    @Transient
    private String offerLetterUUID;
    @Transient
    private String offerLetterFileName;

    public String getEmployeeName() {
        if(employeeName == null) {
            employeeName = this.firstName + " " + (Strings.isNullOrEmpty(this.middleName) ? "" : this.middleName) + " " +  this.lastName;
        }
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public List<OfficeStaffDocument> getDocuments() {
        if (documents == null)
            return new ArrayList<>();
        return documents;
    }

    public void setDocuments(List<OfficeStaffDocument> documents) {
        this.documents = documents;
    }

    public PicklistItem getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(PicklistItem jobTitle) {
        this.jobTitle = jobTitle;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public OfficeStaffType getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(OfficeStaffType employeeType) {
        this.employeeType = employeeType;
    }

    public WorkingPattern getWorkingPattern() {
        return workingPattern;
    }

    public void setWorkingPattern(WorkingPattern workingPattern) {
        this.workingPattern = workingPattern;
    }

    public List<DayOfWeek> getWeeklyOffDayList() {
        if(weeklyOffDayList == null)
            weeklyOffDayList = new ArrayList<>();
        return weeklyOffDayList;
    }

    public void setWeeklyOffDayList(List<DayOfWeek> weeklyOffDayList) {
        this.weeklyOffDayList = weeklyOffDayList;
    }

    public Boolean getVisaRequired() {
        return visaRequired;
    }

    public void setVisaRequired(Boolean visaRequired) {
        this.visaRequired = visaRequired;
    }

    public Date getStartingDate() {
        return startingDate;
    }

    public void setStartingDate(Date startingDate) {
        this.startingDate = startingDate;
    }

    public Double getSalary() {
        return salary;
    }

    public void setSalary(Double salary) {
        this.salary = salary;
    }

    public SalaryCurrency getSalaryCurrency() {
        return salaryCurrency;
    }

    public void setSalaryCurrency(SalaryCurrency salaryCurrency) {
        this.salaryCurrency = salaryCurrency;
    }

    public Double getBasicSalary() {
        return basicSalary;
    }

    public void setBasicSalary(Double basicSalary) {
        this.basicSalary = basicSalary;
    }

    public Double getHousing() {
        return housing;
    }

    public void setHousing(Double housing) {
        this.housing = housing;
    }

    public Double getTransportation() {
        return transportation;
    }

    public void setTransportation(Double transportation) {
        this.transportation = transportation;
    }

    @JsonIgnore
    public List<OfficeStaffTodo> getTodoList() {
        return todoList;
    }

    public void setTodoList(List<OfficeStaffTodo> todoList) {
        this.todoList = todoList;
    }

    public PicklistItem getNationality() {
        return nationality;
    }

    public void setNationality(PicklistItem nationality) {
        this.nationality = nationality;
    }

    public String getFullAddress() {
        return fullAddress;
    }

    public void setFullAddress(String fullAddress) {
        this.fullAddress = fullAddress;
    }
    //...PAY-899...//
    public String getEmergencyContactName() {
        return emergencyContactName;
    }

    public void setEmergencyContactName(String emergencyContactName) {
        this.emergencyContactName = emergencyContactName;
    }

    public String getEmergencyContactPhoneNumber() {
        return emergencyContactPhoneNumber;
    }

    public void setEmergencyContactPhoneNumber(String emergencyContactPhoneNumber) {
        this.emergencyContactPhoneNumber = emergencyContactPhoneNumber;
    }

    public String getEidNumber() {
        return eidNumber;
    }

    public void setEidNumber(String eidNumber) {
        this.eidNumber = eidNumber;
    }

    @JsonIgnore
    public OfficeStaff getFinalManager() {

        if (employeeManager == null) {
            if (jobTitle == null) return null;
            JobTitleManager titleManager = Setup.getRepository(JobTitleManagerRepository.class)
                    .findFirstByJobTitle(this.jobTitle);
            employeeManager = titleManager == null ? null : titleManager.getManager();
        }

        if (employeeManager != null) {
            if (employeeManager.getEmployeeManager() == null) {
                return employeeManager;
            } else {
                return employeeManager.getFinalManager();
            }
        }
        return null;
    }

    @JsonIgnore
    public OfficeStaff getFinalManagerNewHireCase() {
        if (jobTitle == null) return null;
        JobTitleManager titleManager = Setup.getRepository(JobTitleManagerRepository.class)
                .findFirstByJobTitle(this.jobTitle);
        return titleManager == null ? null :
                (titleManager.getManager().getEmployeeManager() == null ? titleManager.getManager() : titleManager.getManager().getFinalManager());
    }

    public double getBasicSalaryPercentage() {
        return Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_EXPAT_BASIC_SALARY_PERCENTAGE));
    }

    public double getHousingPercentage() {
        return Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_EXPAT_HOUSING_PERCENTAGE));
    }

    public double getTransportationPercentage() {
        return Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_EXPAT_TRANSPORTATION_PERCENTAGE));
    }

    public String getApplicantId() {
        return applicantId;
    }

    public void setApplicantId(String applicantId) {
        this.applicantId = applicantId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    @JsonIgnore
    public String getFirstLastName() {
        return firstName + " " + lastName;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getJazzHRJobTitle() {
        return jazzHRJobTitle;
    }

    public void setJazzHRJobTitle(String jazzHRJobTitle) {
        this.jazzHRJobTitle = jazzHRJobTitle;
    }

    public String getJazzHRProfile() {
        return jazzHRProfile;
    }

    public void setJazzHRProfile(String jazzHRProfile) {
        this.jazzHRProfile = jazzHRProfile;
    }

    public void copyFromOtherCandidate(PicklistItem nationality, PicklistItem country, String fullNameInArabic, String cityName, String fullAddress,String emergencyContactName, String emergencyContactPhoneNumber,
                                       List<TransferDestination> transferDestinations, List<OfficeStaffDocument> documents, String eidNumber, String firstName, String middleName, String lastName, OfficeStaff employeeManager, CommunicationMethod preferredCommunicationMethod, java.sql.Date birthDate, String newEmail, PicklistItem city) {
        OfficeStaffDocumentRepository officeStaffDocumentRepository = Setup.getRepository(OfficeStaffDocumentRepository.class);
        TransferDestinationRepository transferDestinationRepository = Setup.getRepository(TransferDestinationRepository.class);
        if (nationality != null)
            this.nationality = nationality;
        if (country != null)
            this.country = country;
        this.name = firstName + " " + middleName + " " + lastName;
        this.firstName = firstName;
        this.middleName = middleName;
        this.lastName = lastName;
        if (fullNameInArabic != null)
            this.fullNameInArabic = fullNameInArabic;
        if (cityName != null)
            this.cityName = cityName;
        if (city != null && city.getId() != null) {
            this.city = city;
            PicklistItem cityDB = Setup.getRepository(PicklistItemRepository.class).findOne(city.getId());
            if (cityDB != null) {
                this.cityName = cityDB.getName();
            }
        }
        if (fullAddress != null)
            this.fullAddress = fullAddress;
        //...PAY-899...//
        this.emergencyContactName = emergencyContactName;
        this.emergencyContactPhoneNumber = emergencyContactPhoneNumber;

        if (eidNumber != null)
            this.eidNumber = eidNumber;
        if (preferredCommunicationMethod != null)
            this.preferredCommunicationMethod = preferredCommunicationMethod;
        if (employeeManager != null)
            this.employeeManager = employeeManager;
        if (birthDate != null)
            this.birthDate = birthDate;
        if (newEmail != null)
            this.newEmail = newEmail;

        for (TransferDestination transferDestination : transferDestinations) {
            // self receiver must be true in case of Emarati or Expat
            Boolean selfReceiver = OfficeStaffType.OVERSEAS_STAFF.equals(this.employeeType) ? transferDestination.getSelfReceiver() : true;

            transferDestination.setOfficeStaffCandidate(this);
            transferDestination.setSelfReceiver(selfReceiver);
            transferDestinationRepository.save(transferDestination);
            this.transferDestinations.add(transferDestination);
        }

        // set selected transfer destination
        this.setSelectedTransferDestination(transferDestinations.get(0));
        this.documents = new ArrayList<>();
        for (OfficeStaffDocument document : documents) {
            document.setOfficeStaffCandidate(this);
            document = officeStaffDocumentRepository.save(document);
            this.documents.add(document);
        }
    }

    @PreUpdate
    @PrePersist
    public void validateSalary() {
        if (!this.getSalary().equals(this.getBasicSalary() + this.getHousing() + this.getTransportation()))
            throw new BusinessException("Salary must be equals to Basic Salary + Housing + Transportation");
    }

    public Boolean getMockCandidate() {
        return mockCandidate;
    }

    public void setMockCandidate(Boolean mockCandidate) {
        this.mockCandidate = mockCandidate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullNameInArabic() {
        return fullNameInArabic;
    }

    public void setFullNameInArabic(String fullNameInArabic) {
        this.fullNameInArabic = fullNameInArabic;
    }

    public PicklistItem getCountry() {
        return country;
    }

    public void setCountry(PicklistItem country) {
        this.country = country;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getNearestCenter() {
        return nearestCenter;
    }

    public void setNearestCenter(String nearestCenter) {
        this.nearestCenter = nearestCenter;
    }

    public List<TransferDestination> getTransferDestinations() {
        if(transferDestinations == null)
            transferDestinations = new ArrayList<>();
        return transferDestinations;
    }

    public void setTransferDestinations(List<TransferDestination> transferDestinations) {
        this.transferDestinations = transferDestinations;
    }

    public TransferDestination getSelectedTransferDestination() {
        return selectedTransferDestination;
    }

    public void setSelectedTransferDestination(TransferDestination selectedTransferDestination) {
        this.selectedTransferDestination = selectedTransferDestination;
    }

    public Boolean getActionTaken() {
        return isActionTaken;
    }

    public void setActionTaken(Boolean actionTaken) {
        isActionTaken = actionTaken;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public AirfareTicketType getAirfareTicketType() {
        return airfareTicketType;
    }

    public void setAirfareTicketType(AirfareTicketType airfareTicketType) {
        this.airfareTicketType = airfareTicketType;
    }

    public PicklistItem getTeam() {
        return team;
    }

    public void setTeam(PicklistItem team) {
        this.team = team;
    }

    public OfficeStaff getEmployeeManager() {
        if (employeeManager == null && jobTitle != null) {
            JobTitleManager jobTitleManager = Setup.getRepository(JobTitleManagerRepository.class)
                    .findFirstByJobTitle(jobTitle);
            employeeManager = jobTitleManager != null ? jobTitleManager.getManager() : employeeManager;
        }

        return employeeManager;
    }

    public void setEmployeeManager(OfficeStaff employeeManager) {
        this.employeeManager = employeeManager;
    }

    public CommunicationMethod getPreferredCommunicationMethod() {
        return preferredCommunicationMethod;
    }

    public void setPreferredCommunicationMethod(CommunicationMethod preferredCommunicationMethod) {
        this.preferredCommunicationMethod = preferredCommunicationMethod;
    }

    public java.sql.Date getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(java.sql.Date birthDate) {
        this.birthDate = birthDate;
    }

    public String getNewEmail() {
        return newEmail;
    }

    public void setNewEmail(String newEmail) {
        this.newEmail = newEmail;
    }

    public Boolean getRehired() {
        return rehired;
    }

    public void setRehired(Boolean rehired) {
        this.rehired = rehired;
    }

    public Long getRehiredId() {
        return rehiredId;
    }

    public void setRehiredId(Long rehiredId) {
        this.rehiredId = rehiredId;
    }

    public Boolean getRehireProcessCompleted() {
        return rehireProcessCompleted;
    }

    public void setRehireProcessCompleted(Boolean rehireProcessCompleted) {
        this.rehireProcessCompleted = rehireProcessCompleted;
    }

    @JsonIgnore
    public String getSalaryWithCurrency(){
        String salaryWithCurrency = "";
        Double salary = getSalary();

        if (getSalaryCurrency() != null){
            salaryWithCurrency += getSalaryCurrency().name() + " ";
        }

        if (salary != null){
            salaryWithCurrency += NumberFormatter.formatNumber(salary);
        }else{
            salaryWithCurrency = "0";
        }

        return salaryWithCurrency;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public PicklistItem getCity() {
        return city;
    }

    public void setCity(PicklistItem city) {
        this.city = city;
    }

    public Date getExpatStartDate() {
        return expatStartDate;
    }

    public void setExpatStartDate(Date expatStartDate) {
        this.expatStartDate = expatStartDate;
    }

    public Boolean getOverseasToExpatSwitched() {
        return overseasToExpatSwitched;
    }

    public void setOverseasToExpatSwitched(Boolean overseasToExpatSwitched) {
        this.overseasToExpatSwitched = overseasToExpatSwitched;
    }

    public String getRejectionNotes() {
        return rejectionNotes;
    }

    public void setRejectionNotes(String rejectionNotes) {
        this.rejectionNotes = rejectionNotes;
    }

    public Date getPotentialStartDate() {
        return potentialStartDate;
    }

    public void setPotentialStartDate(Date potentialStartDate) {
        this.potentialStartDate = potentialStartDate;
    }

    public LocationEnum getLocationEnum() {
        return locationEnum;
    }

    public void setLocationEnum(LocationEnum locationEnum) {
        this.locationEnum = locationEnum;
    }

    public String getZohoApplicantId() {
        return zohoApplicantId;
    }

    public void setZohoApplicantId(String zohoApplicantId) {
        this.zohoApplicantId = zohoApplicantId;
    }

    public void setFirstLastName(String firstLastName) {
        this.firstLastName = firstLastName;
    }

    public String getZohoJobTitle() {
        return zohoJobTitle;
    }

    public void setZohoJobTitle(String zohoJobTitle) {
        this.zohoJobTitle = zohoJobTitle;
    }

    public String getZohoExactJobTitle() {
        return zohoExactJobTitle;
    }

    public void setZohoExactJobTitle(String zohoExactJobTitle) {
        this.zohoExactJobTitle = zohoExactJobTitle;
    }

    public String getZohoProfile() {
        return zohoProfile;
    }

    public void setZohoProfile(String zohoProfile) {
        this.zohoProfile = zohoProfile;
    }

    public String getOfferLetterUUID() {
        Attachment offerLetterAttach = this.getAttachment("zoho_candidate_offer_letter");
        if(offerLetterAttach != null)
            return offerLetterAttach.getUuid();
        return offerLetterUUID;
    }

    public void setOfferLetterUUID(String offerLetterUUID) {
        this.offerLetterUUID = offerLetterUUID;
    }

    public String getOfferLetterFileName() {
        Attachment offerLetterAttach = this.getAttachment("zoho_candidate_offer_letter");
        if(offerLetterAttach != null)
            return offerLetterAttach.getName();
        return offerLetterFileName;
    }

    public void setOfferLetterFileName(String offerLetterFileName) {
        this.offerLetterFileName = offerLetterFileName;
    }

    public List<PicklistItem> getDepartments() {
        if(departments == null)
            departments = new ArrayList<>();
        return departments;
    }

    public void setDepartments(List<PicklistItem> departments) {
        this.departments = departments;
    }
}
