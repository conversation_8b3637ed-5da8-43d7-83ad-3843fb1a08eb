package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.OfficeStaffStatus;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.service.message.MessagingService;

import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 4/15/2021
 **/
public class OfficeStaffBirthDateReminderJob implements MagnamediaJob {

    @Override
    public void run(Map<String, ?> parameters) {
        OfficeStaffRepository officeStaffRepository = Setup.getRepository(OfficeStaffRepository.class);

        // Get list of officeStaff whose birthday is tomorrow
        List<OfficeStaff> officeStaffList = officeStaffRepository.getBirthDayTodayStaffs(OfficeStaffStatus.ACTIVE, DateUtil.addDays(new Date(System.currentTimeMillis()),1));

        Map<Long, List<OfficeStaff>> managerMap = new HashMap<>();

        OfficeStaff cooStaff = Setup.getRepository(OfficeStaffRepository.class).findOne(Long.parseLong(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CFO_ID)));

        String JadBirthDayEmail = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_BIRTHDAY_REMINDER_JAD_EMAIL);

        for (OfficeStaff staff : officeStaffList) {
            // Get the direct manager
            OfficeStaff directManager = staff.getEmployeeManager();
            if (directManager != null) {
                if (directManager.getReceiveBirthdaysForDirectTeamMembers()) {
                    sendBirthdayReminder(directManager, staff, JadBirthDayEmail, cooStaff, directManager.getName());
                    logger.log(Level.SEVERE, "Birthday Reminder sent to direct manager of office staff: " + staff.getName());
                }

                // Get indirect manager (manager of the direct manager)
                OfficeStaff indirectManager = directManager.getEmployeeManager();
                if (indirectManager != null && indirectManager.getReceiveBirthdaysForInDirectTeamMembers()) {
                    sendBirthdayReminder(indirectManager, staff, JadBirthDayEmail, cooStaff, directManager.getName());
                    logger.log(Level.SEVERE, "Birthday Reminder sent to indirect manager of office staff: " + staff.getName());
                }

                // Get the indirect-2 manager (manager of the indirect manager)
                if (indirectManager != null) {
                    OfficeStaff indirect2Manager = indirectManager.getEmployeeManager();
                    if (indirect2Manager != null && indirect2Manager.getReceiveBirthdaysForInDirect2TeamMembers()) {
                        sendBirthdayReminder(indirect2Manager, staff, JadBirthDayEmail, cooStaff, directManager.getName());
                        logger.log(Level.SEVERE, "Birthday Reminder sent to indirect-2 manager of office staff: " + staff.getName());
                    }
                }
            }
        }

    }

    private void sendBirthdayReminder(OfficeStaff receiverManager, OfficeStaff staff, String JadBirthDayEmail, OfficeStaff cooStaff, String directManagerName) {
        Map<String, String> paramValues = new HashMap<>();
        paramValues.put("employees_names", staff.getName());
        paramValues.put("employee_email", staff.getEmail());
        paramValues.put("direct_manager", directManagerName);
        paramValues.put("employee_mobile_number", staff.getPhoneNumber());
        paramValues.put("employee_first_name", staff.getFirstName());

        if (receiverManager != null) {

            String directManagerEmail = receiverManager.getEmail();

            if (cooStaff.getId().equals(receiverManager.getId()))
                directManagerEmail = JadBirthDayEmail;

            Setup.getApplicationContext().getBean(MessagingService.class).send("Payroll_OfficeStaffs_BirthDay_Reminder", "BirthDay Reminder SMS", null,
                    receiverManager, paramValues, null, null, null, directManagerEmail);
        }
    }
}
