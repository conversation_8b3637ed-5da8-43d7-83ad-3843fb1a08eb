package com.magnamedia.service;

import com.magnamedia.controller.PayrollManagerNoteApproveController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.helper.*;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.HousemaidRepository;
import com.magnamedia.repository.MonthlyPaymentRuleRepository;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.repository.PayrollManagerNoteRepository;
import com.magnamedia.service.payroll.generation.newVersion2.OfficeStaffPayrollPaymentServiceV2;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Date;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class ManagerNoteService {

    @Autowired
    private MonthlyPaymentRuleRepository monthlyPaymentRuleRepository;
    @Autowired
    private PayrollManagerNoteRepository payrollManagerNoteRepository;

    @Transactional
    public void applyManagerNote(PayrollManagerNote note) {
        OfficeStaff officeStaff = Setup.getRepository(OfficeStaffRepository.class).findOne(note.getOfficeStaff().getId());
        int sign = note.getNoteType().equals(AbstractPayrollManagerNote.ManagerNoteType.REDUCTION) ? -1 : 1;

        if (officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT) || officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI)) {
            officeStaff.setSalary(officeStaff.getSalary() + sign * note.getAmount());
            officeStaff.setBasicSalary(officeStaff.getBasicSalary() + sign * note.getBasicSalary());
            officeStaff.setTrasnportation(officeStaff.getTrasnportation() + sign * note.getTransportation());
            officeStaff.setHousingAllowance(officeStaff.getHousingAllowance() + sign * note.getHousing());
        } else {
            officeStaff.setSalary(officeStaff.getSalary() + sign * note.getAmount());
            officeStaff.setBasicSalary(officeStaff.getBasicSalary() + sign * note.getBasicSalary());
        }

        note.setApplied(true);
        Setup.getRepository(PayrollManagerNoteRepository.class).save(note);
        Setup.getRepository(OfficeStaffRepository.class).save(officeStaff);
    }

    @Transactional
    public void addFilipinaSalaryAdjustment(Long housemaidId, Double amount) {
        if (housemaidId == null) return;

        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(housemaidId);

        PayrollManagerNote note = new PayrollManagerNote();
        note.setNotFinal(false);
        note.setAmount(amount == null ? 500d : amount);
        note.setNoteDate(new java.sql.Date(System.currentTimeMillis()));
        note.setNoteReasone("Filipina Salary Adjustment");
        note.setHousemaid(housemaid);
        note.setNoteType(PayrollManagerNote.ManagerNoteType.ADDITION);

        PicklistItem salaryDispute =
                PicklistHelper.getItem(
                        PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                        "salary_dispute");
        note.setAdditionReason(salaryDispute);
        Setup.getRepository(PayrollManagerNoteRepository.class).save(note);
    }

    @Transactional
    public void addLowExchangeRateAdditionsForSyrianStaffs(){
        try {
            Date previousPayrollMonth = new Date(new LocalDate().withDayOfMonth(1).plusMonths(-1).toDate().getTime());
            SelectQuery<MonthlyPaymentRule> selectQuery = new SelectQuery<>(MonthlyPaymentRule.class);
            selectQuery.filterBy("payrollMonth", "=", previousPayrollMonth);
            selectQuery.filterBy("payrollType", "=", PayrollType.PRIMARY);
            selectQuery.filterBy("employeeTypeList", "MEMBER OF", PaymentRuleEmployeeType.OVERSEAS);
            selectQuery.filterBy("paymentMethod", "IN", Arrays.asList(PaymentRulePaymentMethod.ACCORDING_TO_EMPLOYEE_PROFILE));
            selectQuery.filterBy("singleHousemaid", "=", false);
            selectQuery.filterBy("singleOfficeStaff", "=", false);
            selectQuery.sortBy("paymentDate", true);
            List<MonthlyPaymentRule> rules = selectQuery.execute();

            MonthlyPaymentRule previousMonthlyPaymentRule = rules != null && rules.size() > 0 ? rules.get(0) : null;

            if (previousMonthlyPaymentRule != null) {
                Date payrollStart = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).getPayrollStartLockDate(previousMonthlyPaymentRule);
                Date payrollEnd = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).getPayrollEndLockDate(previousMonthlyPaymentRule);

                SelectQuery<OfficeStaffPayrollLog> query = new SelectQuery<>(OfficeStaffPayrollLog.class);
                query.filterBy("monthlyPaymentRule", "=", previousMonthlyPaymentRule);
                query.join("payrollAccountantTodo");
                query.filterBy("payrollAccountantTodo.taskName", "=", PayrollAccountantTodoType.INTERNATIONAL_TRANSFER.toString());
                query.filterBy("transferred", "=", true);

                List<OfficeStaffPayrollLog> transferredLogs = query.execute();
                transferredLogs = transferredLogs.stream().filter(x -> x.getDestinationOfTransfer() != null && x.getDestinationOfTransfer().toLowerCase().startsWith("syria")).collect(Collectors.toList());
                for (OfficeStaffPayrollLog officeStaffPayrollLog : transferredLogs) {
                    OfficeStaff officeStaff = officeStaffPayrollLog.getOfficeStaff();
                    if (OfficeStaffStatus.ACTIVE.equals(officeStaff.getStatus())) {
                        Double totalSalary = officeStaffPayrollLog.getTotalSalary();
                        PicklistItem lowExchangeItem = PicklistHelper.getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE, PayrollManagementModule.PICKLIST_ITEM_MANAGER_NOTE_LOW_EXCHANGE_RATE_COMPENSATION_ADDITION_CODE);
                        PayrollManagerNote previousMonthAddition = payrollManagerNoteRepository.findTopByOfficeStaffAndNoteTypeAndNoteDateGreaterThanEqualAndNoteDateLessThanAndAdditionReason(
                                officeStaff, AbstractPayrollManagerNote.ManagerNoteType.BONUS,
                                payrollStart, payrollEnd, lowExchangeItem);
                        if (previousMonthAddition != null) {
                            totalSalary -= previousMonthAddition.getAmount() != null ? previousMonthAddition.getAmount() : 0.0;
                        }

                        String additionPercentageST = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_SYRIAN_LOW_EXCHANGE_RATE_PERCENTAGE);
                        Double additionPercentage = additionPercentageST != null && !additionPercentageST.isEmpty() ? Double.parseDouble(additionPercentageST) : 0.0;
                        totalSalary = Math.round(totalSalary * additionPercentage / 100.0) * 1.0;

                        if (totalSalary > 0.0) {
                            PayrollManagerNote thisMonthAddition = payrollManagerNoteRepository.findTopByOfficeStaffAndNoteTypeAndNoteDateGreaterThanEqualAndNoteDateLessThanAndAdditionReason(
                                    officeStaff, AbstractPayrollManagerNote.ManagerNoteType.BONUS,
                                    payrollEnd, new java.util.Date(), lowExchangeItem);
                            if (thisMonthAddition == null) {
                                PayrollManagerNoteApprove payrollManagerNoteApprove = new PayrollManagerNoteApprove();
                                payrollManagerNoteApprove.setOfficeStaff(officeStaff);
                                payrollManagerNoteApprove.setAmount(totalSalary);
                                payrollManagerNoteApprove.setNoteDate(new Date(System.currentTimeMillis()));
                                payrollManagerNoteApprove.setNoteType(AbstractPayrollManagerNote.ManagerNoteType.BONUS);
                                payrollManagerNoteApprove.setAdditionReason(lowExchangeItem);
                                payrollManagerNoteApprove.setNoteReasone(additionPercentageST + "% low exchange rate compensation for " + StringHelper.enumToCapitalizedFirstLetter(DateUtil.formatSimpleMonth(previousPayrollMonth)) + " salary transfer");
                                Setup.getApplicationContext().getBean(PayrollManagerNoteApproveController.class).create(payrollManagerNoteApprove);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            Logger.getLogger(ManagerNoteService.class.getName()).log(Level.SEVERE, "Exception while addLowExchangeRateAdditionsForSyrianStaffs " + e.getMessage());
            throw e;
        }
    }

    @Transactional
    public Boolean createUnemploymentInsuranceDeductionForMaid(Long housemaidId, Double amount, Long reasonId, Date date, String noteReason) {
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(housemaidId);
        PicklistItem reason = Setup.getRepository(PicklistItemRepository.class).findOne(reasonId);
        PayrollManagerNote payrollManagerNote = new PayrollManagerNote();
        payrollManagerNote.setHousemaid(housemaid);
        payrollManagerNote.setAmount(amount);
        payrollManagerNote.setDeductionReason(reason);
        payrollManagerNote.setNoteDate(date);
        payrollManagerNote.setNoteReasone(noteReason);
        payrollManagerNote.setNoteType(AbstractPayrollManagerNote.ManagerNoteType.DEDUCTION);
        payrollManagerNoteRepository.save(payrollManagerNote);
        return true;
    }

    @Transactional
    public void addPensionDeductionPayrollManagerNote(OfficeStaff officeStaff, Date payrollMonth) {
        PicklistItem deductionReason
                = Setup.getRepository(PicklistItemRepository.class).findByListAndCodeIgnoreCase(
                Setup.getRepository(PicklistRepository.class).findByCode("DeductionReasons"),
                PicklistItem.getCode("pension_compensation"));

        PayrollManagerNote note = new PayrollManagerNote();
        note.setOfficeStaff(officeStaff);
        double percentageToDeduct;

        if (officeStaff.getOnOrBefore31thOct2023()) {
            try {
                percentageToDeduct = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_PENSION_DEDUCTION_PERCENTAGE_ON_OR_BEFORE_31_10_2023));
            }catch (Exception e){
                percentageToDeduct = 5.0;
            }
        } else {
            try {
                percentageToDeduct = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_PENSION_DEDUCTION_PERCENTAGE_AFTER_31_10_2023));
            }catch (Exception e){
                percentageToDeduct = 11.0;
            }
        }
        note.setAmount(officeStaff.getSalary() * (percentageToDeduct / 100.0));
        note.setNoteReasone("Auto Added Deduction due to Pension Compensation.");
        note.setNoteDate(new java.util.Date());
        note.setNoteType(PayrollManagerNote.ManagerNoteType.DEDUCTION);
        note.setDeductionReason(deductionReason);
        note.setNotFinal(false);
        note.setPayrollMonth(new Date(payrollMonth.getTime()));
        payrollManagerNoteRepository.save(note);
    }

    @Transactional
    public void addReducedOverstayFinesAddition(Housemaid housemaid , Double additionAmount){
        PayrollManagerNote payrollManagerNote = new PayrollManagerNote();
        payrollManagerNote.setHousemaid(housemaid);
        payrollManagerNote.setAmount(additionAmount);
        payrollManagerNote.setNoteDate(new java.sql.Date(System.currentTimeMillis()));
        payrollManagerNote.setNoteReasone("Overstay Fines Waived");
        payrollManagerNote.setNotFinal(false);
        payrollManagerNote.setNoteType(PayrollManagerNote.ManagerNoteType.ADDITION);
        PicklistItem salaryDispute = PicklistHelper.getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE, "salary_dispute");
        payrollManagerNote.setAdditionReason(salaryDispute);
        Setup.getRepository(PayrollManagerNoteRepository.class).save(payrollManagerNote);
    }

    public void addRetractedResignationRaise(Long housemaidId,Double raiseAmount){
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(housemaidId);
        if(housemaid != null && raiseAmount > 0.0){
            Double overTime = housemaid.getOverTime() != null? housemaid.getOverTime() : 0.0;
            housemaid.setOverTime(overTime + raiseAmount);
            Setup.getRepository(HousemaidRepository.class).save(housemaid);
        }
    }

}
