package com.magnamedia.service;

import com.aspose.words.Run;
import com.magnamedia.controller.*;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.mail.*;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.repository.WordTemplateRepository;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.accessmgmt.*;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.entity.projection.HousemaidPayslipProjection;
import com.magnamedia.entity.projection.PendingApprovalRequestCEOProjection;
import com.magnamedia.entity.projection.RepaymentProjection;
import com.magnamedia.extra.EmployeeType;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.extra.RepaymentStatus;
import com.magnamedia.helper.*;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.AccountantToDoService;
import com.magnamedia.service.payroll.generation.OfficeStaffFinalSettlementService;
import com.magnamedia.service.payslip.PayslipService;
import com.magnamedia.workflow.type.PayrollAccountantTodoManagerAction;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;


/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 5/11/2020
 **/
@Service
public class PublicPageService {
    protected static final Double EPS = 1E-5;
    public static String errorMessage = "";
    public final Map<String, String> pagesBodiesMap = new HashMap() {
        {
            put("Payroll_New_Hire_Approval", "Do you approve on hiring @employee_name@ as @job_title@ for a salary of @salary@ starting @start_date@?<br> Their manager is @direct_manager_name@, and their weekly day off is on @weekly_off_day@. <br> Employee type is @employee_type@. <br> Reason of Hiring: @notes@.");
            put("Payroll_New_Hire_Approval_From_OfficeStaff_List_Page", "Do you approve on hiring @employee_name@ as @job_title@ for a salary of @salary@ starting @start_date@?<br> Their manager is @direct_manager_name@, and their weekly day off is on @weekly_off_day@. <br> Employee type is @employee_type@. <br> Reason of Hiring: @notes@.");
            put("Payroll_New_Hire_Approval_Expat", "Do you approve on hiring @employee_name@ as @job_title@ for a salary of @salary@ starting @start_date@?<br> Their manager is @direct_manager_name@, and their weekly day off is on @weekly_off_day@. <br> Employee type is @employee_type@. <br> Reason of Hiring: @notes@.<br> Note that their salary breakdown is as follow:<br> Basic Salary: @basic_salary@  <br>Housing Allowance: @housing@  <br>Transportation: @transportation@");
            put("Payroll_Salary_Raise_Approval", "Do you approve on giving @employee_name@ a salary raise of @value@ to monthly salary starting from @note_date@? <br>Reason of Raise: @note_reason@ <br>Employee Type: @employee_type@ <br>Employee's manager: @manager@ <br>Job title: @job_title@ <br>Current Salary: @total_salary@");
            put("Payroll_Salary_Raise_Approval_Expat", "Do you approve on giving @employee_name@ a salary raise of @value@ to monthly salary starting from @note_date@? <br>Reason of Raise: @note_reason@ <br>Employee Type: @employee_type@ <br>Employee's manager: @manager@ <br>Job title: @job_title@ <br>Current Salary: @total_salary@.<br> Note that their salary breakdown is as follow:<br> Basic Salary: @basic_salary@  <br>Housing Allowance: @housing@  <br>Transportation: @transportation@");
            put("Payroll_Salary_Reduction_Approval", "Do you approve on reducing the monthly salary of @employee_name@ by @value@ starting from @note_date@? <br>Reason of Reduction: @note_reason@ <br>Employee Type: @employee_type@ <br>Employee's manager: @manager@ <br>Job title: @job_title@ <br>Employment Start Date: @start_date@ <br>Current Salary: @total_salary@");
            put("Payroll_Salary_Reduction_Approval_Expat", "Do you approve on reducing the monthly salary of @employee_name@ by @value@ starting from @note_date@? <br>Reason of Reduction: @note_reason@ <br>Employee Type: @employee_type@ <br>Employee's manager: @manager@ <br>Job title: @job_title@ <br>Employment Start Date: @start_date@ <br>Current Salary: @total_salary@.<br> Note that their salary breakdown is as follow:<br> Basic Salary: @basic_salary@  <br>Housing Allowance: @housing@  <br>Transportation: @transportation@");
            put("Payroll_Delete_Loan_Approval", "Do you agree on deleting the loan of @employee_name@ that is equal to @loan_amount@ on the date of @delete_loan_date@ ?");
            put("Payroll_Salary_Deduction_Approval", "Do you agree on deducting @value@ from @note_date@ salary of @employee_name@? <br>Reason of Deduction: @note_reason@ <br>Employee Type: @employee_type@ <br>Employee's manager: @manager@ <br>Job title: @job_title@ <br>Employment Start Date: @start_date@ <br>Current Salary: @total_salary@.");
            put("Payroll_Salary_Deduction_Approval_Expat", "Do you agree on deducting @value@ from @note_date@ salary of @employee_name@? <br>Reason of Deduction: @note_reason@ <br>Employee Type: @employee_type@ <br>Employee's manager: @manager@ <br>Job title: @job_title@ <br>Employment Start Date: @start_date@ <br>Current Salary: @total_salary@.<br> Note that their salary breakdown is as follow:<br> Basic Salary: @basic_salary@  <br>Housing Allowance: @housing@  <br>Transportation: @transportation@");
            put("Payroll_Bonus_Approval", "Do you agree on giving @employee_name@ a bonus of @value@ to @note_date@ salary? <br>Reason of Bonus: @note_reason@ <br>Employee Type: @employee_type@ <br>Employee's manager: @manager@ <br>Job title: @job_title@ <br>Employment Start Date: @start_date@ <br>Current Salary: @total_salary@.");
            put("Payroll_Bonus_Approval_Expat", "Do you agree on giving @employee_name@ a bonus of @value@ to @note_date@ salary? <br>Reason of Bonus: @note_reason@ <br>Employee Type: @employee_type@ <br>Employee's manager: @manager@ <br>Job title: @job_title@ <br>Employment Start Date: @start_date@ <br>Current Salary: @total_salary@.<br> Note that their salary breakdown is as follow:<br> Basic Salary: @basic_salary@  <br>Housing Allowance: @housing@  <br>Transportation: @transportation@");
            put("Payroll_Edit_Loan_Approval", "Do you agree on changing the loan amount of @employee_name@ from @old_value@ to @new_value@ with a repayment amount of @repayment_amount@ on @edit_loan_date@ ?");
            put("Payroll_Change_Loan_repayments_Approval", "Do you agree on changing the loan repayment amount of @employee_name@ that is related to the loan given on @related_loan_date@ of amount @initial_related_loan_amount@ from @old_value@ to @new_value@ starting from @edit_loan_repayment_payroll_month@? <br>Employee Type: @employee_type@ <br>Employee's manager: @manager@ <br>Job title: @job_title@ <br>Employment Start Date: @start_date@ <br>Current Salary: @total_salary@.");
            put("Payroll_Change_Loan_repayments_Approval_Expat", "Do you agree on changing the loan repayment amount of @employee_name@ that is related to the loan given on @related_loan_date@ of amount @initial_related_loan_amount@ from @old_value@ to @new_value@ starting from @edit_loan_repayment_payroll_month@? <br>Employee Type: @employee_type@ <br>Employee's manager: @manager@ <br>Job title: @job_title@ <br>Employment Start Date: @start_date@ <br>Current Salary: @total_salary@.<br> Note that their salary breakdown is as follow:<br> Basic Salary: @basic_salary@  <br>Housing Allowance: @housing@  <br>Transportation: @transportation@");
            put("Payroll_Add_Loan_Approval", "Do you agree on adding a loan of amount @value@ to @employee_name@ on @add_loan_date@ with a monthly repayment of @repayment_amount@? <br>Notes: @notes@ <br>Employee Type: @employee_type@ <br>Employee's manager: @manager@ <br>Job title: @job_title@ <br>Employment Start Date: @start_date@ <br>Current Salary: @total_salary@.");
            put("Payroll_Add_Loan_Approval_Expat", "Do you agree on adding a loan of amount @value@ to @employee_name@ on @add_loan_date@ with a monthly repayment of @repayment_amount@? <br>Notes: @notes@ <br>Employee Type: @employee_type@ <br>Employee's manager: @manager@ <br>Job title: @job_title@ <br>Employment Start Date: @start_date@ <br>Current Salary: @total_salary@.<br> Note that their salary breakdown is as follow:<br> Basic Salary: @basic_salary@  <br>Housing Allowance: @housing@  <br>Transportation: @transportation@");
            put("Payroll_Add_PaidOffDays_Approval", "Do you agree on adding a paid off days of amount @value@ to @employee_name@ on @add_paidOffDays_date@? <br>Employee Type: @employee_type@ <br>Employee's manager: @manager@ <br>Job title: @job_title@ <br>Employment Start Date: @start_date@ <br>Current Salary: @total_salary@.");
            put("Payroll_Add_PaidOffDays_Approval_Expat", "Do you agree on adding a paid off days of amount @value@ to @employee_name@ on @add_paidOffDays_date@? <br>Employee Type: @employee_type@ <br>Employee's manager: @manager@ <br>Job title: @job_title@ <br>Employment Start Date: @start_date@ <br>Current Salary: @total_salary@.<br> Note that their salary breakdown is as follow:<br> Basic Salary: @basic_salary@  <br>Housing Allowance: @housing@  <br>Transportation: @transportation@");
            put("Payroll_Update_OfficeStaff_Approval", "Do you approve on hiring @employee_name@ as @job_title@ for a salary of @salary@ starting @start_date@ ?");
            put("Payroll_Edit_Loan_Repayment_Approval", "Do you agree on changing the loan repayment amount of @employee_name@ from @old_value@ to @new_value@ for the month of @edit_loan_repayment_payroll_month@? <br>Employee Type: @employee_type@ <br>Employee's manager: @manager@ <br>Job title: @job_title@");
            put("Payroll_Roster_Approval", "Please click on approve if you have no changes to apply on your employees salaries, or click on send it back for the payroll manager in case you have any changes to be applied for this payroll month.");
            put("Payroll_Question_About_Maid_Salary", "Maid's name: @maid_name@<br>Maid's salary: @amount@<br>Maid's Nationality: @nationality@<br>Maid's Start date: @start_date@<br>Active client: @client_name@<br>Auditor's Question: @auditor_notes@<br>");
            put("Payroll_Pay_Loan_Repayment_Approval","Please approve the loan repayment of @month@ of amount @currency@ @amount@ that is requested by @user@ on the date of @request_date@. The current loan balance is @loan_balance_before_repayment@. <br>Payroll Manager Notes: <b>@pay_repayment_notes@</b>");
            put("Payroll_Mark_Unpaid_Approval", "The processed wire transfer salary of @payroll_date@ for @employee_name@, of the amount of @salary_amount@, was requested to be marked as unpaid.<br><br>Please review the notes below of the requestor, and take the necessary action:<br>@notes@");
        }
    };
    private Logger logger = Logger.getLogger(PublicPageService.class.getName());

    @Autowired
    FinalSettlementNoteRepository finalSettlementNoteRepository ;
    @Autowired
    private OfficeStaffTodoRepository officeStaffTodoRepository;
    @Autowired
    private OfficeStaffCandidateRepository officeStaffCandidateRepository;
    @Autowired
    private OfficeStaffRepository officeStaffRepository;
    @Autowired
    private PublicPageHelper publicPageHelper;
    @Autowired
    private MailService mailService;
    @Autowired
    private PayrollManagerNoteApproveRepository payrollManagerNoteApproveRepository;
    @Autowired
    private PayrollManagerNoteRepository PayrollManagerNoteRepository;
    @Autowired
    private EmployeeLoanApproveRepository employeeLoanApproveRepository;
    @Autowired
    private PaidOffDaysController paidOffDaysController;
    @Autowired
    private PaidOffDaysRepository paidOffDaysRepository;
    @Autowired
    private EmployeeLoanRepository employeeLoanRepository;
    @Autowired
    private EmployeeLoanService employeeLoanService;
    @Autowired
    private PublicPageService publicPageService;
    @Autowired
    private RepaymentRepository repaymentRepository;
    @Autowired
    private LoansController loansController;
//    @Autowired
//    private ManageAccessEmailService manageAccessEmailService;
//    @Autowired
//    private ExternalAccessController externalAccessController;
    @Autowired
    private OfficeStaffFinalSettlementService officeStaffFinalSettlementService;
    @Autowired
    private UpdateOfficeStaffApproveRepository updateOfficeStaffApproveRepository;
    @Autowired
    private Shortener shortener;
    @Autowired
    private OfficeStaffFinalSettlementController officeStaffFinalSettlementController;
    @Autowired
    private AccountantToDoService accountantToDoService;
    @Autowired
    private MonthlyPaymentRuleRepository monthlyPaymentRuleRepository;

    @Autowired
    private MessagingService messagingService;

    @Autowired
    private PayrollRosterApproveRequestRepository payrollRosterApproveRequestRepository;

    @Autowired
    private PayrollNotificationsService notificationsService;

    @Autowired
    WordTemplateRepository wordTemplateRepository;

    @Autowired
    WordTemplateService wordTemplateService;

    @Autowired
    private PayslipService payslipService;
//    @Autowired
//    private RevokeAccessNotifyMailRepository revokeAccessNotifyMailRepository;
    @Autowired
    private PendingApprovalRequestRepository pendingApprovalRequestRepository;

    @Autowired
    OfficeStaffFinalSettlementRepository finalSettlementRepository;

    @Autowired
    UserRepository userRepository;

    public static String generateBodyMessageText(String pageGeneralBody, Map<String, String> paramValues) {
        for (Map.Entry<String, String> entry : paramValues.entrySet())
            pageGeneralBody = pageGeneralBody.replace("@" + entry.getKey() + "@", entry.getValue() != null ? entry.getValue() : "");

        return pageGeneralBody;
    }

    /**
     * get a map of FinalManagerApprovePage according to the needed case from received token
     *
     * @param tokenLoad
     * @return Map<String, String>
     * @throws Exception
     */
    public Map<String, Object> getFinalManagerApprovePageInfo(String tokenLoad) throws RuntimeException {
        Map<String, Object> infoMap = new HashMap<>();
        String[] tokenLoadParts = tokenLoad.split("#");
        if (tokenLoadParts.length != 2)
            throw new RuntimeException(errorMessage = "missing data in the token.");
        String entityId = tokenLoadParts[0];
        String messageTemplateCode = tokenLoadParts[1];

        switch (messageTemplateCode) {
            case "Payroll_Mark_Unpaid_Approval":
                infoMap.put("title", "Mark Payroll Unpaid Approval");
                infoMap.put("body", this.getPayrollMarkUnpaidBody(entityId, messageTemplateCode));
                break;
            case "Payroll_New_Hire_Approval":
                infoMap.put("title", "New Hire Approval");
                infoMap.put("body", this.getNewHireApprovalPageBody(entityId, messageTemplateCode));
                break;
            case "Payroll_New_Hire_Approval_From_OfficeStaff_List_Page":
                infoMap.put("title", "New Hire Approval");
                infoMap.put("body", this.getNewHireApprovalPageBodyFromOfficeStaffListPage(entityId, messageTemplateCode));
                break;
            case "Payroll_Update_OfficeStaff_Approval":
                infoMap.put("body", this.getOfficeStaffUpdatePageBody(entityId, messageTemplateCode));
                UpdateOfficeStaffApprove approve = updateOfficeStaffApproveRepository
                        .findOne(Long.parseLong(entityId));
                infoMap.put("title", approve.getUpdateType() == UpdateOfficeStaffApprove.UpdateType.SALARY ?
                        "Changing Salary Approval" : "Changing Starting Date Approval");
                break;
            case "Payroll_Salary_Raise_Approval":
                infoMap.put("title", "Salary Raise Approval");
                infoMap.put("body", this.getManagerNoteApprovalPageBody(entityId, messageTemplateCode));
                infoMap.put("needRejectionNotes", true);
                break;
            case "Payroll_Salary_Deduction_Approval":
                infoMap.put("title", "Salary Deduction Approval");
                infoMap.put("body", this.getManagerNoteApprovalPageBody(entityId, messageTemplateCode));
                infoMap.put("needRejectionNotes", true);
                break;
            case "Payroll_Bonus_Approval":
                infoMap.put("title", "Bonus Approval");
                infoMap.put("body", this.getManagerNoteApprovalPageBody(entityId, messageTemplateCode));
                infoMap.put("needRejectionNotes", true);
                break;
            case "Payroll_Salary_Reduction_Approval":
                infoMap.put("title", "Salary Reduction Approval");
                infoMap.put("body", this.getManagerNoteApprovalPageBody(entityId, messageTemplateCode));
                infoMap.put("needRejectionNotes", true);
                break;
            case "Payroll_Add_Loan_Approval":
                infoMap.put("title", "Add Loan Approval");
                infoMap.put("body", this.getAddLoanApprovalPageBody(entityId, messageTemplateCode));
                infoMap.put("needRejectionNotes", true);
                break;
            case "Payroll_Edit_Loan_Approval":
                infoMap.put("title", "Edit Loan Approval");
                infoMap.put("body", this.getEditLoanApprovalPageBody(entityId, messageTemplateCode));
                infoMap.put("needRejectionNotes", true);
                break;
            case "Payroll_Change_Loan_repayments_Approval":
                infoMap.put("title", "Edit All Loan Repayments Approval");
                infoMap.put("body", this.getChangeLoanRepaymentsApprovalPageBody(entityId, messageTemplateCode));
                infoMap.put("needRejectionNotes", true);
                break;
            case "Payroll_Delete_Loan_Approval":
                infoMap.put("title", "Delete Loan Approval");
                infoMap.put("body", this.getDeleteLoanApprovalPageBody(entityId, messageTemplateCode));
                infoMap.put("needRejectionNotes", true);
                break;
            case "Payroll_Edit_Loan_Repayment_Approval":
                infoMap.put("title", "Edit Loan Repayment Approval");
                infoMap.put("body", this.getEditLoanRepaymentApprovalPageBody(entityId, messageTemplateCode));
                infoMap.put("needRejectionNotes", true);
                break;
            case "Payroll_Add_PaidOffDays_Approval":
                infoMap.put("title", "Add Paid Off Days Approval");
                infoMap.put("body", this.getAddPaidOffDaysApprovalPageBody(entityId, messageTemplateCode));
                infoMap.put("needRejectionNotes", true);
                break;
            case "switch_to_expat":
                infoMap.put("title", "OverSeas Switching to Expat");
                infoMap.put("body", " ");
                infoMap.put("needRejectionNotes", true);
                break;
            case "Payroll_Roster_Approval":
                return this.payrollRosterPage(entityId, messageTemplateCode);
            case "Payroll_Payment_Approve":
                return getPaymentProcessApproveMetaInfo(entityId);
            case "Payroll_Payment_Approve_For_Bank_Transfer":
                return getPaymentProcessApproveMetaInfoForBankTransfer(entityId);
            case "Payroll_Payment_Approve_By_CFO":
                return getPaymentProcessApproveByCFOMetaInfo(entityId);
            case "Payroll_Pay_Loan_Repayment_Approval":
                return getPaymentLoanApproveMetaInfo(entityId);

            default:
                throw new RuntimeException(errorMessage = "missing data in the token.");
        }

        return infoMap;
    }

    private Map<String, Object> getPaymentLoanApproveMetaInfo(String entityId) {
        String[] tokenLoadParts = entityId.split(":");
        if (tokenLoadParts.length != 2)
            throw new RuntimeException(errorMessage = "missing data in the token.");
        String id = tokenLoadParts[0];
        String repaymentDateString = tokenLoadParts[1];
        Date repaymentDate = null;
        try {
            repaymentDate = DateUtil.parseDateDashed(repaymentDateString);
        } catch (ParseException e) {
            throw new RuntimeException(errorMessage = " Action is already taken");
        }
        OfficeStaff officeStaff = officeStaffRepository.findOne(Long.parseLong(id));
        if (officeStaff == null || repaymentDate == null){
            throw new RuntimeException(errorMessage = " Action is already taken");
        }

        RepaymentProjection repaymentProjection = repaymentRepository.getRepaymentsByOfficestaffAndRepaymentDateBetween(Long.parseLong(id) , DateUtil.getStartDayValue(repaymentDate), DateUtil.getEndDayValue(repaymentDate));
        if (repaymentProjection == null || repaymentProjection.getLastId() == null){
            throw new RuntimeException(errorMessage = " Action is already taken");
        }
        Repayment repayment = repaymentRepository.findOne(repaymentProjection.getLastId());
        if (repayment == null || !RepaymentStatus.Pending_Approval.equals(repayment.getStatus())){
            throw new RuntimeException(errorMessage = " Action is already taken");
        }
        String month = repaymentProjection.getRepaymentDate() != null ? DateUtil.getMonthForInt(repaymentProjection.getRepaymentDate().getMonth()) : "";
        String title = "Approve Loan repayment of "+ month +" for "+officeStaff.getName();
        Map<String, String> params = new HashMap<>();
        params.put("month", month);
        params.put("currency", repaymentProjection.getSalaryCurrency() != null ? repaymentProjection.getSalaryCurrency() : "");
        params.put("amount", repaymentProjection.getAmount() != null ? NumberFormatter.formatNumber(repaymentProjection.getAmount()) : "");
        params.put("user", repayment.getLastModifier() != null ? repayment.getLastModifier().getName() : "");
        params.put("request_date", repayment.getLastModificationDate() != null ? DateUtil.formatDateDashedWithTime(repayment.getLastModificationDate()) : "");
        params.put("loan_balance_before_repayment", officeStaff.getLoanBalance() != null ? NumberFormatter.formatNumber(officeStaff.getLoanBalance()) : "");
        params.put("pay_repayment_notes", repayment.getNotes() != null ? repayment.getNotes() : "");
        String pageGeneralBody = pagesBodiesMap.get("Payroll_Pay_Loan_Repayment_Approval");
        String pageBodyText = this.generateBodyMessageText(pageGeneralBody, params);

        Map<String, Object> body = new HashMap<>();
        body.put("title", title);
        body.put("body", pageBodyText);

        return body;

    }

    private Map<String, Object> getPaymentProcessApproveMetaInfo(String entityId) {
        PayrollAccountantTodo todo = Setup.getRepository(PayrollAccountantTodoRepository.class)
                .findOne(Long.parseLong(entityId));

        if (todo != null) {

            if (todo.getActionTaken() || todo.getFirstActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }
            Map<String, Object> body = new HashMap<>();

            body.put("title", " Approve Payment Files Before Sending to Ansari");
            body.put("body", "Please approve on sending the attached payment file to Ansari if you agree on all the salaries listed in it.");
            return body;
        } else
            throw new RuntimeException("Can't find related accountant todo!");
    }

    private Map<String, Object> getPaymentProcessApproveMetaInfoForBankTransfer(String entityId) {
        PayrollAccountantTodo todo = Setup.getRepository(PayrollAccountantTodoRepository.class)
                .findOne(Long.parseLong(entityId));

        if (todo != null) {

            if (todo.getActionTaken() || todo.getFirstActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }
            Map<String, Object> body = new HashMap<>();

            body.put("title", " Approve Bank Transfer File Before Transferring Money");
            body.put("body", "Please approve on the bank transfers file related to overseas staff  if you agree on all the salaries listed in it.");
            return body;
        } else
            throw new RuntimeException("Can't find related accountant todo!");
    }

    private Map<String, Object> getPaymentProcessApproveByCFOMetaInfo(String entityId) {
        PayrollAccountantTodo todo = Setup.getRepository(PayrollAccountantTodoRepository.class)
                .findOne(Long.parseLong(entityId));

        if (todo != null) {

            if (todo.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }
            Map<String, Object> body = new HashMap<>();

            body.put("title", " Approve the File Sent to Ansari Before Transferring Money");
            body.put("body", "Please confirm the " + (todo.getTaskName().equals("WPS") ? "WPS" : StringHelper.enumToCapitalizedFirstLetter(todo.getTaskName())) + " file sent to " +
                    Setup.getParameter(Setup.getCurrentModule(),PayrollManagementModule.PARAMETER_MONEY_EXCHANGE_NAME) +
                    " of amount " + (!todo.getTaskName().equals("INTERNATIONAL_TRANSFER") ? "AED "+NumberFormatter.formatNumber(todo.getTotal()) : NumberFormatter.formatNumber(todo.getTotal()))
                    +" for the accountant to transfer the money.");
            return body;
        } else
            throw new RuntimeException("Can't find related accountant todo!");
    }


    private Object getManagerNoteApprovalPageBody(String managerNoteId, String messageTemplateCode) throws RuntimeException {
        String pageBodyText = "";

        PayrollManagerNoteApprove managerNote = payrollManagerNoteApproveRepository.findOne(Long.parseLong(managerNoteId));

        if (managerNote != null) {

            if (managerNote.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }

            OfficeStaff officeStaff = officeStaffRepository.getOne(managerNote.getOfficeStaff().getId());
            Date date = PayrollGenerationLibrary.getPayrollEndDate(new LocalDate(managerNote.getNoteDate())).toDate();

            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("employee_name", managerNote.getOfficeStaff().getFirstLastName());
            paramValues.put("value", NumberFormatter.formatNumber(managerNote.getAmount()) + " " + managerNote.getOfficeStaff().getSalaryCurrency());
            paramValues.put("note_date", DateUtil.formatSimpleMonthYear(date));
            paramValues.put("note_reason", managerNote.getNoteReasone() != null ? managerNote.getNoteReasone() : "__");
            paramValues.put("employee_type", officeStaff.getEmployeeType().getLabel());
            paramValues.put("manager", officeStaff.getEmployeeManager() != null ? officeStaff.getEmployeeManager().getShortName() : "__");
            paramValues.put("start_date", DateUtil.formatClientFullDate(officeStaff.getStartingDate()));
            paramValues.put("job_title", officeStaff.getJobTitle() != null ? officeStaff.getJobTitle().getName() : "__");
            paramValues.put("total_salary", officeStaff.getSalaryWithCurrencyAfter());
            if (officeStaff.getEmployeeType() != null
                    && (officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT) || officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI))) {
                messageTemplateCode = messageTemplateCode.concat("_Expat");
                paramValues.put("basic_salary", (officeStaff.getBasicSalary() != null ? NumberFormatter.formatNumber(officeStaff.getBasicSalary()) : "") + " " + officeStaff.getSalaryCurrency());
                paramValues.put("housing", (officeStaff.getHousingAllowance() != null ? NumberFormatter.formatNumber(officeStaff.getHousingAllowance()) : "") + " " + officeStaff.getSalaryCurrency());
                paramValues.put("transportation", (officeStaff.getTrasnportation() != null ? NumberFormatter.formatNumber(officeStaff.getTrasnportation()) : "") + " " + officeStaff.getSalaryCurrency());
            }

            String pageGeneralBody = pagesBodiesMap.get(messageTemplateCode);
            pageBodyText = this.generateBodyMessageText(pageGeneralBody, paramValues);

            if (!managerNote.getOfficeStaff().getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT) && !managerNote.getOfficeStaff().getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI)) {
                return pageBodyText;
            }

            if (!(managerNote.getNoteType().equals(AbstractPayrollManagerNote.ManagerNoteType.REDUCTION) ||
                    managerNote.getNoteType().equals(AbstractPayrollManagerNote.ManagerNoteType.SALARY_RAISE))) {
                return pageBodyText;
            }
            Map<String, Object> body = new HashMap<>();
            body.put("text", pageBodyText);
            body.put("id", managerNote.getId());
            body.put("amount", managerNote.getAmount());
            body.put("basicSalary", managerNote.getBasicSalary());
            body.put("transportation", managerNote.getTransportation());
            body.put("housing", managerNote.getHousing());
            body.put("currency", managerNote.getOfficeStaff().getSalaryCurrency().toString());
            body.put("type", managerNote.getNoteType().toString());
            return body;
        } else
            throw new RuntimeException("can't find related manager note!");
    }

    private String getAddLoanApprovalPageBody(String loanApproveId, String messageTemplateCode) throws RuntimeException {
        String pageBodyText = "";

        EmployeeLoanApprove loanApprove = employeeLoanApproveRepository.findOne(Long.parseLong(loanApproveId));
        OfficeStaff staff = officeStaffRepository.getOne(loanApprove.getOfficeStaff().getId());

        if (loanApprove != null) {

            if (loanApprove.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }

            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("employee_name", staff.getFirstLastName());
            paramValues.put("value", NumberFormatter.formatNumber(loanApprove.getAmount()) + " " + staff.getSalaryCurrency());
            paramValues.put("add_loan_date", DateUtil.formatClientFullDate(loanApprove.getLoanDate()));
            paramValues.put("repayment_amount", NumberFormatter.formatNumber(loanApprove.getMonthlyRepaymentAmount()) + " " + staff.getSalaryCurrency());
            paramValues.put("notes", loanApprove.getNotes() != null ? loanApprove.getNotes() : "");
            paramValues.put("employee_type", staff.getEmployeeType().getLabel());
            paramValues.put("manager", staff.getEmployeeManager() != null ? staff.getEmployeeManager().getShortName() : "__");
            paramValues.put("job_title", staff.getJobTitle() != null ? staff.getJobTitle().getName() : "__");
            paramValues.put("start_date", DateUtil.formatClientFullDate(staff.getStartingDate()));
            paramValues.put("total_salary", staff.getSalaryWithCurrencyAfter());
            if (staff.getEmployeeType() != null
                    && (staff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT) || staff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI))) {
                messageTemplateCode = messageTemplateCode.concat("_Expat");
                paramValues.put("basic_salary", (staff.getBasicSalary() != null ? NumberFormatter.formatNumber(staff.getBasicSalary()) : "") + " " + staff.getSalaryCurrency());
                paramValues.put("housing", (staff.getHousingAllowance() != null ? NumberFormatter.formatNumber(staff.getHousingAllowance()) : "") + " " + staff.getSalaryCurrency());
                paramValues.put("transportation", (staff.getTrasnportation() != null ? NumberFormatter.formatNumber(staff.getTrasnportation()) : "") + " " + staff.getSalaryCurrency());
            }

            String pageGeneralBody = pagesBodiesMap.get(messageTemplateCode);
            pageBodyText = this.generateBodyMessageText(pageGeneralBody, paramValues);
        } else
            throw new RuntimeException(errorMessage = "can't find related  Employee Loan Approve!");

        return pageBodyText;
    }

    private String getEditLoanApprovalPageBody(String loanApproveId, String messageTemplateCode) throws RuntimeException {
        String pageBodyText = "";

        EmployeeLoanApprove loanApprove = employeeLoanApproveRepository.findOne(Long.parseLong(loanApproveId));
        OfficeStaff staff = officeStaffRepository.getOne(loanApprove.getOfficeStaff().getId());

        if (loanApprove != null) {

            if (loanApprove.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }

            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("employee_name", staff.getFirstLastName());
            paramValues.put("old_value", NumberFormatter.formatNumber(loanApprove.getAmount()) + " " + staff.getSalaryCurrency());
            paramValues.put("new_value", NumberFormatter.formatNumber(loanApprove.getUpdatedAmount()) + " " + staff.getSalaryCurrency());
            paramValues.put("repayment_amount", NumberFormatter.formatNumber(loanApprove.getUpdatedRepaymentAmount()) + " " + staff.getSalaryCurrency());
            paramValues.put("edit_loan_date", DateUtil.formatClientFullDate(new Date()));

            String pageGeneralBody = pagesBodiesMap.get(messageTemplateCode);
            pageBodyText = this.generateBodyMessageText(pageGeneralBody, paramValues);
        } else
            throw new RuntimeException(errorMessage = "can't find related  Employee Loan Approve!");

        return pageBodyText;
    }

    private String getChangeLoanRepaymentsApprovalPageBody(String loanApproveId, String messageTemplateCode) throws RuntimeException {
        String pageBodyText = "";

        EmployeeLoanApprove loanApprove = employeeLoanApproveRepository.findOne(Long.parseLong(loanApproveId));
        OfficeStaff staff = officeStaffRepository.getOne(loanApprove.getOfficeStaff().getId());

        if (loanApprove != null) {

            if (loanApprove.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }

            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("employee_name", staff.getFirstLastName());
            paramValues.put("old_value", NumberFormatter.formatNumber(loanApprove.getMonthlyRepaymentAmount()) + " " + staff.getSalaryCurrency());
            paramValues.put("new_value", NumberFormatter.formatNumber(loanApprove.getUpdatedRepaymentAmount()) + " " + staff.getSalaryCurrency());
            paramValues.put("initial_related_loan_amount", NumberFormatter.formatNumber(loanApprove.getAmount()) + " " + staff.getSalaryCurrency());
            paramValues.put("related_loan_date", DateUtil.formatClientFullDate(loanApprove.getLoanDate()));
            paramValues.put("edit_loan_repayment_payroll_month", DateUtil.formatMonth(loanApprove.getLoanDate()));
            paramValues.put("employee_type", staff.getEmployeeType().getLabel());
            paramValues.put("manager", staff.getEmployeeManager() != null ? staff.getEmployeeManager().getShortName() : "__");
            paramValues.put("job_title", staff.getJobTitle() != null ? staff.getJobTitle().getName() : "__");
            paramValues.put("start_date", DateUtil.formatClientFullDate(staff.getStartingDate()));
            paramValues.put("total_salary", staff.getSalaryWithCurrencyAfter());
            if (staff.getEmployeeType() != null
                    && (staff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT) || staff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI))) {
                messageTemplateCode = messageTemplateCode.concat("_Expat");
                paramValues.put("basic_salary", (staff.getBasicSalary() != null ? NumberFormatter.formatNumber(staff.getBasicSalary()) : "") + " " + staff.getSalaryCurrency());
                paramValues.put("housing", (staff.getHousingAllowance() != null ? NumberFormatter.formatNumber(staff.getHousingAllowance()) : "") + " " + staff.getSalaryCurrency());
                paramValues.put("transportation", (staff.getTrasnportation() != null ? NumberFormatter.formatNumber(staff.getTrasnportation()) : "") + " " + staff.getSalaryCurrency());
            }
            String pageGeneralBody = pagesBodiesMap.get(messageTemplateCode);
            pageBodyText = this.generateBodyMessageText(pageGeneralBody, paramValues);
        } else
            throw new RuntimeException(errorMessage = "can't find related  Employee Loan Approve!");

        return pageBodyText;
    }

    private String getDeleteLoanApprovalPageBody(String loanApproveId, String messageTemplateCode) throws RuntimeException {
        String pageBodyText = "";

        EmployeeLoanApprove loanApprove = employeeLoanApproveRepository.findOne(Long.parseLong(loanApproveId));
        EmployeeLoan loan = employeeLoanRepository.findOne(loanApprove.getOriginalLoan().getId());
        OfficeStaff staff = officeStaffRepository.getOne(loan.getOfficeStaff().getId());

        if (loanApprove != null) {

            if (loanApprove.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }

            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("employee_name", staff.getFirstLastName());
            paramValues.put("loan_amount", NumberFormatter.formatNumber(loan.getAmount()) + " " + staff.getSalaryCurrency());
            paramValues.put("delete_loan_date", DateUtil.formatClientFullDate(new Date()));

            String pageGeneralBody = pagesBodiesMap.get(messageTemplateCode);
            pageBodyText = this.generateBodyMessageText(pageGeneralBody, paramValues);
        } else
            throw new RuntimeException(errorMessage = "can't find related  Employee Loan Approve!");

        return pageBodyText;
    }

    private String getEditLoanRepaymentApprovalPageBody(String loanApproveId, String messageTemplateCode) throws RuntimeException {
        String pageBodyText = "";

        EmployeeLoanApprove loanApprove = employeeLoanApproveRepository.findOne(Long.parseLong(loanApproveId));
        OfficeStaff staff = officeStaffRepository.getOne(loanApprove.getOfficeStaff().getId());

        if (loanApprove != null) {

            if (loanApprove.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("employee_name", staff.getFirstLastName());
            paramValues.put("old_value", NumberFormatter.formatNumber(loanApprove.getMonthlyRepaymentAmount()) + " " + staff.getSalaryCurrency());
            paramValues.put("new_value", NumberFormatter.formatNumber(loanApprove.getUpdatedRepaymentAmount()) + " " + staff.getSalaryCurrency());
            paramValues.put("edit_loan_repayment_payroll_month", DateUtil.formatMonth(loanApprove.getUpdatedMonth()));
            paramValues.put("employee_type", staff.getEmployeeType().getLabel());
            paramValues.put("manager", staff.getEmployeeManager() != null ? staff.getEmployeeManager().getShortName() : "__");
            paramValues.put("job_title", staff.getJobTitle() != null ? staff.getJobTitle().getName() : "__");

            String pageGeneralBody = pagesBodiesMap.get(messageTemplateCode);
            pageBodyText = this.generateBodyMessageText(pageGeneralBody, paramValues);
        } else
            throw new RuntimeException(errorMessage = "can't find related  Employee Loan Approve!");

        return pageBodyText;
    }

    private String getAddPaidOffDaysApprovalPageBody(String paidOffDaysId, String messageTemplateCode) throws RuntimeException {
        String pageBodyText = "";

        PaidOffDays paidOffDays = paidOffDaysRepository.findOne(Long.parseLong(paidOffDaysId));

        if (paidOffDays != null) {
            OfficeStaff staff = officeStaffRepository.getOne(paidOffDays.getOfficeStaff().getId());

            if (paidOffDays.getActionTaken() || !PaidOffDays.PaidOffDayStatus.Pending.equals(paidOffDays.getStatus())) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }

            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("employee_name", staff.getFirstLastName());
            paramValues.put("value", NumberFormatter.formatNumber(paidOffDays.getAmount()) + " " + staff.getSalaryCurrency());
            paramValues.put("add_paidOffDays_date", DateUtil.formatClientFullDate(paidOffDays.getPayrollMonth()));
            paramValues.put("employee_type", staff.getEmployeeType().getLabel());
            paramValues.put("manager", staff.getEmployeeManager() != null ? staff.getEmployeeManager().getShortName() : "__");
            paramValues.put("job_title", staff.getJobTitle() != null ? staff.getJobTitle().getName() : "__");
            paramValues.put("start_date", DateUtil.formatClientFullDate(staff.getStartingDate()));
            paramValues.put("total_salary", staff.getSalaryWithCurrencyAfter());
            if (staff.getEmployeeType() != null
                    && (staff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT) || staff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI))) {
                messageTemplateCode = messageTemplateCode.concat("_Expat");
                paramValues.put("basic_salary", (staff.getBasicSalary() != null ? NumberFormatter.formatNumber(staff.getBasicSalary()) : "") + " " + staff.getSalaryCurrency());
                paramValues.put("housing", (staff.getHousingAllowance() != null ? NumberFormatter.formatNumber(staff.getHousingAllowance()) : "") + " " + staff.getSalaryCurrency());
                paramValues.put("transportation", (staff.getTrasnportation() != null ? NumberFormatter.formatNumber(staff.getTrasnportation()) : "") + " " + staff.getSalaryCurrency());
            }

            String pageGeneralBody = pagesBodiesMap.get(messageTemplateCode);
            pageBodyText = this.generateBodyMessageText(pageGeneralBody, paramValues);
        } else
            throw new RuntimeException(errorMessage = "can't find related  Paid Off Days Approve!");

        return pageBodyText;
    }

    private String getPayrollMarkUnpaidBody(String todoId, String messageTemplateCode) throws RuntimeException {
        String pageBodyText;
        OfficeStaffPayrollLog log = Setup.getRepository(OfficeStaffPayrollLogRepository.class).findOne(Long.parseLong(todoId));

        if (log != null) {

            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("payroll_date", DateUtil.formatFullMonthDashedFullYear(log.getPayEndDate()));
            paramValues.put("employee_name", log.getEmployeeName());
            paramValues.put("salary_amount", NumberFormatter.formatNumber(log.getTotalSalary()) + " " + log.getCurrency());
            paramValues.put("notes", log.getMarkUnpaidNotes());

            String pageGeneralBody = pagesBodiesMap.get(messageTemplateCode);
            pageBodyText = generateBodyMessageText(pageGeneralBody, paramValues);
        } else
            throw new RuntimeException("can't find related Office Staff Payroll Log!");

        return pageBodyText;
    }

    private String getNewHireApprovalPageBody(String todoId, String messageTemplateCode) throws RuntimeException {
        String pageBodyText = "";
        OfficeStaffTodo todo;
        OfficeStaffCandidate candidate = (todo = officeStaffTodoRepository.findOne(Long.parseLong(todoId))) != null ? officeStaffCandidateRepository.findOne(todo.getCandidate().getId()) : null;

        if (candidate != null) {

            if (candidate.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }

            Date startingDate = OfficeStaffType.OVERSEAS_STAFF.equals(candidate.getEmployeeType())? candidate.getStartingDate() : candidate.getPotentialStartDate();
            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("employee_name", candidate.getEmployeeName());
            paramValues.put("job_title", candidate.getJobTitle().getName());
            paramValues.put("salary", NumberFormatter.formatNumber(candidate.getSalary()) + " " + candidate.getSalaryCurrency());
            paramValues.put("start_date",startingDate != null? DateUtil.formatClientFullDate(startingDate):"");
            paramValues.put("employee_type", candidate.getEmployeeType().getLabel());
            paramValues.put("notes", candidate.getNotes());
            String weeklyOffDays = "[";
            for (DayOfWeek weeklyOfDay : candidate.getWeeklyOffDayList()) {
                weeklyOffDays += weeklyOfDay + ", ";
            }
            if (candidate.getWeeklyOffDayList().size() > 0)
                weeklyOffDays = weeklyOffDays.substring(0, weeklyOffDays.length() - 2);
            weeklyOffDays += "]";
            paramValues.put("weekly_off_day", weeklyOffDays);
            paramValues.put("direct_manager_name", candidate.getEmployeeManager().getShortName());
            //in case of expat
            if (candidate.getEmployeeType() != null
                    && (candidate.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT) || candidate.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI))) {
                messageTemplateCode = "Payroll_New_Hire_Approval_Expat";
                paramValues.put("basic_salary", (candidate.getBasicSalary() != null ? NumberFormatter.formatNumber(candidate.getBasicSalary()) : "") + " " + candidate.getSalaryCurrency());
                paramValues.put("housing", (candidate.getHousing() != null ? NumberFormatter.formatNumber(candidate.getHousing()) : "") + " " + candidate.getSalaryCurrency());
                paramValues.put("transportation", (candidate.getTransportation() != null ? NumberFormatter.formatNumber(candidate.getTransportation()) : "") + " " + candidate.getSalaryCurrency());
            }
            String pageGeneralBody = pagesBodiesMap.get(messageTemplateCode);
            pageBodyText = generateBodyMessageText(pageGeneralBody, paramValues);
        } else
            throw new RuntimeException("can't find related Office Staff Candidate!");

        return pageBodyText;
    }

    private String getNewHireApprovalPageBodyFromOfficeStaffListPage(String candidateId, String messageTemplateCode) throws RuntimeException {
        String pageBodyText = "";
        OfficeStaffCandidate candidate = officeStaffCandidateRepository.findOne(Long.parseLong(candidateId));

        if (candidate != null) {

            if (candidate.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }

            Date startingDate = OfficeStaffType.OVERSEAS_STAFF.equals(candidate.getEmployeeType())? candidate.getStartingDate() : candidate.getPotentialStartDate();

            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("employee_name", candidate.getEmployeeName());
            paramValues.put("job_title", candidate.getJobTitle().getName());
            paramValues.put("salary", NumberFormatter.formatNumber(candidate.getSalary()) + " " + candidate.getSalaryCurrency());
            paramValues.put("start_date",startingDate != null? DateUtil.formatClientFullDate(startingDate) : "");
            paramValues.put("employee_type", candidate.getEmployeeType().getLabel());
            paramValues.put("notes", candidate.getNotes());
            String weeklyOffDays = "[";
            for (DayOfWeek weeklyOfDay : candidate.getWeeklyOffDayList()) {
                weeklyOffDays += weeklyOfDay + ", ";
            }
            if (candidate.getWeeklyOffDayList().size() > 0)
                weeklyOffDays = weeklyOffDays.substring(0, weeklyOffDays.length() - 2);
            weeklyOffDays += "]";
            paramValues.put("weekly_off_day", weeklyOffDays);
            paramValues.put("direct_manager_name", candidate.getEmployeeManager().getShortName());
            //in case of expat
            if (candidate.getEmployeeType() != null
                    && (candidate.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT) || candidate.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI))) {
                messageTemplateCode = "Payroll_New_Hire_Approval_Expat";
                paramValues.put("basic_salary", (candidate.getBasicSalary() != null ? NumberFormatter.formatNumber(candidate.getBasicSalary()) : "") + " " + candidate.getSalaryCurrency());
                paramValues.put("housing", (candidate.getHousing() != null ? NumberFormatter.formatNumber(candidate.getHousing()) : "") + " " + candidate.getSalaryCurrency());
                paramValues.put("transportation", (candidate.getTransportation() != null ? NumberFormatter.formatNumber(candidate.getTransportation()) : "") + " " + candidate.getSalaryCurrency());
            }
            String pageGeneralBody = pagesBodiesMap.get(messageTemplateCode);
            pageBodyText = generateBodyMessageText(pageGeneralBody, paramValues);
        } else
            throw new RuntimeException("can't find related Office Staff Candidate!");

        return pageBodyText;
    }

    private String getOfficeStaffUpdatePageBody(String todoId, String messageTemplateCode) throws RuntimeException {
        String pageBodyText = "";
        UpdateOfficeStaffApprove approve = updateOfficeStaffApproveRepository
                .findOne(Long.parseLong(todoId));

        if (approve != null) {

            if (approve.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }

            OfficeStaff officeStaff = approve.getOfficeStaff();
            Double salary = approve.getUpdateType() == UpdateOfficeStaffApprove.UpdateType.SALARY ? approve.getSalary() : officeStaff.getSalary();
            SalaryCurrency currency = approve.getUpdateType() == UpdateOfficeStaffApprove.UpdateType.SALARY ? approve.getSalaryCurrency() : officeStaff.getSalaryCurrency();
            Date startingDate = approve.getUpdateType() == UpdateOfficeStaffApprove.UpdateType.START_DATE ? approve.getStartingDate() : officeStaff.getStartingDate();
            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("employee_name", officeStaff.getFirstLastName());
            paramValues.put("job_title", officeStaff.getJobTitle().getName());
            paramValues.put("salary", NumberFormatter.formatNumber(salary) + " " + currency);
            paramValues.put("start_date", DateUtil.formatClientFullDate(startingDate));

            String pageGeneralBody = pagesBodiesMap.get(messageTemplateCode);
            pageBodyText = generateBodyMessageText(pageGeneralBody, paramValues);
        } else
            throw new RuntimeException("can't find related approve todo!");

        return pageBodyText;
    }

    /**
     * Do something when the Final Manager approve/reject according to the needed case from received token and wanted action
     *
     * @param tokenLoad
     * @param action
     * @param notes
     * @return Boolean
     * @throws Exception
     */
    @Transactional
    public Boolean approveCaseByFinalManager(String tokenLoad, Boolean action, String notes) throws RuntimeException {
        boolean result = false;

        String[] tokenLoadParts = tokenLoad.split("#");
        if (tokenLoadParts.length != 2)
            throw new RuntimeException(errorMessage = "missing data in the token.");
        String entityId = tokenLoadParts[0];
        String messageTemplateCode = tokenLoadParts[1];

        switch (messageTemplateCode) {
            case "Payroll_Mark_Unpaid_Approval":
                result = this.approveUnpaidPayrollApproval(entityId, action);
                break;
            case "Payroll_New_Hire_Approval":
                result = publicPageService.approveNewHireEmployeeTodo(entityId, action);
                break;
            case "Payroll_New_Hire_Approval_From_OfficeStaff_List_Page":
                result = publicPageService.approveNewHireEmployeeTodoFromOfficeStaffListPage(entityId, action);
                break;
            case "Payroll_Salary_Raise_Approval":
                result = this.approveManagerNote(entityId, messageTemplateCode, action, notes);
                break;
            case "Payroll_Salary_Deduction_Approval":
                result = this.approveManagerNote(entityId, messageTemplateCode, action, notes);
                break;
            case "Payroll_Bonus_Approval":
                result = this.approveManagerNote(entityId, messageTemplateCode, action, notes);
                break;
            case "Payroll_Salary_Reduction_Approval":
                result = this.approveManagerNote(entityId, messageTemplateCode, action, notes);
                break;
            case "Payroll_Add_Loan_Approval":
                result = publicPageService.approveAddEmployeeLoan(entityId, action, notes);
                break;
            case "Payroll_Edit_Loan_Approval":
            case "Payroll_Change_Loan_repayments_Approval":
                result = publicPageService.approveEditEmployeeLoan(entityId, action, notes);
                break;
            case "Payroll_Edit_Loan_Repayment_Approval":
                result = publicPageService.approveEditEmployeeLoanRepayment(entityId, action, notes);
                break;
            case "Payroll_Delete_Loan_Approval":
                result = publicPageService.approveDeleteEmployeeLoan(entityId, action, notes);
                break;
            case "Payroll_Add_PaidOffDays_Approval":
                result = publicPageService.approveAddPaidOffDays(entityId, action, notes, true);
                break;
            case "Payroll_Update_OfficeStaff_Approval":
                result = this.approveOfficeStaffUpdate(entityId, action);
                break;
            case "Payroll_Roster_Approval":
                result = this.approvePayrollRoster(entityId, action, notes);
                break;
            case "Payroll_Payment_Approve":
            case "Payroll_Payment_Approve_For_Bank_Transfer":
                result = this.approvePaymentProcess(entityId, action);
                break;
            case "Payroll_Payment_Approve_By_CFO":
                result = this.approvePaymentProcessByCFO(entityId, action);
                break;
            case "Payroll_Pay_Loan_Repayment_Approval":
                result = this.approvePayLoanRepayment(entityId, action);
                break;
            case "switch_to_expat":
                result = this.approveOverseasToExpatSwitching(entityId, action,notes);
                break;
            default:
                throw new RuntimeException(errorMessage = "missing data in the token.");
        }

        PendingApprovalRequest pendingApprovalRequest = pendingApprovalRequestRepository.findTopByTokenLoadOrderByIdDesc(tokenLoad);
        if (pendingApprovalRequest != null) {
            pendingApprovalRequest.setApproved(true);
            pendingApprovalRequest.setApprovedBy(CurrentRequest.getUser());
            //...PAY-895...//
            pendingApprovalRequest.setRejectionNotes(notes);
            pendingApprovalRequestRepository.save(pendingApprovalRequest);
        }

        return result;
    }

    private boolean approvePayLoanRepayment(String entityId, Boolean action) {
        String[] tokenLoadParts = entityId.split(":");
        if (tokenLoadParts.length != 2)
            throw new RuntimeException(errorMessage = "missing data in the token.");
        String id = tokenLoadParts[0];
        String repaymentDateString = tokenLoadParts[1];
        Date repaymentDate = null;
        try {
            repaymentDate = DateUtil.parseDateDashed(repaymentDateString);
        } catch (ParseException e) {
            throw new RuntimeException(errorMessage = " Action is already taken");
        }
        OfficeStaff officeStaff = officeStaffRepository.findOne(Long.parseLong(id));
        if (officeStaff == null || repaymentDate == null){
            throw new RuntimeException(errorMessage = " Action is already taken");
        }
        List<Repayment> repayments = repaymentRepository.findByOfficestaffIdAndRepaymentDateBetweenAndStatus(Long.parseLong(id),DateUtil.getStartDayValue(repaymentDate), DateUtil.getEndDayValue(repaymentDate), RepaymentStatus.Pending_Approval);
        Date requestDate;
        if (repayments != null && !repayments.isEmpty()){
            requestDate = repayments.get(0).getLastModificationDate();
            if (action != null && action){
                repayments.forEach(r ->{
                    r.setStatus(RepaymentStatus.Paid);
                    r.setPaidRepayment(true);
                    repaymentRepository.save(r);
                });
            }else {
                repayments.forEach(r ->{
                    r.setStatus(RepaymentStatus.Normal);
                    repaymentRepository.save(r);
                });
            }
        }else{
            throw new RuntimeException(errorMessage = " Action is already taken");
        }

        String managerId = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAY_LOAN_REPAYMENT_APPROVAL_EMAIL_RECIPIENTS);
        OfficeStaff manager = officeStaffRepository.findOne(Long.parseLong(managerId));
        String link = shortener.shorten(PublicPageHelper.getModuleBaseAngular13Url() + "office-staff-payroll/" + officeStaff.getId() + "/loans");
        Setup.getApplicationContext().getBean(MessagingService.class).notifyPayrollTrusteeAboutRequest(officeStaff, manager, "pay loan repayment", requestDate, "", link, Boolean.TRUE.equals(action));
        return true;
    }

    @Transactional
    public boolean approvePaymentProcess(String entityId, Boolean action) {
        PayrollAccountantTodo todo = Setup.getRepository(PayrollAccountantTodoRepository.class)
                .findOne(Long.parseLong(entityId));

        if (todo != null) {

            //check if action is taken before
            if (todo.getActionTaken() || todo.getFirstActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }
            if (action) {

                List<Attachment> attachments = new ArrayList<>();
                List<Attachment> auditorAttachments = new ArrayList<>();
                Map<String, String> paramValues = new HashMap();
                boolean emailSent = false;
                switch (PayrollAccountantTodoType.valueOf(todo.getTaskName())) {
                    case WPS:
                        //send email to Ansari
                        paramValues.put("payroll_month", DateUtil.formatSimpleMonthYear(todo.getPayrollMonth()));
                        paramValues.put("payment_date", DateUtil.formatMonthDayYear(todo.getPaymentDate()));
                        paramValues.put("total_salaries", NumberFormatter.formatNumber(todo.getAmount()));
                        paramValues.put("ansari_charges", NumberFormatter.formatNumber(todo.getCharges() + todo.getVat()));
                        paramValues.put("total", NumberFormatter.formatNumber(todo.getTotal()));

                        if (todo.getAttachment("WPSTransferFile") != null)
                            attachments.add(todo.getAttachment("WPSTransferFile"));
                        if (!attachments.isEmpty()) {
                            Setup.getApplicationContext().getBean(EmailTemplateService.class).sendEmail("PAY_WPS_Template", paramValues, attachments);
                            emailSent = true;
                        }
                        break;
                    case INTERNATIONAL_TRANSFER:
                        //send email to Ansari
                        paramValues.put("payment_date", DateUtil.formatMonthDayYear(todo.getPaymentDate()));
                        paramValues.put("unique_payment_code", todo.getUniquePaymentCode());
                        if (todo.getAttachment("InternationalTransferFile") != null)
                            attachments.add(todo.getAttachment("InternationalTransferFile"));
                        if (!attachments.isEmpty()) {
                            Setup.getApplicationContext().getBean(EmailTemplateService.class).sendEmail("PAY_Request_International_Exchange_Template",
                                    paramValues, attachments);
                            emailSent = true;
                        }
                        break;
                    case LOCAL_TRANSFER:
                        //send email to Ansari
                        paramValues.put("payment_date", DateUtil.formatMonthDayYear(todo.getPaymentDate()));
                        if (todo.getAttachment("LocalTransferFile") != null)
                            attachments.add(todo.getAttachment("LocalTransferFile"));
                        if (!attachments.isEmpty()) {
                            Setup.getApplicationContext().getBean(EmailTemplateService.class).sendEmail("PAY_Request_Local_Exchange_Template",
                                    paramValues, attachments);
                            emailSent = true;
                        }
                        break;
                }


                if (emailSent) {
                    //send email to CFO to approve the files sent to Ansari
                    String usersIds = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_PAYMENT_APPROVE_USER_ID_RECEIVERS);
                    List<String> idsList = Arrays.asList(usersIds.split(";"));

                    for(String userId : idsList) {
                        User user = userRepository.findOne(Long.parseLong(userId));

                        EmailRecipient recipient = new Recipient(user.getEmail() != null ? user.getEmail() : null , user.getName() != null ? user.getName() : null);
                        List<EmailRecipient> recipients = new ArrayList<>();
                        recipients.add(recipient);

                        Map<String, String> params = new HashMap();
                        params.put("money_transfer_center", Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_MONEY_EXCHANGE_NAME));
                        String url = publicPageHelper.generatePublicURL(PublicPageHelper.FINAL_MANAGER_APPROVE, todo.getId().toString() + "#Payroll_Payment_Approve_By_CFO", userId);
                        params.put("link", url);
                        params.put("file_type", todo.getTaskName().equals("WPS") ? "WPS" : StringHelper.enumToCapitalizedFirstLetter(todo.getTaskName()));
                        params.put("currency_with_total_amount", !todo.getTaskName().equals("INTERNATIONAL_TRANSFER") ? "AED " + NumberFormatter.formatNumber(todo.getTotal()) : NumberFormatter.formatNumber(todo.getTotal()));

                        messagingService.send(recipients, null, "Payroll_Approve_Money_File_By_CFO", "Approve the File Sent to Ansari Before Transferring Money"
                                , params, new ArrayList<>(), null);
                    }
//                    TemplateEmail templateEmail = new TemplateEmail("Approve the File Sent to Ansari Before Transferring Money", "Payroll_Approve_Money_File_By_CFO", params);
//                    mailService.sendEmail(new MailObject.builder(templateEmail, EmailReceiverType.Office_Staff)
//                            .recipients(recipients)
//                            .html()
//                            .senderName(MessageTemplateService.getMaidsCcSenderName())
//                            .secure()
//                            .build());
                    todo.setFirstActionTaken(true);
                    Setup.getRepository(PayrollAccountantTodoRepository.class)
                            .save(todo);
                } else {
                    todo.setStopped(false);
                    todo.setOpenedToAccountants(true);
                    todo.setOpenedToAccountantsDate(new Date());
                    todo.setCompleted(false);
                    todo.setActionTaken(true);
                    todo.setFirstActionTaken(true);
                    Setup.getRepository(PayrollAccountantTodoRepository.class)
                            .save(todo);
                    messagingService.notifyAccountants(todo);

                    // insert a new background task to send celebration email
                    Setup.getApplicationContext()
                            .getBean(BackgroundTaskService.class)
                            .addDirectCallBackgroundTaskForEntity(
                                    "sendCelebrationEmails", "accountantToDoService", "payroll",
                                    "sendCelebrationEmails",
                                    todo.getEntityType(), todo.getId(), false,
                                    false, new Class[]{Long.class}, new Object[]{todo.getId()});
                }

            } else {
                todo.setActionTaken(true);
                todo.setFirstActionTaken(true);
                Setup.getRepository(PayrollAccountantTodoRepository.class)
                        .save(todo);
            }
            return true;
        }
        return false;
    }

    @Transactional
    public boolean approvePaymentProcessByCFO(String entityId, Boolean action) {
        PayrollAccountantTodo todo = Setup.getRepository(PayrollAccountantTodoRepository.class)
                .findOne(Long.parseLong(entityId));

        if (todo != null) {

            //check if action is taken before
            if (todo.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }
            if (action) {

                todo.setStopped(false);
                todo.setOpenedToAccountants(true);
                todo.setOpenedToAccountantsDate(new Date());
                todo.setCompleted(false);
                todo.setActionTaken(true);
                Setup.getRepository(PayrollAccountantTodoRepository.class)
                        .save(todo);
                messagingService.notifyAccountants(todo);

                // insert a new background task to send celebration email
                Setup.getApplicationContext()
                        .getBean(BackgroundTaskService.class)
                        .addDirectCallBackgroundTaskForEntity(
                                "sendCelebrationEmails", "accountantToDoService", "payroll",
                                "sendCelebrationEmails",
                                todo.getEntityType(), todo.getId(), false,
                                false, new Class[]{Long.class}, new Object[]{todo.getId()});
            } else {
                todo.setActionTaken(true);
                Setup.getRepository(PayrollAccountantTodoRepository.class)
                        .save(todo);
            }
            return true;
        }
        return false;
    }


    @Transactional
    public boolean approveEditEmployeeLoanRepayment(String loanApproveId, Boolean action, String notes) {
        EmployeeLoanApprove loanApprove = employeeLoanApproveRepository.findOne(Long.parseLong(loanApproveId));
        Double usd_to_ead_exchange_rate = 3.6735;
        String exchaneRate = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_USD_TO_AED_EXCHANGE_RATE);
        if(exchaneRate != null)
            usd_to_ead_exchange_rate = Double.parseDouble(exchaneRate);
        if (loanApprove != null) {
            OfficeStaff staff = officeStaffRepository.findOne(loanApprove.getOfficeStaff().getId());
            //check if action is taken before
            if (loanApprove.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }
            if (action) {


                List<EmployeeLoan> loanList = new ArrayList<>();
                List<Double> remainingAmounts = new ArrayList<>();
                Double sumRemainingAmounts = 0d;

                // 0- get repayments list of the updated month
                List<Repayment> repaymentList = repaymentRepository.getCurrentMonthRepayments(staff, loanApprove.getUpdatedMonth());

                //loop on repayments
                for (Repayment r : repaymentList) {
                    //1- throw exception if the repayment is paid
                    if (r.getPaidRepayment())
                        throw new RuntimeException("can not update paid repayment!");

                    // 2- get active Loans list from the above list
                    loanList.add(r.getEmployeeLoan());

                    // 3- get Remaining Amounts List of above loans starting from updated month
                    Double remaining = repaymentRepository.sumAmountByEmployeeLoanAndRepaymentDateGreaterThanEqual(r.getEmployeeLoan(), loanApprove.getUpdatedMonth());
                    sumRemainingAmounts += remaining;
                    remainingAmounts.add(remaining);
                }

                // 4- get next month
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(loanApprove.getUpdatedMonth());
                calendar.add(Calendar.MONTH, 1);
                Date nextMonth = calendar.getTime();

                Double sumUsed = 0d;
                // loop on loans
                for (int i = 0; i < loanList.size(); i++) {
                    Repayment r = repaymentList.get(i);
                    EmployeeLoan loan = employeeLoanRepository.findOne(loanList.get(i).getId());

                    // 5- calculate new repayment amount of the updated month and save it
                    if (i != loanList.size() - 1) {
                        r.setAmount(Math.min(Math.floor(loanApprove.getUpdatedRepaymentAmount() * remainingAmounts.get(i) / sumRemainingAmounts), remainingAmounts.get(i)));
                    } else {
                        r.setAmount(loanApprove.getUpdatedRepaymentAmount() - sumUsed);
                    }


                    sumUsed += r.getAmount();
                    repaymentRepository.save(r);

                    // 6- delete all repayment from the next month
                    employeeLoanService.deleteRepayments(nextMonth, loan);

                    // 7- regenerate payments from the next month
                    employeeLoanService.generateAllRepayments(loan.getMonthlyRepaymentAmount(), nextMonth, loan);
                }
                //make sure that the new values are equal to repayment amount
                if (!loanApprove.getUpdatedRepaymentAmount().equals(sumUsed))
                    throw new RuntimeException("something went wrong, could't updated Repayment");

                // set loanApprove as approved
                loanApprove.setApproved(true);
                loanApprove.setApproveDate(new Date());
            }
            //else {} //action is false then there is nothing to do here

            //send notification to trustee
            String link = shortener.shorten(PublicPageHelper.getModuleBaseAngular13Url() + "office-staff-payroll/" + staff.getId() + "/loans");
            messagingService.notifyPayrollTrusteeAboutRequest(staff, staff.getFinalManager(), "loan repayment edit", loanApprove.getCreationDate(), notes, link, action);

            //set action taken to true
            loanApprove.setActionTaken(true);
            employeeLoanApproveRepository.save(loanApprove);
            return true;
        }

        return false;
    }

    @Transactional
    public boolean approveDeleteEmployeeLoan(String loanApproveId, Boolean action, String notes) {
        EmployeeLoanApprove loanApprove = employeeLoanApproveRepository.findOne(Long.parseLong(loanApproveId));
        EmployeeLoan loan = employeeLoanRepository.findOne(loanApprove.getOriginalLoan().getId());
        if (loan != null) {
            OfficeStaff staff = officeStaffRepository.findOne(loanApprove.getOfficeStaff().getId());
            //check if action is taken before
            if (loanApprove.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }
            if (action) {


                //set the approve entity as approved
                loanApprove.setApproved(true);
                loanApprove.setActionTaken(true);
                loanApprove.setOriginalLoan(null);
                loanApprove.setApproveDate(new Date());
                employeeLoanApproveRepository.save(loanApprove);

                //check if there is any paid repayments
                Double paidAmount = repaymentRepository.sumAmountByEmployeeLoanAndPaidRepayment(loan, true);
                if (paidAmount != null && paidAmount > 0)
                    throw new BusinessException("can't delete this loan, it had paid repayments");

                //delete all related repayments
                for (Repayment repayment : loan.getRepaymentList()) {
                    repaymentRepository.delete(repayment);
                    repaymentRepository.flush();
                }

                // delete all related loan approves
                for (EmployeeLoanApprove approve : employeeLoanApproveRepository.findByOriginalLoan(loan)) {
                    employeeLoanApproveRepository.delete(approve);
                    employeeLoanApproveRepository.flush();
                }

                employeeLoanRepository.delete(loan);
                employeeLoanRepository.flush();

            } else {
                //set action taken to true
                loanApprove.setActionTaken(true);
                loanApprove.setOriginalLoan(null);
                employeeLoanApproveRepository.save(loanApprove);
            }

            //send notification to trustee
            String link = shortener.shorten(PublicPageHelper.getModuleBaseAngular13Url() + "office-staff-payroll/" + staff.getId() + "/loans");
            messagingService.notifyPayrollTrusteeAboutRequest(staff, staff.getFinalManager(), "loan deletion", loanApprove.getCreationDate(), notes, link, action);

            return true;
        }
        return false;
    }

    @Transactional
    public boolean approveEditEmployeeLoan(String loanApproveId, Boolean action, String notes) {
        EmployeeLoanApprove loanApprove = employeeLoanApproveRepository.findOne(Long.parseLong(loanApproveId));
        EmployeeLoan originalLoan = employeeLoanRepository.findOne(loanApprove.getOriginalLoan().getId());
        if (loanApprove != null && originalLoan != null) {
            OfficeStaff staff = officeStaffRepository.findOne(loanApprove.getOfficeStaff().getId());
            //check if action is taken before
            if (loanApprove.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }
            if (action) {

                //make sure that the loanApprove data is still correct (no updates have been done between requested update and approve date
                Double currentPaidAmount = repaymentRepository.sumAmountByEmployeeLoanAndPaidRepayment(originalLoan, true);
                Double currentRemainingAmount = originalLoan.getAmount() - (currentPaidAmount != null ? currentPaidAmount : 0d);
                if (Math.abs(currentRemainingAmount - loanApprove.getRemainingAmount()) > EPS ||
                        Math.abs(loanApprove.getAmount() - originalLoan.getAmount()) > EPS ||
                        Math.abs(loanApprove.getMonthlyRepaymentAmount() - originalLoan.getMonthlyRepaymentAmount()) > EPS) {

                    throw new RuntimeException(errorMessage = "Current Information is out of date. You can’t approve this loan!");
                }

                //update Employee Loan without saving it
                originalLoan.setAmount(loanApprove.getUpdatedAmount());
                originalLoan.setEditNotes(loanApprove.getEditNotes());
                if (loanApprove.getUpdatedRepaymentAmount() != 0)
                    originalLoan.setMonthlyRepaymentAmount(loanApprove.getUpdatedRepaymentAmount());

                //update doNotDeductFromSalary
                originalLoan.setDoNotDeductFromSalary(loanApprove.getDoNotDeductFromSalary());

                //delete unpaid repayments
                employeeLoanService.deleteRepayments(originalLoan);

                // generate repayments
                employeeLoanService.generateAllRepayments(originalLoan.getMonthlyRepaymentAmount(), originalLoan);

                // set loanApprove as approved
                loanApprove.setApproved(true);
                loanApprove.setApproveDate(new Date());
            }
            //else {} //action is false then there is nothing to do here

            //send notification to trustee
            String link = shortener.shorten(PublicPageHelper.getModuleBaseAngular13Url() + "office-staff-payroll/" + staff.getId() + "/loans");
            messagingService.notifyPayrollTrusteeAboutRequest(staff, staff.getFinalManager(), "edit loan", loanApprove.getCreationDate(), notes, link, action);

            //set action taken to true
            loanApprove.setActionTaken(true);
            employeeLoanApproveRepository.save(loanApprove);
            return true;
        }

        return false;
    }

    @Transactional
    public boolean approveAddEmployeeLoan(String loanApproveId, Boolean action, String notes) {
        EmployeeLoanApprove loanApprove = employeeLoanApproveRepository.findOne(Long.parseLong(loanApproveId));
        OfficeStaff staff = officeStaffRepository.findOne(loanApprove.getOfficeStaff().getId());
        Double usd_to_ead_exchange_rate = 3.6735;
        String exchaneRate = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_USD_TO_AED_EXCHANGE_RATE);
        if(exchaneRate != null)
            usd_to_ead_exchange_rate = Double.parseDouble(exchaneRate);
        if (loanApprove != null) {
            //check if action is taken before
            if (loanApprove.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }
            Date loanDate = loanApprove.getLoanDate();
            if (action) {
                //check if action is taken before
                if (loanApprove.getActionTaken()) {
                    // action is already taken
                    throw new BusinessException(errorMessage = " Action is already taken");
                }

                //create new Employee Loan
                EmployeeLoan employeeLoan = new EmployeeLoan(loanApprove, true);
                if (employeeLoan.getLoanDate().compareTo(new Date()) < 0) {
                    loanDate = new Date();
                    employeeLoan.setLoanDate(loanDate);
                }
                employeeLoan.setCurrency(staff.getSalaryCurrency());
                employeeLoan = employeeLoanRepository.save(employeeLoan);

                //PAY-696 pay this loan Immediately
                if (employeeLoan.isAlreadyPaid()) {
                    TransferDestination prevTransferDestination = staff.getSelectedTransferDestination();
                    staff.setSelectedTransferDestination(employeeLoan.getSelectedTransferDestination());
                    MonthlyPaymentRule rule = accountantToDoService.createMonthlyRuleForSingleOfficeStaff(staff);
                    rule = monthlyPaymentRuleRepository.save(rule);
                    OfficeStaffPayrollLog log = accountantToDoService.createPayrollLogForSingleOfficeStaff(staff, employeeLoan, rule);
                    List<OfficeStaffPayrollLog> logs = new ArrayList<>();
                    logs.add(log);
                    accountantToDoService.createPaymentToDoForSingleOfficeStaff(rule, staff, logs, true);
                    staff.setSelectedTransferDestination(prevTransferDestination);
                }

                // if marked as paid without to-do just mark it as Already Paid also
                if (employeeLoan.isAlreadyPaidWithoutTodo()) {
                    employeeLoan.setAlreadyPaid(true);
                    employeeLoan = employeeLoanRepository.save(employeeLoan);
                }
                //PAY-903 pay this loan Release by cash
                if(employeeLoan.getReleaseByCash()){
                    employeeLoan.setAlreadyPaid(true);
                    employeeLoan = employeeLoanRepository.save(employeeLoan);
                    Double amount = employeeLoan.getAmount();
                    String description = "Money handed to " + employeeLoan.getReleaseByCashMoneyReceiverName() + ", and the payroll manager noted the following: " + employeeLoan.getNotes();
                    Setup.getApplicationContext().getBean(OfficestaffPayrollController.class).addExpenseRequest(amount, staff, description, loanApprove.getCreator());
                }

                // set it as approved
                loanApprove.setApproved(true);
                loanApprove.setApproveDate(new Date());
                loanApprove.setOriginalLoan(employeeLoan);

                // generate repayments
                employeeLoanService.generateAllRepayments(employeeLoan.getMonthlyRepaymentAmount(), employeeLoan);

                //create a Bonus manager note
//                if (!loanApprove.isAlreadyPaid()) {
//                    PayrollManagerNote note = new PayrollManagerNote();
//                    note.setAmount(employeeLoan.getAmount());
//                    note.setBasicSalary(employeeLoan.getAmount());
//                    note.setOfficeStaff(staff);
//                    note.setNoteDate(employeeLoan.getLoanDate());
//                    note.setNoteType(AbstractPayrollManagerNote.ManagerNoteType.BONUS);
//                    note.setEmployeeManager(staff.getEmployeeManager());
//                    note.setNoteReasone(employeeLoan.getNotes());
//                    note.setApplied(true);
//                    PayrollManagerNoteRepository.save(note);
//                }
            }
            //else {} //action is false then there is nothing to do here

            //send notification to trustee
            String link = shortener.shorten(PublicPageHelper.getModuleBaseAngular13Url() + "office-staff-payroll/" + staff.getId() + "/loans");
            messagingService.notifyPayrollTrusteeAboutRequest(staff, staff.getFinalManager(), "loan", loanDate, notes, link, action);

            //set action taken to true
            loanApprove.setActionTaken(true);
            employeeLoanApproveRepository.save(loanApprove);
            return true;
        }

        return false;
    }

    @Transactional
    public boolean approveAddPaidOffDays(String paidOffDaysId, Boolean action, String notes, Boolean fromApprovalProcess) {
        PaidOffDays paidOffDays = paidOffDaysRepository.findOne(Long.parseLong(paidOffDaysId));
        if (paidOffDays != null) {
            OfficeStaff staff = officeStaffRepository.findOne(paidOffDays.getOfficeStaff().getId());
            //check if action is taken before
            if (paidOffDays.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }
            Date paidOffDaysDate = paidOffDays.getCreationDate();
            if (action) {

                // set it as approved
                paidOffDays.setStatus(PaidOffDays.PaidOffDayStatus.Done);

                // deduct number of days from PaidOffDaysBalance days
                staff.setPaidOffDaysBalance(staff.getPaidOffDaysBalance() + paidOffDays.getNumOfDays());

                // Jirra ACC-1569 add paid off days to consumed off days balance
                staff.setConsumedOffDaysBalance(staff.getConsumedOffDaysBalance() + paidOffDays.getNumOfDays());

                // save changes
                officeStaffRepository.save(staff);

                //Sending messages
                Map<String, String> paramValues = new HashMap<>();

                paramValues.put("employee_name", staff.getFirstLastName());
                paramValues.put("number_of_requested_days", NumberFormatter.formatNumber(paidOffDays.getNumOfDays()));
                paramValues.put("amount_of_paid_off_days", (staff.getSalaryCurrency() != null ? staff.getSalaryCurrency().toString() + " " : "") + Math.round((staff.getSalary() / 30.4) * paidOffDays.getNumOfDays() * 100) / 100);

//                messageTemplateService.sendMessageOrEmail("Paid Off Days for employee",
//                        staff.getFinalManager(),
//                        "Payroll_Paid_Off_Days_For_Employee",
//                        paramValues);
                messagingService.send("Payroll_Paid_Off_Days_For_Employee", "Paid Off Days for employee",
                        null, staff.getFinalManager(), paramValues, staff, null, staff.getFinalManager());

            } else {
                paidOffDays.setStatus(PaidOffDays.PaidOffDayStatus.Rejected);
            }

            if (fromApprovalProcess) {
                //send notification to trustee
                String link = shortener.shorten(PublicPageHelper.getModuleBaseAngular13Url() + "office-staff-payroll/" + staff.getId() + "/off_days");
                OfficeStaff coo = officeStaffRepository.findOne(Long.valueOf(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CEO_ID)));
                messagingService.notifyPayrollTrusteeAboutRequest(staff, coo, "paid off days", paidOffDaysDate, notes, link, action);
            }

            //set action taken to true
            paidOffDays.setActionTaken(true);
            paidOffDaysRepository.save(paidOffDays);
            return true;
        }

        return false;
    }

    private boolean approveManagerNote(String todoId, String messageTemplateCode, Boolean action, String notes) {
        PayrollManagerNoteApprove managerNoteApprove = payrollManagerNoteApproveRepository.findOne(Long.parseLong(todoId));
        if (managerNoteApprove != null) {
            //check if action is taken before
            if (managerNoteApprove.getActionTaken() != null && managerNoteApprove.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }
            PayrollManagerNote note = new PayrollManagerNote();
            OfficeStaff staff = officeStaffRepository.findOne(managerNoteApprove.getOfficeStaff().getId());

            Date noteDate = new Date().after(managerNoteApprove.getNoteDate()) ? new Date() : managerNoteApprove.getNoteDate();
            note.setNoteDate(noteDate);
            if (action) {
                //create a new payroll manager note record
                note.setAmount(managerNoteApprove.getAmount());
                note.setTransportation(managerNoteApprove.getTransportation());
                note.setHousing(managerNoteApprove.getHousing());
                note.setBasicSalary(managerNoteApprove.getBasicSalary());
                note.setOfficeStaff(managerNoteApprove.getOfficeStaff());
                note.setNoteType(managerNoteApprove.getNoteType());
                note.setAdditionReason(managerNoteApprove.getAdditionReason());
                note.setDeductionReason(managerNoteApprove.getDeductionReason());
                note.setNoteReasone(managerNoteApprove.getNoteReasone());
                note.setFromManager(managerNoteApprove.getFromManager());
                note.setEmployeeManager(managerNoteApprove.getEmployeeManager());
                note.setApplied(managerNoteApprove.getNoteType().equals(PayrollManagerNote.ManagerNoteType.DEDUCTION)
                        || managerNoteApprove.getNoteType().equals(PayrollManagerNote.ManagerNoteType.BONUS));
                for (Attachment attachment : managerNoteApprove.getAttachments()) {
                    note.addAttachment(Storage.cloneTemporary(attachment, attachment.getTag()));
                }

                //get last salary
                Double lastSalary = staff != null ? staff.getSalary() : 0D;
                note.setLastSalary(lastSalary);

                note.setCreator(managerNoteApprove.getCreator());
                note.setCurrency(managerNoteApprove.getCurrency());
                PayrollManagerNoteRepository.save(note);
//            payrollManagerNoteApproveRepository.delete(managerNoteApprove);
            }
//            else { } //there is nothing to do here

            //send notification to trustee
            String link = shortener.shorten(PublicPageHelper.getModuleBaseAngular13Url() + "office-staff-payroll/"  + staff.getId() + "/manager_notes");
            messagingService.notifyPayrollTrusteeAboutRequest(staff, staff.getFinalManager(), StringHelper.enumToCapitalizedFirstLetter(managerNoteApprove.getNoteType().name()), action ? noteDate : managerNoteApprove.getNoteDate(), notes, link, action);

            //set action taken to true
            managerNoteApprove.setActionTaken(true);
            payrollManagerNoteApproveRepository.save(managerNoteApprove);
            return true;
        }

        return false;
    }

    public boolean approveUnpaidPayrollApproval(String todoId, Boolean action) {
        OfficeStaffPayrollLog officeStaffPayrollLog = Setup.getRepository(OfficeStaffPayrollLogRepository.class).findOne(Long.parseLong(todoId));

        if (action) {
            officeStaffPayrollLog.setTransferred(false);
            officeStaffPayrollLog.setWillBeIncluded(false);
            officeStaffPayrollLog.setPaidOnDate(null);
            officeStaffPayrollLog.setCeoActionBy(null);
            officeStaffPayrollLog.setCeoAction(PayrollAccountantTodoManagerAction.PENDING);

            Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(officeStaffPayrollLog);
        }
        return true;
    }

    @Transactional
    public boolean approveNewHireEmployeeTodo(String todoId, Boolean action) {
        OfficeStaffTodo hireEmployeeTodo = officeStaffTodoRepository.findOne(Long.parseLong(todoId));
        if (hireEmployeeTodo != null) {
            OfficeStaffCandidate candidate = officeStaffCandidateRepository.findOne(hireEmployeeTodo.getCandidate().getId());

            if (candidate != null) {

                if (candidate.getActionTaken()) {
                    // action is already taken
                    throw new BusinessException(errorMessage = " Action is already taken");
                }


                if (action) {
                    //check if already approved
                    if (hireEmployeeTodo.getApprovedByFinalManager())
                        return false;
                    //set it as approved
                    hireEmployeeTodo.setApprovedByFinalManager(true);
                    officeStaffTodoRepository.save(hireEmployeeTodo);

                    //now check if it has a REMIND_MANAGER To-Do to set it completed and approved
                    OfficeStaffTodo remindManagerTodo = officeStaffTodoRepository.findFirstByTaskNameAndCandidateOrderByCreationDateDesc(OfficeStaffTodoType.REMIND_FINAL_MANAGE_TO_APPROVE_CASE.toString(), hireEmployeeTodo.getCandidate());
                    if (remindManagerTodo != null) {
                        remindManagerTodo.setCompleted(true);
                        remindManagerTodo.setApprovedByFinalManager(true);
                        officeStaffTodoRepository.save(remindManagerTodo);
                    }

                    //create a new OfficeStaff record
                    OfficeStaff officeStaff;
                    if (candidate.getRehired()) {
                        officeStaff = officeStaffRepository.findOne(candidate.getRehiredId());
                    } else {
                        officeStaff = new OfficeStaff();
                    }
                    officeStaff.setName(candidate.getEmployeeName());
                    officeStaff.setFirstName(candidate.getFirstName());
                    officeStaff.setMiddleName(candidate.getMiddleName());
                    officeStaff.setLastName(candidate.getLastName());
                    // set basic salary
                    officeStaff.setBasicSalary(candidate.getBasicSalary());
                    officeStaff.setOldTerminationDate(officeStaff.getTerminationDate());
                    officeStaff.setTerminationDate(null);
                    officeStaff.setOldStartingDate(officeStaff.getStartingDate());
                    if(OfficeStaffType.OVERSEAS_STAFF.equals(candidate.getEmployeeType()))
                        officeStaff.setStartingDate(candidate.getStartingDate());
                    else
                        officeStaff.setPotentialStartDate(candidate.getPotentialStartDate());
                    officeStaff.setExcludedFromPayroll(false);
                    officeStaff.setLocationEnum(candidate.getLocationEnum());
                    officeStaff.setSalaryBeforeRehiring(officeStaff.getSalary());
                    officeStaff.setHousingAllowance(candidate.getHousing());
                    officeStaff.setTrasnportation(candidate.getTransportation());
                    officeStaff.setSalary(candidate.getSalary());
                    officeStaff.setPhoneNumber(candidate.getPhoneNumber());
                    officeStaff.setWeeklyOffDayList(new ArrayList<>(candidate.getWeeklyOffDayList()));
                    officeStaff.setEmployeeType(candidate.getEmployeeType());
                    officeStaff.setJobTitle(candidate.getJobTitle());
                    // set employee manager as the job title manager
                    officeStaff.setEmployeeManager(candidate.getEmployeeManager());
                    officeStaff.setSalaryCurrency(candidate.getSalaryCurrency());
                    officeStaff.setStatus(OfficeStaffStatus.ACTIVE);
                    officeStaff.setWorkingPattern(candidate.getWorkingPattern());
                    officeStaff.setVisaRequired(candidate.getVisaRequired());
                    officeStaff.setOfficeStaffCandidate(candidate);
                    officeStaff.setJazzHRProfile(candidate.getJazzHRProfile());
                    officeStaff.setCountry(candidate.getCountry());
                    officeStaff.setNationality(candidate.getNationality());
                    officeStaff.setCityName(candidate.getCity() != null ? candidate.getCity().getName() : candidate.getCityName());
                    officeStaff.setCity(candidate.getCity());
                    officeStaff.setTeam(candidate.getTeam());
                    for (PicklistItem department : candidate.getDepartments()) {
                        officeStaff.getDepartments().add(department);
                    }
                    officeStaff.setPreferredCommunicationMethod(candidate.getPreferredCommunicationMethod());
                    officeStaff.setBirthDate(candidate.getBirthDate());
                    officeStaff.setNewEmail(candidate.getEmail());
                    officeStaff.setAirfareTicketType(candidate.getAirfareTicketType());
                    //Set zoho recruit info:
                    officeStaff.setZohoJobTitle(candidate.getZohoJobTitle());
                    officeStaff.setZohoExactJobTitle(candidate.getZohoExactJobTitle());
                    officeStaff.setZohoProfile(candidate.getZohoProfile());

                   /* if(OfficeStaffType.DUBAI_STAFF_EXPAT.equals(candidate.getEmployeeType())) // revert this change
                        officeStaff.setExpatStartDate(candidate.getStartingDate());*/
                    //Jira PAY-411
                    officeStaff.setEmail(candidate.getEmail());

                    if (officeStaff.getFinalSettlement() != null) {
                        // de-attach final settlement
                        officeStaff.getFinalSettlement().setOfficeStaff(null);
                        Setup.getRepository(OfficeStaffFinalSettlementRepository.class)
                                .save(officeStaff.getFinalSettlement());
                        officeStaff.setFinalSettlement(null);
                    }
                    officeStaff = officeStaffRepository.save(officeStaff);
                    String defaultAccessesIds = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_AUTOMATICALLY_EMPLOYEE_ACCESSES);
                    for (String accessId : defaultAccessesIds.split(","))
                        if (accessId != null && !accessId.trim().isEmpty())
                            addAccess(officeStaff, Setup.getRepository(ExternalAccessRepository.class).findOne(Long.parseLong(accessId)));

                    Map<String, String> paramValues = new HashMap<>();
                    paramValues.put("employee_name", officeStaff.getFirstLastName());
                    paramValues.put("job_title", officeStaff.getJobTitle().getName());
                    paramValues.put("direct_manager_name", officeStaff.getEmployeeManager().getShortName());
                    paramValues.put("employee_email_address", officeStaff.getEmail());

                    messagingService.sendMessageConsideringPosition(officeStaff, "Payroll_New_Hire_Notification", "New Hire Notification", paramValues);

                    //generate public page and send the link with email to the employee
                    String url = publicPageHelper.generatePublicURL(PublicPageHelper.NEW_STAFF_QUESTIONNAIRE, candidate.getId().toString());

                    List<EmailRecipient> receivers = Recipient.parseEmailsString(Setup.isProduction() ? candidate.getEmail()
                            : Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_TEST_EMAIL_RECEIVER));
                    List<EmailRecipient> ccReceivers = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_NEW_STAFF_QUESTIONNAIRE_CC_EMAIL_RECEIVER));
                    String subject = Setup.getParameter(Setup.getCurrentModule(),
                            PayrollManagementModule.NEW_STAFF_QUESTIONNAIRE_EMAIL_SUBJECT);
                    Map<String ,String > params = new HashMap<>();
                    params.put("url", url);
                    messagingService.send(receivers, ccReceivers, "Payroll_Send_New_Hire_Questionnaire",
                            subject, params, null, null);
//                    TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_Send_New_Hire_Questionnaire", params);
//                    mailService.sendEmail(new MailObject.builder(templateEmail, EmailReceiverType.Office_Staff)
//                            .recipients(receivers)
//                            .cc(ccReceivers)
//                            .html()
//                            .senderName(MessageTemplateService.getMaidsCcSenderName())
//                            .secure()
//                            .build());

                }
                //else {} //action is false then there is nothing to do here

                //set action taken to true
                candidate.setRehireProcessCompleted(true);
                candidate.setActionTaken(true);
                officeStaffCandidateRepository.save(candidate);
                return true;
            }
        }

        return false;
    }

    @Transactional
    public boolean approveNewHireEmployeeTodoFromOfficeStaffListPage(String candidateId, Boolean action) {
        OfficeStaffCandidate candidate = officeStaffCandidateRepository.findOne(Long.parseLong(candidateId));

        if (candidate != null) {

            if (candidate.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }


            if (action) {

                //create a new OfficeStaff record
                OfficeStaff officeStaff = new OfficeStaff();
                officeStaff.setName(candidate.getEmployeeName());
                officeStaff.setFirstName(candidate.getFirstName());
                officeStaff.setMiddleName(candidate.getMiddleName());
                officeStaff.setLastName(candidate.getLastName());
                // set basic salary
                officeStaff.setBasicSalary(candidate.getBasicSalary());
                if(OfficeStaffType.OVERSEAS_STAFF.equals(candidate.getEmployeeType()))
                    officeStaff.setStartingDate(candidate.getStartingDate());
                else
                    officeStaff.setPotentialStartDate(candidate.getPotentialStartDate());
                officeStaff.setLocationEnum(candidate.getLocationEnum());
                officeStaff.setHousingAllowance(candidate.getHousing());
                officeStaff.setTrasnportation(candidate.getTransportation());
                officeStaff.setSalary(candidate.getSalary());
                officeStaff.setPhoneNumber(candidate.getPhoneNumber());
                officeStaff.setWeeklyOffDayList(new ArrayList<>(candidate.getWeeklyOffDayList()));
                officeStaff.setEmployeeType(candidate.getEmployeeType());
                officeStaff.setJobTitle(candidate.getJobTitle());
                // set employee manager as the job title manager
                officeStaff.setEmployeeManager(candidate.getEmployeeManager());
                officeStaff.setSalaryCurrency(candidate.getSalaryCurrency());
                officeStaff.setStatus(OfficeStaffStatus.ACTIVE);
                officeStaff.setWorkingPattern(candidate.getWorkingPattern());
                officeStaff.setVisaRequired(candidate.getVisaRequired());
                officeStaff.setOfficeStaffCandidate(candidate);
                officeStaff.setJazzHRProfile(candidate.getJazzHRProfile());
                officeStaff.setCountry(candidate.getCountry());
                officeStaff.setNationality(candidate.getNationality());
                officeStaff.setCityName(candidate.getCityName());
                officeStaff.setTeam(candidate.getTeam());
                for (PicklistItem department : candidate.getDepartments()) {
                    officeStaff.getDepartments().add(department);
                }
                officeStaff.setPreferredCommunicationMethod(candidate.getPreferredCommunicationMethod());
                officeStaff.setBirthDate(candidate.getBirthDate());
                officeStaff.setNewEmail(candidate.getNewEmail());
                officeStaff.setAirfareTicketType(candidate.getAirfareTicketType());
                //Jira PAY-411
                officeStaff.setEmail(candidate.getEmail());

                officeStaff = officeStaffRepository.save(officeStaff);
                String defaultAccessesIds = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_AUTOMATICALLY_EMPLOYEE_ACCESSES);
                for (String accessId : defaultAccessesIds.split(","))
                    if (accessId != null && !accessId.trim().isEmpty())
                        addAccess(officeStaff, Setup.getRepository(ExternalAccessRepository.class).findOne(Long.parseLong(accessId)));


                Map<String, String> paramValues = new HashMap<>();
                paramValues.put("employee_name", officeStaff.getFirstLastName());
                paramValues.put("job_title", officeStaff.getJobTitle().getName());
                paramValues.put("direct_manager_name", officeStaff.getEmployeeManager().getShortName());
                paramValues.put("employee_email_address", officeStaff.getEmail());

                messagingService.sendMessageConsideringPosition(officeStaff, "Payroll_New_Hire_Notification", "New Hire Notification", paramValues);

                //generate public page and send the link with email to the employee
                String url = publicPageHelper.generatePublicURL(PublicPageHelper.NEW_STAFF_QUESTIONNAIRE, candidate.getId().toString());

                List<EmailRecipient> receivers = Recipient.parseEmailsString(Setup.isProduction() ? candidate.getEmail()
                        : Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_TEST_EMAIL_RECEIVER));
                List<EmailRecipient> ccReceivers = Recipient.parseEmailsString(Setup.isProduction() ?
                        Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_NEW_STAFF_QUESTIONNAIRE_CC_EMAIL_RECEIVER)
                        : Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_TEST_EMAIL_RECEIVER));
                String subject = Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.NEW_STAFF_QUESTIONNAIRE_EMAIL_SUBJECT);
                Map<String ,String> params = new HashMap<>();
                params.put("url", url);
                messagingService.send(receivers, ccReceivers, "Payroll_Send_New_Hire_Questionnaire",
                        subject, params, null, null);
//                TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_Send_New_Hire_Questionnaire", params);
//                mailService.sendEmail(new MailObject.builder(templateEmail, EmailReceiverType.Office_Staff)
//                        .recipients(receivers)
//                        .cc(ccReceivers)
//                        .html()
//                        .senderName(MessageTemplateService.getMaidsCcSenderName())
//                        .secure()
//                        .build());

            }

            //set action taken to true
            candidate.setRehireProcessCompleted(true);
            candidate.setActionTaken(true);
            officeStaffCandidateRepository.save(candidate);
            return true;
        }

        return false;
    }

    private boolean approveOfficeStaffUpdate(String todoId, Boolean action) {
        UpdateOfficeStaffApprove approve = updateOfficeStaffApproveRepository
                .findOne(Long.parseLong(todoId));

        if (approve != null) {

            if (approve.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }

            if (action) {

                OfficeStaff officeStaff = approve.getOfficeStaff();

                Double salary = approve.getUpdateType() == UpdateOfficeStaffApprove.UpdateType.SALARY ? approve.getSalary() : officeStaff.getSalary();
                Double housing = approve.getUpdateType() == UpdateOfficeStaffApprove.UpdateType.SALARY ? approve.getHousing() : officeStaff.getHousingAllowance();
                Double transportation = approve.getUpdateType() == UpdateOfficeStaffApprove.UpdateType.SALARY ? approve.getTransportation() : officeStaff.getTrasnportation();
                Double basic = approve.getUpdateType() == UpdateOfficeStaffApprove.UpdateType.SALARY ? approve.getBasicSalary() : officeStaff.getBasicSalary();
                SalaryCurrency currency = approve.getUpdateType() == UpdateOfficeStaffApprove.UpdateType.SALARY ? approve.getSalaryCurrency() : officeStaff.getSalaryCurrency();
                Date startingDate = approve.getUpdateType() == UpdateOfficeStaffApprove.UpdateType.START_DATE ? approve.getStartingDate() : officeStaff.getStartingDate();

                //update OfficeStaff record

                Date oldStartingDate = officeStaff.getStartingDate();
                // set primary salary
                officeStaff.setBasicSalary(basic);
                officeStaff.setStartingDate(startingDate);
                officeStaff.setHousingAllowance(housing);
                officeStaff.setTrasnportation(transportation);
                officeStaff.setSalary(salary);
                officeStaff.setSalaryCurrency(currency);
               /* if(OfficeStaffType.DUBAI_STAFF_EXPAT.equals(officeStaff.getEmployeeType()))
                    officeStaff.setExpatStartDate(startingDate);*/
                officeStaffRepository.save(officeStaff);

                if (UpdateOfficeStaffApprove.UpdateType.START_DATE.equals(approve.getUpdateType())) {
                    Setup.getApplicationContext().getBean(OfficeStaffUpdateService.class)
                            .sendEmailAfterUpdateStartingDate(officeStaff, oldStartingDate);
                }

//                updateOfficeStaffApproveRepository.delete(approve);
            }
            //else {} //action is false then there is nothing to do here

            //set action taken to true
            approve.setActionTaken(true);
            updateOfficeStaffApproveRepository.save(approve);
            return true;
        }

        return false;
    }

    public Map<String, Object> payrollRosterPage(String todoId, String messageTemplateCode) {
        Map<String, Object> infoMap = new HashMap<>();
        PayrollRosterApproveRequest request = payrollRosterApproveRequestRepository.findOne(Long.parseLong(todoId));

        if (request == null || request.getActionTaken()) {
            // action is already taken
            throw new BusinessException(errorMessage = " Action is already taken");
        }
        infoMap.put("title", (request.getRosterNotes() != null ? "Edited " : "") + "Payroll Roster Of " + DateUtil.formatSimpleMonth(request.getPayrollMonth()) + (request.getForEmarati() ? " - Emirati" : ""));
        infoMap.put("body", pagesBodiesMap.get(messageTemplateCode));
        return infoMap;
    }

    public boolean approvePayrollRoster(String todoId, Boolean action, String notes) {

        PayrollRosterApproveRequest request = payrollRosterApproveRequestRepository.findOne(Long.parseLong(todoId));
        Boolean isEmarati = request.getForEmarati();
        boolean ok = false;
        if (request != null) {
            if (request.getActionTaken()) {
                // action is already taken
                throw new BusinessException(errorMessage = " Action is already taken");
            }
            request.setApproved(action);
            request.setActionTaken(true);
            request.setRosterNotes(notes);
            payrollRosterApproveRequestRepository.save(request);
            ok = true;

            OfficeStaff manager = request.getManager();
            OfficeStaffCandidate candidate = manager.getOfficeStaffCandidate();
            if (candidate == null) {
                candidate = new OfficeStaffCandidate();
                candidate.setEmployeeName(manager.getName());
                candidate.setFirstName(manager.getFirstName());
                candidate.setMiddleName(manager.getMiddleName());
                candidate.setLastName(manager.getLastName());
                candidate.setJobTitle(manager.getJobTitle());
                candidate.setPhoneNumber(manager.getPhoneNumber());
                candidate.setEmail(manager.getEmail());
                candidate.setMockCandidate(true);
                Setup.getRepository(OfficeStaffCandidateRepository.class).save(candidate);
                manager.setOfficeStaffCandidate(candidate);
                Setup.getRepository(OfficeStaffRepository.class).save(manager);
            }
            if (!action) {
                OfficeStaffTodo todo = new OfficeStaffTodo();
                todo.setCandidate(candidate);
                todo.setTaskName(OfficeStaffTodoType.X_DECLINED_HIS_ROSTER_FILE.toString());
                todo.setLabel((request.getManager() != null ? request.getManager().getShortName() : "X") + " Declined Their Roster File" + (isEmarati ? " - Emirati" : ""));
                todo.setCompleted(false);
                todo.setStopped(false);
                todo.setRosterApproveRequest(request);
                officeStaffTodoRepository.save(todo);
            }
        }

        java.sql.Date payrollMonth = new java.sql.Date(new LocalDate().withDayOfMonth(1).toDate().getTime());
        List<PayrollRosterApproveRequest> requests = payrollRosterApproveRequestRepository
                .findByPayrollMonthAndFinalManagerFalseAndForEmarati(payrollMonth, isEmarati);
        boolean sendToAllFinalManagers = true;
        for (PayrollRosterApproveRequest payrollRosterApproveRequest : requests) {
            if (!payrollRosterApproveRequest.getActionTaken() || !payrollRosterApproveRequest.getApproved())
                sendToAllFinalManagers = false;
        }

        if (sendToAllFinalManagers && !request.getFinalManager()) {
            //insert a background task to sendApprovalMails for FINAL MANAGERS
            Setup.getApplicationContext()
                    .getBean(BackgroundTaskService.class)
                    .addDirectCallBackgroundTaskForEntity(
                            "sendApprovalMails", "payrollRosterApprovalService", "payroll",
                            "sendApprovalMails",
                            null, null, false,
                            false, new Class[]{Boolean.class, Boolean.class}, new Object[]{true,isEmarati});
        } else if (sendToAllFinalManagers) {
            //PAY-830 check if all roster requests are approved regardless if they are for emarati or not, then send an email to Adeeb and Cinda
            // PAY-1299 send a separated email for Cinda and Adeeb when the Final Manager Roster requests are approved (one for Emarati and one for Overseas & Expat)
            if (payrollRosterApproveRequestRepository.countByPayrollMonthAndApprovedFalseAndForEmarati(payrollMonth, isEmarati) == 0) {
                Integer countOtherType = payrollRosterApproveRequestRepository.countByPayrollMonthAndApprovedFalseAndForEmarati(payrollMonth, !isEmarati);

                // in case it's not for Emirates and we still have an Emirates pending approval roster then wh don't nee to send anything
                if (!(!isEmarati && countOtherType != 0)) {

                    if (isEmarati) {
                        List<EmailRecipient> emiratesRecipients = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.EMIRATES_ROSTER_APPROVAL_EMAIL_RECEIVERS));
                        String emiratesSubject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.EMIRATES_ROSTER_APPROVAL_EMAIL_SUBJECT);
                        TextEmail textEmail = new TextEmail(emiratesSubject, emiratesSubject);
                        Setup.getMailService().sendEmail(emiratesRecipients, textEmail, EmailReceiverType.Office_Staff);
                    }

                    if (!isEmarati || countOtherType==0) {
                        List<EmailRecipient> recipients = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_ROSTER_REQUESTS_ARE_APPROVED_EMAIL_RECEIVERS));
                        String subject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_ROSTER_REQUESTS_ARE_APPROVED_EMAIL_SUBJECT);
                        TextEmail mail = new TextEmail(subject, subject);
                        Setup.getMailService().sendEmail(recipients, mail, EmailReceiverType.Office_Staff);
                    }
                }
            }
        }

        return ok;
    }


    /**
     * get the type of the new staff according to the needed case from received token
     *
     * @param tokenLoad : will represent here the officeStaffCandidate id
     * @return
     */
    public Map<String, Object> getNewStaffQuestionnairePageInfo(String tokenLoad) {

        Map<String, Object> info = new HashMap();
        OfficeStaffCandidate candidate = officeStaffCandidateRepository.getOne(Long.parseLong(tokenLoad));
        if (candidate == null)
            throw new RuntimeException(errorMessage = "Couldn't find the candidate!");

        //check if it already has a submitted questionnaire before (if there is a CONFIRM_PAYROLL_QUESTIONNAIRE To-Do)
        if (officeStaffTodoRepository.countByTaskNameAndCandidate(OfficeStaffTodoType.CONFIRM_PAYROLL_QUESTIONNAIRE.toString(), candidate) > 0)
            throw new BusinessException(errorMessage = "already submited before");
        // Check if Overseas switched to Expat, so he must Fill Expat questionnaire info.
        info.put("type",candidate.getEmployeeType().toString());
        info.put("employeeName", candidate.getEmployeeName());
        return info;
    }

    /**
     * save the data entered by employee (candidate) and create a new To-Do for Trustee to approve it
     *
     * @param tokenLoad
     * @param questionnaireCandidate
     * @return
     */
    @Transactional
    public boolean submitQuestionnaire(String tokenLoad, OfficeStaffCandidate questionnaireCandidate) {
        OfficeStaffCandidate candidate = officeStaffCandidateRepository.getOne(Long.parseLong(tokenLoad));
        if (candidate == null)
            throw new RuntimeException(errorMessage = "Couldn't find the candidate!");

        if (officeStaffTodoRepository.existsByTaskNameAndCandidateAndCompleted(OfficeStaffTodoType.CONFIRM_PAYROLL_QUESTIONNAIRE.toString(), candidate, false))
            throw new BusinessException(errorMessage = "Already submitted before!");

        // Normalize phone numbers
        if (questionnaireCandidate.getPhoneNumber() != null && !questionnaireCandidate.getPhoneNumber().isEmpty()) {
            String normalizedPhoneNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(questionnaireCandidate.getPhoneNumber());
            if (!normalizedPhoneNumber.equals(questionnaireCandidate.getPhoneNumber())) {
                questionnaireCandidate.setPhoneNumber(normalizedPhoneNumber);
            }
            questionnaireCandidate.setPhoneNumber(PhoneNumberUtil.normalizeInternationalPhoneNumber(questionnaireCandidate.getPhoneNumber()));
        }

        if (questionnaireCandidate.getEmergencyContactPhoneNumber() != null && !questionnaireCandidate.getEmergencyContactPhoneNumber().isEmpty()) {
            String normalizedEmergencyNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(questionnaireCandidate.getEmergencyContactPhoneNumber());
            if (!normalizedEmergencyNumber.equals(questionnaireCandidate.getEmergencyContactPhoneNumber())) {
                questionnaireCandidate.setEmergencyContactPhoneNumber(normalizedEmergencyNumber);
            }
        }

        if (questionnaireCandidate.getSelectedTransferDestination() != null
                && questionnaireCandidate.getSelectedTransferDestination().getSelfReceiver() != null && !questionnaireCandidate.getSelectedTransferDestination().getSelfReceiver()
                && questionnaireCandidate.getSelectedTransferDestination().getPhoneNumber() != null && !questionnaireCandidate.getSelectedTransferDestination().getPhoneNumber().isEmpty()) {
            TransferDestination destination = questionnaireCandidate.getSelectedTransferDestination();
            String normalizedPhoneNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(destination.getPhoneNumber());

            if (!normalizedPhoneNumber.equals(destination.getPhoneNumber())) {
                destination.setPhoneNumber(normalizedPhoneNumber);
            }
        }

        //save received data
        candidate.copyFromOtherCandidate(
                questionnaireCandidate.getNationality(), questionnaireCandidate.getCountry(),
                questionnaireCandidate.getFullNameInArabic(),
                questionnaireCandidate.getCityName(), questionnaireCandidate.getFullAddress(),
                //...PAY-899...//
                questionnaireCandidate.getEmergencyContactName(), questionnaireCandidate.getEmergencyContactPhoneNumber(),
                questionnaireCandidate.getTransferDestinations(), questionnaireCandidate.getDocuments(),
                questionnaireCandidate.getEidNumber(), questionnaireCandidate.getFirstName(),
                questionnaireCandidate.getMiddleName(), questionnaireCandidate.getLastName(),
                questionnaireCandidate.getEmployeeManager(), questionnaireCandidate.getPreferredCommunicationMethod(),
                questionnaireCandidate.getBirthDate(), questionnaireCandidate.getNewEmail(),
                questionnaireCandidate.getCity());

        // Clear some choices based on selected receiver & payment method
        //questionnaireService.clearAndValidate(candidate);
        officeStaffCandidateRepository.save(candidate);

        //create a CONFIRM_PAYROLL_QUESTIONNAIRE To-Do for Trustee
        OfficeStaffTodo confirmQuestionnaireTodo = new OfficeStaffTodo(OfficeStaffTodoType.CONFIRM_PAYROLL_QUESTIONNAIRE.toString());
        confirmQuestionnaireTodo.setCandidate(candidate);
        officeStaffTodoRepository.save(confirmQuestionnaireTodo);

        messagingService.notifyPayrollTrustee(confirmQuestionnaireTodo);
        return true;
    }


//    /**
//     * Return Access information page
//     *
//     * @param tokenLoad
//     * @return
//     */
//    public Map<String, Object> getAccessInformationPageInfo(String tokenLoad) {
//
//        String[] tokenLoadParts = tokenLoad.split("#");
//        if (tokenLoadParts.length != 2)
//            throw new RuntimeException(errorMessage = "missing data in the token.");
//
//        GrantAccessRequest request = Setup.getRepository(GrantAccessRepository.class)
//                .findOne(Long.parseLong(tokenLoadParts[0]));
//        ExternalAccess access = Setup.getRepository(ExternalAccessRepository.class)
//                .findOne(Long.parseLong(tokenLoadParts[1]));
//
//        if (request == null)
//            throw new RuntimeException(errorMessage = "Couldn't find the related request!");
//
//        //check if it already has granted the access
//        if (request.getApproved())
//            throw new RuntimeException(errorMessage = "Access has been already granted");
//
//        //check if the access is not existed in the request
//        if (!request.getExternalAccesses().contains(access))
//            throw new RuntimeException(errorMessage = "Access has been already granted");
//
//        Map<String, Object> body = new HashMap<>();
//        body.put("title", "Please mention how the following employees will access " + access.getName());
//        body.put("body", tokenLoad);
//        List<AccessNote> accessNoteList = Setup.getRepository(AccessNoteRepository.class).findByGrantAccessRequestAndAccess(request, access);
//        body.put("accessNotes", accessNoteList);
//        return body;
//    }

//    /**
//     * Return Revoke access page
//     *
//     * @param tokenLoad
//     * @return
//     */
//    public Map<String, String> getRevokeAccessPageInfo(String tokenLoad) {
//
//        String[] tokenLoadParts = tokenLoad.split("#");
//        if (tokenLoadParts.length != 2)
//            throw new RuntimeException(errorMessage = "missing data in the token.");
//        String entityId = tokenLoadParts[0];
//        long handlerId = Long.parseLong(tokenLoadParts[1]);
//
//        RevokeAccessRequest request = Setup.getRepository(RevokeAccessRepository.class)
//                .findOne(Long.parseLong(entityId));
//        if (request == null)
//            throw new RuntimeException(errorMessage = "Couldn't find the related request!");
//
//        int handlerAccesses = 0;
//        for (ExternalAccess externalAccess : request.getExternalAccesses()) {
//            handlerAccesses += externalAccess.getAdmin().getId().equals(handlerId) ? 1 : 0;
//        }
//        //check if it already has been revoked
//        if (request.getApproved() || handlerAccesses == 0)
//            throw new RuntimeException(errorMessage = "Access has been already revoked!");
//
//        Map<String, String> body = new HashMap<>();
//        body.put("title", "Revoke access for " + request.getEmployeeGrantAccess().getName());
//        return body;
//    }

    /**
     * Return inform terminated staff page
     *
     * @param tokenLoad
     * @return
     */
    public Map<String, Object> getInformTerminatedStaffPageInfo(String tokenLoad) {

        String[] tokenLoadParts = tokenLoad.split("#");
        if (tokenLoadParts.length != 2)
            throw new RuntimeException(errorMessage = "missing data in the token.");
        String entityId = tokenLoadParts[0];

        OfficeStaff staff = Setup.getRepository(OfficeStaffRepository.class)
                .findOne(Long.parseLong(entityId));
        if (staff == null)
            throw new RuntimeException(errorMessage = "Couldn't find the related employee!");

        Map<String, Object> body = new HashMap<>();
        body.put("title", "");
        body.put("body", String.format("%s is informed about the termination", staff.getFirstLastName()));
        body.put("offboardingFlow", getOffBoardingFlow(staff));

        return body;
    }

    /**
     * Return inform terminated staff page
     *
     * @param tokenLoad
     * @return
     */
    public HousemaidPayslipProjection getHousemaidPayslip(String tokenLoad) {
        String[] tokenLoadParts = tokenLoad.split("#");
        if (tokenLoadParts.length != 3)
            throw new RuntimeException(errorMessage = "missing data in the token.");
        String housemaidId = tokenLoadParts[0];
        String date = tokenLoadParts[1];
        String lang = tokenLoadParts[2];

        java.sql.Date payrollMonth = java.sql.Date.valueOf(date);

        Page<HousemaidPayslipProjection> projections = payslipService.getHousemaidPayslips(payrollMonth, null,
                null, null, Long.parseLong(housemaidId), lang, null);

        return projections.getContent().get(0);
    }

    /**
     * @param tokenLoad
     * @return
     */
    public Map<String, Object> getQuestionAboutMaidSalary(String tokenLoad) {
        Map<String, Object> infoMap = new HashMap<>();

        String[] tokenLoadParts = tokenLoad.split("#");
        if (tokenLoadParts.length != 1)
            throw new RuntimeException(errorMessage = "missing data in the token.");
        String questionAboutSalaryId = tokenLoadParts[0];

        QuestionAboutMaidSalary questionAboutMaidSalary = Setup.getRepository(QuestionAboutMaidSalaryRepository.class).getOne(Long.parseLong(questionAboutSalaryId));
        if (questionAboutMaidSalary.getActionTaken())
            throw new BusinessException(errorMessage = "Action has been already taken!");

        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).getOne(questionAboutMaidSalary.getHousemaid().getId());
        String title = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_QUESTION_ABOUT_SALARY_EMAIL_TITLE);
        title = title.replace("@maid_name@", housemaid.getName());

        Map params = new HashMap();
        params.put("maid_name", housemaid.getName());
        params.put("amount", housemaid.getSalaryWithCurrency());
        params.put("nationality", housemaid.getNationality() != null ? housemaid.getNationality().getName() : "");
        params.put("start_date", DateUtil.formatDateSlashed(housemaid.getStartDate()));
        params.put("client_name", housemaid.getCurrentClient() != null ? housemaid.getCurrentClient().getName() : "");
        params.put("auditor_notes", questionAboutMaidSalary.getNotes());

        String pageGeneralBody = pagesBodiesMap.get("Payroll_Question_About_Maid_Salary");
        infoMap.put("body", generateBodyMessageText(pageGeneralBody, params));
        infoMap.put("title", title);

        return infoMap;
    }

//    /**
//     * Approve grant access
//     *
//     * @param tokenLoad
//     * @return
//     */
//    @Transactional
//    public boolean approveGrantAccess(String tokenLoad) {
//
//        String[] tokenLoadParts = tokenLoad.split("#");
//        if (tokenLoadParts.length != 2)
//            throw new RuntimeException(errorMessage = "missing data in the token.");
//        GrantAccessRequest request = Setup.getRepository(GrantAccessRepository.class)
//                .findOne(Long.parseLong(tokenLoadParts[0]));
//        ExternalAccess access = Setup.getRepository(ExternalAccessRepository.class)
//                .findOne(Long.parseLong(tokenLoadParts[1]));
//
//        if (request == null)
//            throw new RuntimeException(errorMessage = "Couldn't find the related request!");
//
//        //check if it already has granted the access
//        if (request.getApproved())
//            throw new RuntimeException(errorMessage = "Access has been already granted");
//
//        //check if the access is not existed in the request
//        if (!request.getExternalAccesses().contains(access))
//            throw new RuntimeException(errorMessage = "Access has been already granted");
//
//        for (AccessNote accessNote : Setup.getRepository(AccessNoteRepository.class).findByGrantAccessRequestAndAccess(request, access)) {
//
//            addAccess(accessNote.getOfficeStaff(), access);
//            OfficeStaff staff = Setup.getRepository(OfficeStaffRepository.class).findOne(accessNote.getOfficeStaff().getId());
//            try {
//                manageAccessEmailService.sendAddAccessEmail(access, staff, accessNote);
//                manageAccessEmailService.sendAddAccessEmailToManagerWhoRequested(request, access, staff);
//            } catch (Exception e) {
//                Logger.getLogger(PublicPageService.class.getName()).log(Level.SEVERE, "Unable to send grant access email #" + request.getId(), e);
//                throw new RuntimeException(errorMessage = "Unable to send the access email to the target employee, please check his email address!");
//            }
//        }
//
//        request.getExternalAccesses().remove(access);
//        if (request.getExternalAccesses().isEmpty()) {
//            request.setApproved(true);
//        }
//        Setup.getRepository(GrantAccessRepository.class).save(request);
//        return true;
//    }

//    /**
//     * Approve revoke access
//     *
//     * @param tokenLoad
//     * @return
//     */
//    @Transactional
//    public boolean approveRevokeAccess(String tokenLoad) {
//
//        String[] tokenLoadParts = tokenLoad.split("#");
//        if (tokenLoadParts.length != 2)
//            throw new RuntimeException(errorMessage = "missing data in the token.");
//        String entityId = tokenLoadParts[0];
//        long handlerId = Long.parseLong(tokenLoadParts[1]);
//
//        RevokeAccessRequest request = Setup.getRepository(RevokeAccessRepository.class)
//                .findOne(Long.parseLong(entityId));
//        if (request == null)
//            throw new RuntimeException(errorMessage = "Couldn't find the related request!");
//
//        //check if it already has been revoked
//        if (request.getApproved())
//            throw new RuntimeException(errorMessage = "Access has been already revoked!");
//
//        List<ExternalAccess> removedAccesses = new ArrayList<>();
//
//        OfficeStaff manager = request.getEmployeeGrantAccess().getEmployeeManager();
//        OfficeStaff finalManager = request.getEmployeeGrantAccess().getFinalManager();
//
//        for (ExternalAccess externalAccess : request.getExternalAccesses()) {
//            if (externalAccess.getAdmin().getId().equals(handlerId)) {
//                removeAccess(request.getEmployeeGrantAccess(), externalAccess);
//                removedAccesses.add(externalAccess);
//            }
//            RevokeAccessNotifyMail revokeAccessNotifyMail = new RevokeAccessNotifyMail(externalAccess.getName(), request.getEmployeeGrantAccess(), manager, finalManager, false);
//            revokeAccessNotifyMailRepository.save(revokeAccessNotifyMail);
//        }
//
////
////        if (manager != null && manager.getEmail() != null) {
////            try {
////                manageAccessEmailService.sendRevokeAccessEmail(manager, request.getEmployeeGrantAccess().getFirstLastName(), removedAccesses);
////            } catch (Exception e) {
////                Logger.getLogger(PublicPageService.class.getName()).log(Level.SEVERE, "Unable to send revoke access email for direct manager #" + request.getId(), e);
////            }
////        }
////
////
////        if (finalManager != null && finalManager.getEmail() != null &&
////                (manager == null || !finalManager.getId().equals(manager.getId()))) {
////            try {
////                manageAccessEmailService.sendRevokeAccessEmail(finalManager, request.getEmployeeGrantAccess().getFirstLastName(), removedAccesses);
////            } catch (Exception e) {
////                Logger.getLogger(PublicPageService.class.getName()).log(Level.SEVERE, "Unable to send revoke access email for final manager #" + request.getId(), e);
////            }
////        }
//
//        request.getExternalAccesses().removeAll(removedAccesses);
//
//        if (request.getExternalAccesses().isEmpty()) {
//            request.setApproved(true);
//        }
//
//        if (request.getExternalAccesses().isEmpty() && request.getEmployeeGrantAccess().getTerminationDate() != null) {
//            officeStaffFinalSettlementService.allAccessesRevoked(request.getEmployeeGrantAccess());
//        }
//
//        Setup.getRepository(RevokeAccessRepository.class).save(request);
//        return true;
//    }

    /**
     * Return inform terminated staff page
     *
     * @param tokenLoad
     * @return
     */
    public boolean approveInformTerminatedStaff(String tokenLoad) {

        String[] tokenLoadParts = tokenLoad.split("#");
        if (tokenLoadParts.length != 2)
            throw new RuntimeException(errorMessage = "missing data in the token.");
        String entityId = tokenLoadParts[0];

        OfficeStaff officeStaff = Setup.getRepository(OfficeStaffRepository.class)
                .findOne(Long.parseLong(entityId));
        if (officeStaff == null)
            throw new RuntimeException(errorMessage = "Couldn't find the related employee!");


        Map<String, String> parameters = new HashMap<>();
        parameters.put("employee_name", officeStaff.getFirstLastName());

        this.messagingService.sendMessageConsideringPosition(officeStaff, "Payroll_OfficeStaff_Terminated",
                officeStaff.getName()+" no longer works with us", parameters);

        Setup.getRepository(OfficeStaffRepository.class).save(officeStaff);

        return true;
    }

    public Map<String, Object> getFinalSettlementForOverseas(String tokenLoad) {
        Map<String, Object> infoMap = new HashMap<>();
        OfficeStaffFinalSettlement finalSettlement = Setup.getRepository(OfficeStaffFinalSettlementRepository.class).findOne(Long.parseLong(tokenLoad));
        if (finalSettlement == null) {
            throw new RuntimeException(errorMessage = "can't find related final settlement!");
        }

        if (finalSettlement.getActionTaken()) {
            throw new BusinessException(errorMessage = "Action has been already taken!");
        }

        OfficeStaff officeStaff = finalSettlement.getOfficeStaff();
        if (officeStaff.getEmployeeType() == OfficeStaffType.OVERSEAS_STAFF) {
            String text = String.format("%s is requested for termination. The reason of termination is: %s.  Their termination compensation is: %s.<br><br>" +
                            "Note that their prorated salary for this month is: %s", officeStaff.getFirstLastName(),
                    officeStaff.getTerminationReason().getName(),
                    officeStaff.getSalaryCurrency() + " " + NumberFormatter.formatNumber(officeStaff.getCompensation()),
                    officeStaff.getSalaryCurrency() + " " + NumberFormatter.formatNumber(finalSettlement.getSalaryOfLastMonth()));

            infoMap.put("title", "Termination Compensation");
            infoMap.put("text", text);
            infoMap.put("compensation", officeStaff.getCompensation());
            infoMap.put("terminationNotes", officeStaff.getTerminationNotes());
        } else {
            throw new RuntimeException("this context is not available (meta)");
        }
        return infoMap;
    }

    public Map<String, Object> getFinalSettlementForExpatAndEmirati(String tokenLoad, long generateTime) {
        Map<String, Object> infoMap = new HashMap<>();
        OfficeStaffFinalSettlement finalSettlement = Setup.getRepository(OfficeStaffFinalSettlementRepository.class).findOne(Long.parseLong(tokenLoad));
        if (finalSettlement == null) {
            throw new RuntimeException(errorMessage = "can't find related final settlement!");
        }
        if (!finalSettlement.isValidURL(generateTime)) {
            throw new RuntimeException(errorMessage = "Action has been already taken!");
        }
        OfficeStaff officeStaff = finalSettlement.getOfficeStaff();
        if (officeStaff.getEmployeeType() == OfficeStaffType.OVERSEAS_STAFF) {
            throw new RuntimeException("this context is not available (meta)");
        }
        infoMap.put("officeStaff", finalSettlement.getOfficeStaff());
        infoMap.put("finalSettlement", finalSettlement);
        infoMap.put("actions", finalSettlement.getActions());
        infoMap.put("notes", finalSettlement.getNotes());
        infoMap.put("fileNames",finalSettlement.getFileNames());
        return infoMap;
    }

    //just for Expat and Emarati for Payroll Manager Page (ERP)
    public Map<String, Object> getFinalSettlement(OfficeStaffFinalSettlement finalSettlement) {
        Map<String, Object> infoMap = new HashMap<>();
        if (finalSettlement == null) {
            throw new RuntimeException(errorMessage = "can't find related final settlement!");
        }

        OfficeStaff officeStaff = finalSettlement.getOfficeStaff();
        if (officeStaff.getEmployeeType() == OfficeStaffType.OVERSEAS_STAFF)
            throw new RuntimeException("Wrong employee type.");

        infoMap.put("officeStaff", finalSettlement.getOfficeStaff());
        infoMap.put("finalSettlement", finalSettlement);
        infoMap.put("actions", finalSettlement.getActions());
        infoMap.put("notes", finalSettlement.getNotes());
//        infoMap.put("offboardingFlow", getOffBoardingFlow(officeStaff));
//        infoMap.put("needRejectionNotes", true);
        return infoMap;
    }

    public boolean approveFinalSettlementForOverseas(String tokenLoad, OfficeStaffFinalSettlement updateFinalSettlement, boolean action, String notes) {
        OfficeStaffFinalSettlement finalSettlement = Setup.getRepository(OfficeStaffFinalSettlementRepository.class).findOne(Long.parseLong(tokenLoad));


        if (finalSettlement != null) {
            if (finalSettlement.getActionTaken())
                // action is already taken by manager
                throw new BusinessException(errorMessage = " Action is already taken");

            finalSettlement.setActionTaken(true);
            OfficeStaff officeStaff = finalSettlement.getOfficeStaff();

            if (officeStaff.getEmployeeType() == OfficeStaffType.OVERSEAS_STAFF) {
                Map<String, String> paramValues = new HashMap<>();
                paramValues.put("employee_name", officeStaff.getFirstLastName());
                paramValues.put("termination_compensation_amount", NumberFormatter.formatNumber(updateFinalSettlement.getCompensation()));
                paramValues.put("action", action ? "approved" : "rejected");
                OfficeStaff finalManager = officeStaff.getFinalManager();
                paramValues.put("final_manager", finalManager.getShortName());


                Set<OfficeStaff> receivers = new HashSet<>();
                // get all managers
                OfficeStaff curr = officeStaff;
                while (curr.getEmployeeManager() != null && !curr.getEmployeeManager().getId().equals(curr.getId())) {
                    receivers.add(curr.getEmployeeManager());
                    curr = curr.getEmployeeManager();
                }

                for (OfficeStaff manager : receivers) {
//                    messageTemplateService.sendMessageOrEmail(
//                            "Termination compensation is rejected/approved",
//                            manager,
//                            "Payroll_Action_Taken_About_Compensation",
//                            paramValues);
                    messagingService.send("Payroll_Action_Taken_About_Compensation", "Termination compensation is rejected/approved",
                            null, manager, paramValues, officeStaff, null, manager);
                }

                PendingApprovalRequest pendingApprovalRequest = pendingApprovalRequestRepository.findTopByTokenLoadOrderByIdDesc(tokenLoad);
                if (pendingApprovalRequest != null) {
                    pendingApprovalRequest.setApproved(true);
                    pendingApprovalRequest.setApprovedBy(CurrentRequest.getUser());
                    pendingApprovalRequestRepository.save(pendingApprovalRequest);
                }
            }

            Double amount;
            if (officeStaff.getEmployeeType() == OfficeStaffType.OVERSEAS_STAFF) {
                amount = updateFinalSettlement.getCompensation();
            } else {
                amount = (double) updateFinalSettlement.getNet();
            }

            //send notification to trustee
            String link = shortener.shorten(PublicPageHelper.getModuleBaseAngular13Url() + "office-staff-payroll/" + officeStaff.getId() + "/termination");
            messagingService.notifyPayrollTrusteeAboutRequest(officeStaff, officeStaff.getFinalManager(), "termination compensation with amount "+NumberFormatter.formatNumber(amount), finalSettlement.getCreationDate(), notes, link, action);

            if (!action) {
                officeStaffFinalSettlementController.updateEntity(finalSettlement);
                return true;
            }

            finalSettlement.setCompensation(updateFinalSettlement.getCompensation());

            finalSettlement.setApprovedByCFO(true);

            Date payrollEndDate = PayrollGenerationLibrary.getPayrollEndDate(new LocalDate(finalSettlement.getPayrollDate())).plusDays(1).toDate();

            if (new Date().getTime() < payrollEndDate.getTime()) {
                finalSettlement.setIncludeInPayroll(true);
            }

            officeStaffFinalSettlementController.updateEntity(finalSettlement);
            return true;
        }

        return false;
    }

    @Transactional
    public void sendFinalSettlementToCFO(OfficeStaffFinalSettlement finalSettlement) {

        if (finalSettlement != null) {
            //if(finalSettlement.getApprovedByManager())
            // action is already taken by manager
            //    throw new BusinessException(errorMessage = " Action is already taken");
            OfficeStaff staff = officeStaffRepository.getOne(finalSettlement.getOfficeStaff().getId());
            finalSettlement.setApprovedByManager(true);
            Setup.getRepository(OfficeStaffFinalSettlementRepository.class).save(finalSettlement);

            String usersIds = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.CFO_FINAL_SETTLEMENT_USER_ID);
            List<String> idsList = Arrays.asList(usersIds.split(";"));

            for(String userId : idsList) {
                User user = userRepository.findOne(Long.parseLong(userId));

                EmailRecipient recipient = new Recipient(user.getEmail() != null ? user.getEmail() : null, user.getName() != null ? user.getName() : null);
                List<EmailRecipient> recipients = new ArrayList<>();
                recipients.add(recipient);

                String publicPage = publicPageHelper.generatePublicURL(PublicPageHelper.FINAL_SETTLEMENT_CFO, finalSettlement.getId().toString(), userId);

                OfficeStaff lastManager = staff.getLastManagerOtherThanCFO();
                String subject = "Revised Final Settlement of " + staff.getFirstLastName();
                Map<String, String> params = new HashMap<>();
                params.put("firstLastName", staff.getFirstLastName());
                params.put("url", publicPage);
                params.put("finalManagerName", lastManager.getShortName());
                messagingService.send(recipients, null, "Payroll_Final_Settlement_Of_OfficeStaff_To_CFO",
                        subject, params, null, null);
//            mailService.sendEmail(new MailObject.builder(new TemplateEmail(subject, "Payroll_Final_Settlement_Of_OfficeStaff_To_CFO", params), EmailReceiverType.Office_Staff)
//                    .recipients(recipients)
//                    .html()
//                    .senderName(MessageTemplateService.getMaidsCcSenderName())
//                    .secure()
//                    .build());
            }
        }

    }


    @Transactional
    public void addAccess(OfficeStaff employee, ExternalAccess externalAccess) {

        if (externalAccess != null) {
            employee = Setup.getRepository(OfficeStaffRepository.class).findOne(employee.getId());
            OfficeStaffAccessRepository accessRepository = Setup.getRepository(OfficeStaffAccessRepository.class);
            OfficeStaffRepository employeeRepository = Setup.getRepository(OfficeStaffRepository.class);
            ExternalAccessRepository repository = Setup.getRepository(ExternalAccessRepository.class);

            OfficeStaffAccess osa = accessRepository.findByEmployeeAndAccess(
                    employee,
                    externalAccess);
            if (osa == null) {
                osa = new OfficeStaffAccess(employee,
                        externalAccess,
                        true);
            } else {
                osa.setHasAccess(true);
            }
            accessRepository.save(osa);

            employee.getEmployeeAccesses()
                    .add(externalAccess);
            employeeRepository.save(employee);

            externalAccess = repository.findOne(externalAccess.getId());
            externalAccess.setTeams(externalAccess.getTeams());
            repository.save(externalAccess);
        }
    }

//    @Transactional
//    public void removeAccess(OfficeStaff employee, ExternalAccess access) {
//
//        OfficeStaffAccessRepository accessRepository = Setup.getRepository(OfficeStaffAccessRepository.class);
//        OfficeStaffRepository employeeRepository = Setup.getRepository(OfficeStaffRepository.class);
//        ExternalAccessRepository repository = Setup.getRepository(ExternalAccessRepository.class);
//
//        OfficeStaffAccess osa = accessRepository.findByEmployeeAndAccess(
//                employee,
//                access);
//        if (osa == null) {
//        } else {
//            osa.setHasAccess(false);
//            accessRepository.delete(osa);
//
//            employee.getEmployeeAccesses()
//                    .remove(access);
//            employeeRepository.save(employee);
//
//            access = repository.findOne(access.getId());
//            access.setTeams(access.getTeams());
//            repository.save(access);
//        }
//    }

    public ResponseEntity<?> getPendingApprovalPageInfo(String tokenLoad) {
        OfficeStaff manager = officeStaffRepository.findOne(Long.parseLong(tokenLoad));
        if (manager != null){
            BaseControllerHelper baseControllerHelper = Setup.getApplicationContext().getBean(BaseControllerHelper.class);
            SelectQuery<BackgroundTask> taskSelectQuery = new SelectQuery<>(BackgroundTask.class);
            taskSelectQuery.filterBy("relatedEntityType", "=", "PendingApprovalRequest");
            taskSelectQuery.filterBy("status", "NOT IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
            taskSelectQuery.filterBy("name", "=", "managerApproveRequest");
            List<BackgroundTask> tasks = taskSelectQuery.execute();
            List<String> pendingRequestsPublicLinks = tasks.stream().map(backgroundTask -> backgroundTask.getParameters() != null ? backgroundTask.getParameters().replace("[","").replace("]","").replace("\"","") : "").collect(Collectors.toList());
            if (pendingRequestsPublicLinks != null && !pendingRequestsPublicLinks.isEmpty())
                return  ResponseEntity.ok(baseControllerHelper.project(Setup.getRepository(PendingApprovalRequestRepository.class).getByManagerAndPublicLinkNotIn(manager, pendingRequestsPublicLinks), PendingApprovalRequestCEOProjection.class));
            else
                return ResponseEntity.ok(baseControllerHelper.project(Setup.getRepository(PendingApprovalRequestRepository.class).getByManager(manager), PendingApprovalRequestCEOProjection.class));
        }
        return ResponseEntity.ok(new ArrayList<>());
    }

    public List<Map<String, Object>> getOffBoardingFlow(OfficeStaff staff) {
        List<Map<String, Object>> offBoardingFlowList = new ArrayList<>();
        Map<String, Object> offBoardingFlowMap = new HashMap<>();

        OfficeStaffFinalSettlement finalSettlement = Setup.getRepository(OfficeStaffFinalSettlementRepository.class).findFirstByOfficeStaffOrderByCreationDateDesc(staff);

        // 1- termination request
        offBoardingFlowMap.put("label", "Request Termination");
        offBoardingFlowMap.put("status", staff.getTerminationDate() != null ? true : false);
        offBoardingFlowList.add(offBoardingFlowMap);

        // 2- approve final settlement / termination compensation
        offBoardingFlowMap = new HashMap<>();
        offBoardingFlowMap.put("label", OfficeStaffType.OVERSEAS_STAFF.equals(staff.getEmployeeType()) ? "Approve Termination Compensation" : "Approve Final Settlement");
        offBoardingFlowMap.put("status", finalSettlement != null && finalSettlement.getActionTaken() && finalSettlement.getApprovedByCFO() ? true : false);
        offBoardingFlowList.add(offBoardingFlowMap);

        // 3- revoke all accesses
        offBoardingFlowMap = new HashMap<>();
        offBoardingFlowMap.put("label", "Revoke All Accesses");
        offBoardingFlowMap.put("status", staff.getExternalAccesses() == null || staff.getExternalAccesses().size() == 0 ? true : false);
        offBoardingFlowList.add(offBoardingFlowMap);

        // 4- inform employee about termination
        offBoardingFlowMap = new HashMap<>();
        offBoardingFlowMap.put("label", "Inform Employee About Termination");
        offBoardingFlowMap.put("status", OfficeStaffStatus.TERMINATED.equals(staff.getStatus()) ? true : false);
        offBoardingFlowList.add(offBoardingFlowMap);

        //5- employee terminated
        offBoardingFlowMap = new HashMap<>();
        offBoardingFlowMap.put("label", "Employee Terminated");
        offBoardingFlowMap.put("status", OfficeStaffStatus.TERMINATED.equals(staff.getStatus()) ? true : false);
        offBoardingFlowList.add(offBoardingFlowMap);

        return offBoardingFlowList;
    }
    public Map<String, Object> getCFOQuestion(String tokenLoad){
        OfficeStaff cfo = officeStaffRepository.findOne(Long.valueOf(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CEO_ID)));
        FinalSettlementNote question = finalSettlementNoteRepository.findOne(Long.parseLong(tokenLoad));
        if(question == null){
            throw new RuntimeException("Question is not found");
        }
        if(question.isAnswered()){
            throw new RuntimeException(errorMessage = " Action is already taken");
        }
        Map<String, Object> response = new HashMap<>() ;
        response.put("cfoName",cfo.getFirstLastName());
        response.put("question",question.getText());
        return response ;
    }

    public boolean submitAnswerForCFO(String tokenLoad , String answer){
        FinalSettlementNote question = finalSettlementNoteRepository.findOne(Long.parseLong(tokenLoad));
        if(question == null){
            throw new RuntimeException("The Related Question is not found");
        }
        if(question.isAnswered()){
            throw new RuntimeException(errorMessage = " Action is already taken");
        }
        if(question.getUser() == null){
            throw new RuntimeException(errorMessage = " User is not found");
        }
        FinalSettlementNote answerFromUser = new FinalSettlementNote();
        answerFromUser.setFinalSettlement(question.getFinalSettlement());
        answerFromUser.setText(answer);
        answerFromUser.setNoteType(FSNoteType.ANSWER);
        answerFromUser.setUser(question.getUser());
        question.setAnswered(true);

        finalSettlementNoteRepository.save(question);
        finalSettlementNoteRepository.save(answerFromUser);

        String usersIds = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.CFO_FINAL_SETTLEMENT_USER_ID);
        List<String> idsList = Arrays.asList(usersIds.split(";"));

        for(String userId : idsList) {
            User user = userRepository.findOne(Long.parseLong(userId));

            EmailRecipient recipient = new Recipient(user.getEmail() != null ? user.getEmail() : null, user.getName() != null ? user.getName() : null);
            List<EmailRecipient> recipients = new ArrayList<>();
            recipients.add(recipient);

            String publicPage = publicPageHelper.generatePublicURL(PublicPageHelper.FINAL_SETTLEMENT_CFO, question.getFinalSettlement().getId().toString(), userId);
            String subject = "Answer by " + question.getUser().getName();
            Map<String, String> params = new HashMap<>();
            params.put("answer", answerFromUser.getText());
            params.put("question", question.getText());
            params.put("url", publicPage);
            messagingService.send(recipients, null, "User_Answer_About_Final_Settlement_Of_OfficeStaff",
                    subject, params, new ArrayList<>(), null);

//        mailService.sendEmail(new MailObject.builder(new TemplateEmail(subject, "User_Answer_About_Final_Settlement_Of_OfficeStaff", params), EmailReceiverType.Office_Staff)
//                .recipients(recipients)
//                .html()
//                .senderName(MessageTemplateService.getMaidsCcSenderName())
//                .secure()
//                .build());
        }
        return true ;
    }

    public List<Attachment> generationDocumentsAndAddToFinalSettlement(OfficeStaffFinalSettlement finalSettlement){
        List<Attachment> result = new ArrayList<>();
        try {
            WordTemplate wordFinalSettlement = wordTemplateRepository.findByCodeIgnoreCase(PayrollManagementModule.PAYROLL_OFFICESTAFF_FINAL_SETTLEMENT);
            WordTemplate wordResignationLetter = wordTemplateRepository.findByCodeIgnoreCase(PayrollManagementModule.PAYROLL_OFFICESTAFF_RESIGNATION_LETTER);
            XWPFDocument documentFinalSettlement = new XWPFDocument(Storage.getStream(wordFinalSettlement.getAttachment("template")));
            XWPFDocument documentResignationLetter = new XWPFDocument(Storage.getStream(wordResignationLetter.getAttachment("template")));
            ByteArrayOutputStream outFinalSettlement = new ByteArrayOutputStream();
            ByteArrayOutputStream outResignationLetter = new ByteArrayOutputStream();
            documentFinalSettlement.write(outFinalSettlement);
            documentResignationLetter.write(outResignationLetter);
            InputStream inputStreamFinalSettlement = new ByteArrayInputStream(outFinalSettlement.toByteArray());
            inputStreamFinalSettlement = wordTemplateService.generateDocument(inputStreamFinalSettlement, new HashMap<>());
            InputStream inputStreamResignationLetter = new ByteArrayInputStream(outResignationLetter.toByteArray());
            inputStreamResignationLetter = wordTemplateService.generateDocument(inputStreamResignationLetter, new HashMap<>());
            Attachment attFinalSettlement = Storage.storeTemporary("Final Settlement.pdf", inputStreamFinalSettlement, PayrollManagementModule.PAYROLL_OFFICESTAFF_FINAL_SETTLEMENT +"_NOT_SIGNED", Boolean.TRUE);
            Attachment attResignationLetter = Storage.storeTemporary("Resignation Letter.pdf", inputStreamResignationLetter, PayrollManagementModule.PAYROLL_OFFICESTAFF_RESIGNATION_LETTER +"_NOT_SIGNED", Boolean.TRUE);
            result.add(attFinalSettlement);
            result.add(attResignationLetter);
            finalSettlement.addAttachment(attFinalSettlement);
            finalSettlement.addAttachment(attResignationLetter);
            finalSettlementRepository.save(finalSettlement);
            return result ;

        }catch (Exception e){
            e.printStackTrace();
        }
        return result ;
    }

    public void addAttachmentsToFS(List<Attachment> attachments, OfficeStaffFinalSettlement finalSettlement) {
        List<OfficeStaffDocument> officeStaffDocuments = new ArrayList<>();
        OfficeStaffDocument officeStaffDocument = null;
        for (Attachment att : attachments) {
            Attachment oldAttachment = finalSettlement.getAttachment(att.getTag());
            if (oldAttachment == null || !att.getId().equals(oldAttachment.getId())) {
                String officeStaffDocumentName = StringHelper.enumToCapitalizedFirstLetter(att.getTag()) + " - " + att.getName();
                String officeStaffOldDocumentName = oldAttachment != null ? (StringHelper.enumToCapitalizedFirstLetter(oldAttachment.getTag()) + " - " + oldAttachment.getName()) : officeStaffDocumentName;

                officeStaffDocument = Setup.getRepository(OfficeStaffDocumentRepository.class)
                        .findTopByOfficeStaffAndTypeAndNameOrderByCreationDateDesc(finalSettlement.getOfficeStaff(),
                                OfficeStaffDocumentType.FINAL_SETTLEMENT, officeStaffOldDocumentName);
                if (officeStaffDocument == null) {
                    officeStaffDocument = new OfficeStaffDocument();
                    officeStaffDocument.setOfficeStaff(finalSettlement.getOfficeStaff());
                    officeStaffDocument.setType(OfficeStaffDocumentType.FINAL_SETTLEMENT);
                }
                officeStaffDocument.setUploadDate(new Date());
                officeStaffDocument.setName(officeStaffDocumentName);
                officeStaffDocument.setAttachments(Collections.singletonList(Storage.cloneTemporary(att, att.getTag())));
                List<Attachment> newAttachments = finalSettlement.getAttachments();
                newAttachments.removeIf(attachment -> Objects.equals(attachment.getTag(), att.getTag()));
                newAttachments.add(att);
                finalSettlement.setAttachments(newAttachments);
                Setup.getRepository(OfficeStaffDocumentRepository.class).save(officeStaffDocument);
            }
        }
    }

    @Transactional
    public boolean approveOverseasToExpatSwitching(String entityId, Boolean action,String notes) {
        OfficeStaff officeStaff = officeStaffRepository.findOne(Long.parseLong(entityId));
        if(officeStaff == null){
            throw  new BusinessException("OfficeStaff Id is incorrect!");
        }
        if(action){
            OfficeStaffCandidate officeStaffCandidate=officeStaff.getOfficeStaffCandidate();
            /*officeStaff.setEmployeeType(OfficeStaffType.DUBAI_STAFF_EXPAT);
            officeStaff.setExpatStartDate(officeStaffCandidate.getExpatStartDate());
            officeStaff.setOverseasToExpatSwitched(true);
            officeStaff.setSalaryAsOverseas(officeStaff.getSalary());
            officeStaff.setInitialCurrency(officeStaff.getSalaryCurrency());
            officeStaff.setSalary(officeStaffCandidate.getSalary());
            officeStaff.setBasicSalary(officeStaffCandidate.getBasicSalary());
            officeStaff.setHousingAllowance(officeStaffCandidate.getHousing());
            officeStaff.setTrasnportation(officeStaffCandidate.getTransportation());
            officeStaff.setSalaryCurrency(SalaryCurrency.AED);
            officeStaff.setLocation(PicklistHelper.getItem("locations", "uae"));
            officeStaff.setSwitchingToExpatDate(new Date());
            officeStaff.setVisaRequired(true);*/

            officeStaff.setPotentialStartDate(officeStaffCandidate.getPotentialStartDate());
            officeStaff.setLocationEnum(officeStaffCandidate.getLocationEnum());
            officeStaff.setOverseasToExpatSwitched(true);
            officeStaff.setVisaRequired(true);

            officeStaffRepository.save(officeStaff);

            //generate public page and send the link with email to the employee
            String url = publicPageHelper.generatePublicURL(PublicPageHelper.NEW_STAFF_QUESTIONNAIRE, officeStaffCandidate.getId().toString());

            List<EmailRecipient> receivers = Recipient.parseEmailsString(Setup.isProduction() ? officeStaff.getEmail()
                    : Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_TEST_EMAIL_RECEIVER));
            List<EmailRecipient> ccReceivers = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_NEW_STAFF_QUESTIONNAIRE_CC_EMAIL_RECEIVER));
            String subject = Setup.getParameter(Setup.getCurrentModule(),
                    PayrollManagementModule.UPDATE_INFO_QUESTIONNAIRE_EMAIL_SUBJECT);
            Map<String ,String > params = new HashMap<>();
            params.put("url", url);
            messagingService.send(receivers, ccReceivers, "Payroll_Send_New_Hire_Questionnaire",
                    subject, params, null, null);
            // create NewRequest visa for OfficeStaff:
            Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                    .addDirectCallBackgroundTaskForEntity(
                            "createNewRequestVisaForSwitchingToExpat",
                            "newRequestService",
                            "visa",
                            "createNewRequestForSwitchingExpatOfficeStaff",
                            null, null, false, false,
                            new Class[] { Long.class},
                            new Object[] { officeStaff.getId()});
            // Remove existing Questionnaire of this candidate:
             List<OfficeStaffTodo> officestaffCandidateQuestionnaireTodos= officeStaffTodoRepository.findByTaskNameAndCandidate(OfficeStaffTodoType.CONFIRM_PAYROLL_QUESTIONNAIRE.toString(), officeStaffCandidate);
             if(!officestaffCandidateQuestionnaireTodos.isEmpty())
                 officestaffCandidateQuestionnaireTodos.stream().forEach(todo-> officeStaffTodoRepository.delete(todo));


        }
        else {
            OfficeStaffCandidate officeStaffCandidate=officeStaff.getOfficeStaffCandidate();
            officeStaffCandidate.setRejectionNotes(notes);
            officeStaffCandidate.setEmployeeType(OfficeStaffType.OVERSEAS_STAFF);
            officeStaffCandidate.setOverseasToExpatSwitched(null);
            officeStaffCandidateRepository.save(officeStaffCandidate);
        }
        return true;
    }

}
