package com.magnamedia.service.message;

import com.google.common.base.Strings;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.entity.template.TemplateMessage;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SendTemplateMessageRequest;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.Shortener;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.core.repository.PositionRepository;
import com.magnamedia.core.repository.TagRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.type.*;
import com.magnamedia.entity.*;
import com.magnamedia.extra.CardStatus;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.PayrollAuditTodoTaskNameType;
import com.magnamedia.helper.*;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.OfficeStaffStatus;
import com.magnamedia.repository.*;
import com.magnamedia.repository.HousemaidRepository;
import com.magnamedia.repository.MonthlyPaymentRuleRepository;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.service.HousemaidService;
import com.magnamedia.service.payroll.generation.newversion.HousemaidPayrollAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.extra.AnsariPaymentMethod.BANK_TRANSFER;
import static com.magnamedia.helper.PublicPageHelper.getResourceLinkFromAttachment;
import static java.util.logging.Level.SEVERE;

@Service
public class MessagingService {

    @Autowired
    private Shortener shortener;

    @Autowired
    private TemplateUtil templateUtil;

    @Autowired
    private InterModuleConnector interModuleConnector;

    @Autowired
    private HousemaidService housemaidService;

    private static final Logger logger = Logger.getLogger(MessagingService.class.getName());

    String PAYROLL_AUDITOR_POSITION = "payroll_auditor";


    public Boolean send(String templateName, String type, String lang, BaseEntity recipient, Map<String, String> parameters, Object context, Map<String, ? extends AppAction> cta, BaseEntity owner, String specificEmail) {

        if (context instanceof Client){
            List<Contract> activeContracts = ((Client) context).getActiveContracts();
            if (activeContracts != null && activeContracts.size() == 1){
                context = activeContracts.get(0);
            }else if (!(owner instanceof Client)){
                context = owner;
            }
        }

        Template template = TemplateUtil.getTemplate(templateName);
        if (template == null)
            return false;
        SpecialBehaviorConfig config = new SpecialBehaviorConfig(template, lang, context, parameters, cta);

        //Prepare Target
        MessageTarget target = new MessageTarget(recipient, owner, context);
        if (specificEmail != null && !specificEmail.isEmpty()) {
            target.setEmail(specificEmail);
        }
        if (type != null && !type.isEmpty()) {
            target.setEmailSubject(type);
            target.setNotificationTitle(type);
        }
        config.addTarget(target);

        // pre send (execute all method in class PreSendAction)
        preSend(template, config);

        // send depend on result of preSend
        List<TemplateMessage> messages = templateUtil.send(template, config.getLang(), config.getChannelSpecificSettingType(), config.getTargetList(), config.getContext(), config.getCta(), config.getParameters());

        // post send (execute all method in class PostSendAction)
        postSend(template, config, messages);

        return true;
    }

    public Boolean send(String templateName, String type, String lang, BaseEntity recipient, Map<String, String> parameters, Object context, Map<String, ? extends AppAction> cta, BaseEntity owner) {
        return send(templateName, type, lang, recipient, parameters, context, cta, owner, null);
    }

    public void send(List<EmailRecipient> recipients, List<EmailRecipient> ccRecipients, String templateName, String subject, Map<String, String> paramValues, List<Attachment> attachmentList, Object context) {

        Template template = TemplateUtil.getTemplate(templateName);

        if (template == null)
            return;

        if (context instanceof Client){
            List<Contract> activeContracts = ((Client) context).getActiveContracts();
            if (activeContracts != null && activeContracts.size() == 1){
                context = activeContracts.get(0);
            }
        }

        SpecialBehaviorConfig config = new SpecialBehaviorConfig(template, null, context, paramValues, null);

        //Prepare Target
        if (recipients != null && !recipients.isEmpty()) {
            MessageTarget target = new MessageTarget();
            target.setEmailRecipients(recipients);
            if (ccRecipients != null && !ccRecipients.isEmpty()) {
                target.setCc(ccRecipients);
            }
            if (attachmentList != null && !attachmentList.isEmpty()) {
                target.setEmailAttachments(attachmentList);
            }
            if (subject != null && !subject.isEmpty()) {
                target.setEmailSubject(subject);
            }
            config.addTarget(target);
        }

        // pre send (execute all method in class PreSendAction)
        preSend(template, config);

        // send depend on result of preSend
        List<TemplateMessage> messages = templateUtil.send(template, config.getLang(), config.getChannelSpecificSettingType(), config.getTargetList(), config.getContext(), config.getCta(), config.getParameters());

        // post send (execute all method in class PostSendAction)
        postSend(template, config, messages);

        return;

    }

    public Boolean send(List<EmailRecipient> recipients, List<EmailRecipient> ccRecipients, String templateName, String subject, String lang, BaseEntity recipient, Map<String, String> parameters, Object context, Map<String, ? extends AppAction> cta, BaseEntity owner) {

        Template template = TemplateUtil.getTemplate(templateName);
        if (template == null)
            return false;
        SpecialBehaviorConfig config = new SpecialBehaviorConfig(template, lang, context, parameters, cta);

        //Prepare Target
        if (recipients != null && !recipients.isEmpty()) {
            if (recipient != null) {
                MessageTarget target = new MessageTarget(recipient, owner);
                target.setEmailRecipients(recipients);
                if (ccRecipients != null && !ccRecipients.isEmpty()) {
                    target.setCc(ccRecipients);
                }
                if (subject != null && !subject.isEmpty()) {
                    target.setEmailSubject(subject);
                }
                config.addTarget(target);
            } else {
                MessageTarget target = new MessageTarget();
                target.setEmailRecipients(recipients);
                if (ccRecipients != null && !ccRecipients.isEmpty()) {
                    target.setCc(ccRecipients);
                }
                if (subject != null && !subject.isEmpty()) {
                    target.setEmailSubject(subject);
                }
                config.addTarget(target);
            }
        }

        // pre send (execute all method in class PreSendAction)
        preSend(template, config);

        // send depend on result of preSend
        List<TemplateMessage> messages = templateUtil.send(template, config.getLang(), config.getChannelSpecificSettingType(), config.getTargetList(), config.getContext(), config.getCta(), config.getParameters());

        // post send (execute all method in class PostSendAction)
        postSend(template, config, messages);

        return true;
    }

    private List<EmailRecipient> getEmailRecipients(String recipients) {
        if (recipients != null && !recipients.isEmpty()) {
            if (recipients.contains(";")) {
                return EmailHelper.getMailRecipients(recipients);
            } else {
                return Recipient.parseEmailsString(recipients);
            }
        }
        return null;
    }

    private void preSend(Template template, SpecialBehaviorConfig config) {

        PreSendAction preSendAction = new PreSendAction();
        preSendAction.action1(template, config);
        preSendAction.action2(template, config);
        preSendAction.action3(template, config);

//        List<String> methods = Arrays.stream(PreSendAction.class.getDeclaredMethods()).map(Method::getName).collect(Collectors.toList());
//        PreSendAction preSendAction = new PreSendAction();
//        methods.forEach(s -> {
//            try {
//                Method action = PreSendAction.class.getDeclaredMethod(s, Template.class, SpecialBehaviorConfig.class);
//                action.invoke(preSendAction, template, config);
//            } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
//                throw new RuntimeException(e);
//            }
//        });
    }

    private void postSend(Template template, SpecialBehaviorConfig config, List<TemplateMessage> messages) {
//        List<String> methods = Arrays.stream(PostSendAction.class.getDeclaredMethods()).map(Method::getName).collect(Collectors.toList());
//        PostSendAction postSendAction = new PostSendAction();
//        methods.forEach(s -> {
//            try {
//                Method action = PostSendAction.class.getDeclaredMethod(s, Template.class, SpecialBehaviorConfig.class, List.class);
//                action.invoke(postSendAction, template, config, messages);
//            } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
//                throw new RuntimeException(e);
//            }
//        });
//        Setup.getRepository(TemplateMessageRepository.class).save(messages);
    }


    /***********************************************************/
    // Send With logic
    @Async
    public void sendMessageConsideringPosition(OfficeStaff officeStaff, String messageTemplate, String messageType, Map<String, String> paramValues) {
        if (officeStaff == null || officeStaff.getEmployeeManager() == null || officeStaff.getJobTitle() == null)
            return;

        boolean isManager = isManager(officeStaff);

        Set<OfficeStaff> receivers = new HashSet<>();
        if (!isManager) {
            // get all managers
            OfficeStaff curr = officeStaff;
            while (curr.getEmployeeManager() != null && !curr.getEmployeeManager().getId().equals(curr.getId())) {
                receivers.add(curr.getEmployeeManager());
                curr = curr.getEmployeeManager();
            }

            if (receivers.isEmpty()) {
                OfficeStaff finalManager = officeStaff.getFinalManager();
                // always should be true
                if (finalManager != null) receivers.add(finalManager);
            }
            // get all colleagues
            if (officeStaff.getEmployeeManager() != null) {
                receivers.addAll(Setup.getRepository(OfficeStaffRepository.class)
                        .findByEmployeeManager(officeStaff.getEmployeeManager()));
            }

        } else {
            // get all subordinates
            receivers.addAll(Setup.getRepository(OfficeStaffRepository.class)
                    .findByEmployeeManager(officeStaff));

            // get all other managers
            List<Tag> managers = Setup.getRepository(TagRepository.class)
                    .findByNameIgnoreCase("JOB_TITLE_MANAGER_TAG");

            List<Tag> managers2 = Setup.getRepository(TagRepository.class)
                    .findByNameIgnoreCase("manager");

            if (!managers.isEmpty()) {
                receivers.addAll(Setup.getRepository(OfficeStaffRepository.class)
                        .findAllManagers(managers.get(0)));
            }

            if (!managers2.isEmpty()) {
                receivers.addAll(Setup.getRepository(OfficeStaffRepository.class)
                        .findAllManagers(managers2.get(0)));
            }
        }

        if (receivers.contains(officeStaff)) {
            receivers.remove(officeStaff);
        }

        for (OfficeStaff staff : receivers) {
            if (staff.getStatus() == OfficeStaffStatus.ACTIVE && staff.getPhoneNumber() != null) {
                send(messageTemplate, messageType, null, staff, paramValues, staff, null, staff);
            }
        }

    }

    public void sendSmsToOfficeStaffManagers(OfficeStaff officeStaff, String template, String type, Map<String, String> paramValues) {

        List<OfficeStaff> receivers = new ArrayList<>();
        OfficeStaff manager = officeStaff.getEmployeeManager();
        if (manager == null) {
            throw new BusinessException("This employee has no manager!");
        }
        OfficeStaff finalManager = officeStaff.getFinalManager();

        receivers.add(manager);
        if (!manager.getId().equals(finalManager.getId()))
            receivers.add(finalManager);

        for (OfficeStaff staff : receivers) {
            send(template, type, null, staff, paramValues, officeStaff, null, officeStaff);
        }
        return;
    }

    // send with secure
    public void notifyPayrollTrusteeAboutRequest(OfficeStaff staff, OfficeStaff manager, String requestType, Date requestDate, String notes, String link, boolean approved) {
        List<EmailRecipient> recipients = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_TRUSTEE_EMAIL));
        String subject = "Payroll Change Approval Notification";
        String messageTemplate = approved ? "Payroll_Request_Trustee_Approval_Notification" : "Payroll_Request_Trustee_Rejection_Notification";
        Map<String, String> paramValues = new HashMap<>();
        paramValues.put("employee_name", staff.getName());
        paramValues.put("manager_name", manager != null ? manager.getShortName() : "__");
        paramValues.put("request_type", requestType);
        paramValues.put("request_date", DateUtil.formatDateSlashed(requestDate));
        paramValues.put("link_of_tab", link);
        if (!approved)
            paramValues.put("notes", notes);
        send(recipients, null, messageTemplate, subject, paramValues, null, null);
//        TemplateEmail templateEmail = new TemplateEmail(subject, messageTemplate, paramValues);
//        MailService mailService = Setup.getMailService();
//
//        for (EmailRecipient recipient : recipients) {
//            mailService.sendEmail(new MailObject.builder(templateEmail, EmailReceiverType.Office_Staff)
//                    .recipient(recipient)
//                    .html()
//                    .secure()
//                    .build());
//        }
    }

    public void notifyAuditorsAboutIncludedMaidsTodo(PayrollAuditTodo payrollAuditTodo) {
        Position position = Setup.getRepository(PositionRepository.class).findByCode(PAYROLL_AUDITOR_POSITION);
        if (position != null) {
            List<User> auditors = Setup.getRepository(UserRepository.class).findByPositionOrderByFullNameAsc(position);
            for (User auditor : auditors) {
                if (!auditor.isEnabled() || Strings.isNullOrEmpty(auditor.getEmail())) {
                    continue;
                }

                String link = "";

                String subject;
                String templateName;
                String managerName = "";

                if (PayrollAuditTodoTaskNameType.CC_HOUSEMAIDS_INCLUDED_BY_MANAGER.toString().equals(payrollAuditTodo.getTaskName())) {
                    subject = "CC maids that manager included in payroll";
                    String ccManagerId = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CC_MANAGER_USER_ID);
                    if (ccManagerId != null && !"".equals(ccManagerId)) {
                        User ccManager = Setup.getRepository(UserRepository.class).findOne(Long.parseLong(ccManagerId));
                        managerName = ccManager.getFullName();
                    }
                    link = shortener.shorten(PublicPageHelper.getModuleBaseUrl() + PublicPageHelper.CC_INCLUDED_MAIDS + "/" + payrollAuditTodo.getId());
                } else {
                    subject = "MV maids that manager included in payroll";
                    String mvManagerId = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_MV_MANAGER_USER_ID);
                    if (mvManagerId != null && !"".equals(mvManagerId)) {
                        User mvManager = Setup.getRepository(UserRepository.class).findOne(Long.parseLong(mvManagerId));
                        managerName = mvManager.getFullName();
                    }
                    link = shortener.shorten(PublicPageHelper.getModuleBaseUrl() + PublicPageHelper.MV_INCLUDED_MAIDS + "/" + payrollAuditTodo.getId());
                }

                List<EmailRecipient> recipients = Recipient.parseEmailsString(auditor.getEmail());
                Map<String, String> params = new HashMap<>();
                params.put("manager_name", managerName);
                params.put("url", link);
                send(recipients, null, "Payroll_Maids_Included_By_Manager_Todo_Email", subject, params, null, null);
//                TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_Maids_Included_By_Manager_Todo_Email", params);
//                Setup.getMailService().sendEmail(recipients, templateEmail, null);
            }
        }
    }

    public void notifyClientAboutMaidSalary(List<String> housemaidIds, boolean received, Date receiveDate) {
        for (String idAsString : housemaidIds) {
            try {
                Long id = Long.parseLong(idAsString);
                Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(id);
                if (housemaid == null) {
                    continue;
                }

                if (housemaid.getNotifySalaryRelease()) {
                    Client client = housemaid.getCurrentClient();
                    if (client != null) {
                        notifyClientAboutMaidSalary(client, housemaid, received, receiveDate);
                    }
                }
            } catch (Exception e) {
                logger.info(e.getMessage() + Arrays.toString(e.getStackTrace()));
            }
        }
    }

    public void notifyClientAboutMaidSalary(Client client, Housemaid housemaid, boolean received, Date receiveDate) {
        try {
            String notificationType = "";
            Contract contract = housemaid.getActiveContract();
            Map<String, String> params = new HashMap<>();
            if (received) {
                notificationType = "Payroll_Maid_Salary_Transferred_Notification";
                params.put("maid_name", housemaid.getName());
            } else {
                notificationType = "Payroll_Maid_Salary_On_Hold_Notification";
                String reason = HousemaidStatus.ON_VACATION.equals(housemaid.getStatus()) ? "was on vacation" : (HousemaidStatus.NO_SHOW.equals(housemaid.getStatus()) ? "was absent when salaries were released" : "");
                params.put("reason", reason);
                params.put("date", DateUtil.formatDateSlashed(receiveDate));
            }
            AppAction action1 = new AppAction();
            action1.setText("Where and how can my maid receive her salary");
            action1.setType(AppActionType.BUTTON);
            action1.setFunctionType(FunctionType.NAVIGATE);
            action1.setNavigationType(NavigationType.INAPP);
            action1.setAppRouteName("/receive_salary_instructions");
            action1.setAppRouteArguments(new HashMap() {{
                put("housemaidId", housemaid.getId());
                put("contractId", contract != null ? contract.getId() : null);
                put("maidName", housemaid.getFirstName());
            }});

            Map<String, AppAction> context = new HashMap<>();
            context.put("action1", action1);

            send(notificationType, notificationType, null, client, params, housemaid, context, housemaid);
//            notificationService.pushNotification(client, notificationType, null, null, notificationType, params, context, client.getNormalizedMobileNumber(), client.getId(), client.getEntityType());
        } catch (Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception while notifyClientAboutMaidSalary for " + housemaid.getName(), false);
        }
    }

    public void notifyMvClientAboutMaidSalary(Client client, Housemaid housemaid){
        try {
            String notificationType = "Pay_MV_Sal_notify";
            Map<String, String> params = new HashMap<>();
            params.put("maid_name", housemaid.getFirstName());
            params.put("link", "@link_1@");


            AppAction action_1 = new AppAction();
            action_1.setText("Get " + housemaid.getFirstName() + "'s gov. documents & salary info");
            action_1.setType(AppActionType.LINK);
            action_1.setFunctionType(FunctionType.NAVIGATE);
            action_1.setNavigationType(NavigationType.INAPP);
            action_1.setAppRouteName("/docs");
            action_1.setAppRouteArguments(new HashMap());

            Map<String, AppAction> context = new HashMap<>();
            context.put("link_1", action_1);
            send(notificationType, notificationType, null, client, params, housemaid, context, housemaid);
//            notificationService.pushNotification(client, notificationType, null, null, notificationType, params, context, client.getNormalizedMobileNumber(), client.getId(), client.getEntityType());
        }catch (Exception e){
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception while notifyMvClientAboutMaidSalary for " + housemaid.getName(), false);
        }
    }

    public void notifyMaidAboutHerSalaryOnYaya(Housemaid housemaid, String salary, boolean withClient, String url){
        try {
            String notificationType = "";
            Map<String, String> params = new HashMap<>();
            params.put("salary_amount", salary);
            params.put("url", url);

            Map<String, AppAction> context = new HashMap<>();
//            HashMap<String, Object> action1 = new HashMap<>();
            AppAction action_1 = new AppAction();
            action_1.setType(AppActionType.BUTTON);
            action_1.setFunctionType(FunctionType.NAVIGATE);
            action_1.setNavigationType(NavigationType.INAPP);
            action_1.setHyperlink(url);
            action_1.setAppRouteArguments(new HashMap<>());
//            action1.put("type", AppActionType.BUTTON);
//            action1.put("functionType", FunctionType.NAVIGATE);
//            action1.put("navigationType", NavigationType.INAPP);
//            action1.put("hyperlink", url);
//            action1.put("appRouteArguments", new HashMap<>());

            if (withClient) {
                notificationType = "Payroll_Inform_WithClient_Maid_About_Salary_After_Yaya_Notification";
                action_1.setText("Go to My Salary Information");
                action_1.setAppRouteName("/questionAboutSalary");
//                action1.put("text", "Go to My Salary Information");
//                action1.put("appRouteName", "/questionAboutSalary");
            } else {
                notificationType = "Payroll_Inform_InAccommodation_Maid_About_Salary_After_Yaya_Notification";
                action_1.setText("View Payslip");
                action_1.setAppRouteName("/payslip_route");
//                action1.put("text", "View Payslip");
//                action1.put("appRouteName", "/payslip_route");
            }

            context.put("@action1@", action_1);
            send(notificationType, notificationType, housemaid.getYayaAppNotificationLang(), housemaid, params, housemaid, context, housemaid);
//            notificationService.pushNotification(housemaid, notificationType, null, null, notificationType, housemaid.getYayaAppNotificationLang(), params, context, housemaid.getNormalizedPhoneNumber(), housemaid.getId(), housemaid.getEntityType());
        }catch (Exception e){
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception while notifyMaidAboutHerSalaryOnYaya for " + housemaid.getName(), false);
        }
    }

    public void notifyClientMaidSalaryIsDaley(Client client, String  paymentDateWithFormat, String maidName) {
        Map<String,String> paramValue = new HashMap<>();
        paramValue.put("maid_name", maidName);
        paramValue.put("payment_date", paymentDateWithFormat);
        paramValue.put("greetings", Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSVISA));

        try {
            send("Salary_Is_Delayed_Because_Update_Monthly_Payment_Rule_Payment_Date", "Salary_Is_Delayed_Because_Update_Monthly_Payment_Rule_Payment_Date",
                    null, client, paramValue, client, null, client);
//            PushNotification notification = pushNotification(client,
//                    "Salary_Is_Delayed_Because_Update_Monthly_Payment_Rule_Payment_Date",
//                    "Salary_Is_Delayed_Because_Update_Monthly_Payment_Rule_Payment_Date",
//                    null,
//                    paramValue,
//                    new HashMap(),
//                    client.getNormalizedMobileNumber(),
//                    true,
//                    DateUtil.remainingHoursTillNextDayAt(9),
//                    client.getId(),
//                    client.getEntityType()
//            );
        }catch (Exception e){
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception while notifyClientMaidSalaryIsDaley for " + client.getName(), false);
        }

    }

    public void notifyPayrollTrustee(OfficeStaffTodo officeStaffTodo) {
        List<EmailRecipient> recipients = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_TRUSTEE_EMAIL));
        String link = shortener.shorten(PublicPageHelper.getModuleBaseAngular13Url() + "office-staff-onboarding");

        String subject = "Payroll Manager TO-DO Notification";

        Map<String, String> params = new HashMap<>();
        params.put("todoLabel", StringHelper.enumToCapitalizedFirstLetter(officeStaffTodo.getTaskName()));
        params.put("url", link);
//        TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_Payroll_Manager_TODO_Notification", params);
//        MailService mailService = Setup.getMailService();
        send(recipients, null, "Payroll_Payroll_Manager_TODO_Notification", subject, params, null, null);
//        for (EmailRecipient recipient : recipients) {
//            mailService.sendEmail(recipient, templateEmail, null);
//        }
    }

    public void notifyAuditors(PayrollAuditTodo payrollAuditTodo) {
        if (payrollAuditTodo == null)
            return;

        Position position = Setup.getRepository(PositionRepository.class).findByCode(PAYROLL_AUDITOR_POSITION);
        if(position != null) {
            String link = shortener.shorten(PublicPageHelper.getModuleBaseUrl() + "auditors-to-do");

            String subject;
            String templateName;

            if ("EXCLUDED_MAID_VISA".equals(payrollAuditTodo.getTaskName())) {
                subject = Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.PARAMETER_MV_EXCLUDED_AUDIT_TODO_EMAIL_SUBJECT);
                templateName = "Payroll_Excluded_MV_Audit_TODO_Notification";
            }else{
                subject = "Payroll Audit TO-DO Notification";
                templateName = "Payroll_Audit_TODO_Notification";
            }

            Map<String, String> params = new HashMap<>();
            params.put("todoLabel",payrollAuditTodo.getLabel());
            params.put("url",link);
//            TemplateEmail templateEmail = new TemplateEmail(subject, templateName, params);

            List<User> auditors = Setup.getRepository(UserRepository.class).findByPositionOrderByFullNameAsc(position);
            for(User auditor: auditors) {
                if(!auditor.isEnabled() || Strings.isNullOrEmpty(auditor.getEmail())) {
                    continue;
                }

                //String testReceiver = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_TEST_EMAIL_RECEIVER);
                List<EmailRecipient> recipients = Recipient.parseEmailsString(auditor.getEmail());
                send(recipients, null, templateName, subject, params, null, null);
//                Setup.getMailService().sendEmail(recipients, templateEmail, null);
            }
        }
    }

    public void notifyAccountants(PayrollAccountantTodo accountantTodo) {
        String email = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PAYROLL_PAYMENT_TODO_NOTIFICATION_EMAIL);
        List<EmailRecipient> recipients = Recipient.parseEmailsString(email);
        String link = shortener.shorten(PublicPageHelper.getOtherModuleBaseUrl("accounting") + "/accountant-todo");

        String subject = "Payroll Payment TO-DO Notification";
        Map<String, String> params = new HashMap<>();
        params.put("todoLabel", accountantTodo.getLabel());
        params.put("url", link);
//        TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_Payment_TODO_Notification", params);

//        MailService mailService = Setup.getMailService();

        send(recipients, null, "Payroll_Payment_TODO_Notification", subject, params, null, null);
//        for (EmailRecipient recipient : recipients)
//            mailService.sendEmail(recipient, templateEmail, null);

    }

    @Transactional
    public boolean notifyAllEligibleMaidsBT(Long ruleId, java.sql.Date paymentDate) {
        BackgroundTaskService backgroundTaskService = Setup.getApplicationContext().getBean(BackgroundTaskService.class);
        MonthlyPaymentRule rule = Setup.getRepository(MonthlyPaymentRuleRepository.class).findOne(ruleId);
        List<Housemaid> includedHousemaidCCList = Setup.getApplicationContext().getBean(HousemaidPayrollAuditService.class)
                .getTargetListForHousemaidType(rule, true);
//        Map<String, String> paramValue = new HashMap<>();
//        paramValue.put("month", DateUtil.formatMonth(rule.getPayrollMonth()));
//        paramValue.put("payment_date", DateUtil.formatDateDashed(paymentDate));

        String month = DateUtil.formatMonth(rule.getPayrollMonth());

        int chunkSize = 100;
        try {
            chunkSize = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CHUNK_SIZE_FOR_BGT));
        }catch (Exception ignored){
        }
        List<String> includedHousemaidCCIds = includedHousemaidCCList.stream().map(housemaid -> housemaid.getId().toString()).collect(Collectors.toList());
        List<List<String>> batches = ListHelper.divideList(includedHousemaidCCIds, chunkSize);
        int i = 0;
        for (List<String> batch: batches) {
            BackgroundTask task = new BackgroundTask
                    .builder("notifyIncludedHousemaidCCBT batch #" + i, Setup.getCurrentModule().getCode(), "messagingService", "notifyIncludedHousemaidCCBT")
                    .withQueue(BackgroundTaskQueues.HeavyOperationsQueue)
                    .withParameters(new Class<?>[]{List.class, String.class, Date.class}, batch, month, paymentDate)
                    .build();

            backgroundTaskService.create(task);
            i++;
        }


//        for (Housemaid housemaid : includedHousemaidCCList) {
//            paramValue.put("maid_name", housemaid.getName());
//            send("Payroll_Late_Salary_Because_Of_Holiday_Notification", "Payroll_Late_Salary_Because_Of_Holiday_Notification",
//                    housemaid.getYayaAppNotificationLang(), housemaid, paramValue, housemaid, null, housemaid);
//            sendNotification(housemaid, "Payroll_Late_Salary_Because_Of_Holiday_Notification", paramValue, null);
//        }

        List<Housemaid> eligibleListForMVMaids = Setup.getApplicationContext().getBean(HousemaidPayrollAuditService.class)
                .getEligibleListForMVMaids(rule);

        List<String> eligibleListForMVMaidsIds = eligibleListForMVMaids.stream().map(housemaid -> housemaid.getId().toString()).collect(Collectors.toList());

        batches = ListHelper.divideList(eligibleListForMVMaidsIds, chunkSize);
        i = 0;
        for (List<String> batch: batches) {
            BackgroundTask task = new BackgroundTask
                    .builder("notifyEligibleListForMVMaid batch #" + i, Setup.getCurrentModule().getCode(), "messagingService", "notifyEligibleListForMVMaid")
                    .withQueue(BackgroundTaskQueues.HeavyOperationsQueue)
                    .withParameters(new Class<?>[]{List.class, String.class, Date.class}, batch, month, paymentDate)
                    .build();

            backgroundTaskService.create(task);
            i++;
        }

//        paramValue.put("payment_date", DateUtil.formatDateDashed(DateUtil.addDays(paymentDate, 1)));
//        for (Housemaid housemaid : eligibleListForMVMaids) {
//
//            if (housemaid.getCurrentClient() != null) {
//                notifyClientMaidSalaryIsDaley(housemaid.getCurrentClient(), DateUtil.formatClientFullDate(paymentDate), housemaid.getFormattedFirstName());
//            }
//
//            List<String> languages = Setup.getApplicationContext().getBean(YayaAppContentHelper.class).getLanguageForMaid(housemaid);
//            paramValue.put("maid_name", housemaid.getName());
//            //Template template = TemplateUtil.getTemplate("Payroll_Late_Salary_Because_Of_Holiday_Notification_MV_Maids_SMS");
//            for (String lang : languages) {
//                //String text = TemplateUtil.compileTemplate(template, lang, null, paramValue);
////                smsService.send(
////                        "Payroll_Late_Salary_Because_Of_Holiday_Notification_MV_Maids_SMS",
////                        normalizePhoneNumber(housemaid.getPhoneNumber()),
////                        text,
////                        false,
////                        SmsReceiverType.Housemaid,
////                        housemaid.getId(),
////                        housemaid.getName()
////                );
//                send("Payroll_Late_Salary_Because_Of_Holiday_Notification_MV_Maids_SMS",
//                        lang, housemaid, paramValue, housemaid, null, housemaid);
//            }
//        }

        return true;
    }


    /**************************************/
    /***** Helper**********/
    public boolean isManager(OfficeStaff officeStaff) {
        if (officeStaff == null || officeStaff.getJobTitle() == null) return false;

        return officeStaff.getJobTitle().hasTag("manager")
                || officeStaff.getJobTitle().hasTag("JOB_TITLE_MANAGER_TAG");
    }

    public static boolean isSyrianNumber(String number) {
        return number != null && (
                number.startsWith("+963") || number.startsWith("00963") || number.startsWith("963")
        );
    }

    public static String generateText(String text, Map<String, String> paramValues) {
        for (Map.Entry<String, String> entry : paramValues.entrySet())
            text = text.replace("@" + entry.getKey() + "@", entry.getValue());

        return text;
    }

    public Boolean sendWithExpiryDate(String templateName, String type, String lang, BaseEntity recipient, Map<String, String> parameters, Object context, Map<String, ? extends AppAction> cta, BaseEntity owner, Date expirationDate) {

        if (context instanceof Client) {
            List<Contract> activeContracts = ((Client) context).getActiveContracts();
            if (activeContracts != null && activeContracts.size() == 1) {
                context = activeContracts.get(0);
            } else if (!(owner instanceof Client)) {
                context = owner;
            }
        }
        Template template = TemplateUtil.getTemplate(templateName);
        if (template == null)
            return false;
        SpecialBehaviorConfig config = new SpecialBehaviorConfig(template, lang, context, parameters, cta);

        //Prepare Target
        MessageTarget target = new MessageTarget(recipient, owner);
        config.addTarget(target);
        target.setEmailSubject(type);

        // pre send (execute all method in class PreSendAction)
        preSend(template, config);

        // send depend on result of preSend
        List<TemplateMessage> messages = null;
        if (expirationDate == null) {
            messages = templateUtil.send(template, config.getLang(), config.getChannelSpecificSettingType(), config.getTargetList(), config.getContext(), config.getCta(), config.getParameters());
        } else {
            Map<String, Date> notificationRelativeExpirationDateValues = new HashMap<>();
            notificationRelativeExpirationDateValues.put(
                    Setup.getOrCreateItem(Picklist.IMS_NOTIFICATION_RELATIVE_EXPIRATION, "@Next_Payroll_Date@")
                            .getCode(),
                    expirationDate);

            messages = templateUtil.send(new SendTemplateMessageRequest
                    .builder()
                    .template(template)
                    .channelType(config.getChannelSpecificSettingType())
                    .target(config.getTargetList())
                    .context(config.getContext())
                    .cta(config.getCta())
                    .parameters(config.getParameters())
                    .notificationRelativeExpirationDate(notificationRelativeExpirationDateValues)
                    .build());
        }

        // post send (execute all method in class PostSendAction)
        postSend(template, config, messages);

        return true;
    }

    public boolean notifyIncludedHousemaidCCBT(List<String> ids, String month, Date paymentDate){
        HousemaidRepository housemaidRepository = Setup.getRepository(HousemaidRepository.class);
        Map<String, String> paramValue = new HashMap<>();
        paramValue.put("month", month);
        paramValue.put("payment_date", DateUtil.formatDateDashed(paymentDate));
        for (String idString : ids) {
            try {
                Housemaid housemaid = housemaidRepository.findOne(Long.parseLong(idString));
                if (housemaid == null){
                    continue;
                }
                paramValue.put("maid_name", housemaid.getName());
                send("Payroll_Late_Salary_Because_Of_Holiday_Notification", "Payroll_Late_Salary_Because_Of_Holiday_Notification",
                        housemaid.getYayaAppNotificationLang(), housemaid, paramValue, housemaid, null, housemaid);
            } catch (Exception e) {
                logger.info(e.getMessage() + Arrays.toString(e.getStackTrace()));
            }

        }
        return true;
    }

    public boolean notifyEligibleListForMVMaid(List<String> ids, String month, Date paymentDate) {
        HousemaidRepository housemaidRepository = Setup.getRepository(HousemaidRepository.class);
        Map<String, String> paramValue = new HashMap<>();
        paramValue.put("month", month);
        paramValue.put("payment_date", DateUtil.formatDateDashed(DateUtil.addDays(paymentDate, 1)));
        for (String idString : ids) {
            try {
                Housemaid housemaid = housemaidRepository.findOne(Long.parseLong(idString));
                if (housemaid == null) {
                    continue;
                }
                Client client = housemaid.getCurrentClient();
                if (client != null) {
                    notifyClientMaidSalaryIsDaley(client, DateUtil.formatClientFullDate(paymentDate), housemaid.getFormattedFirstName());
                }

                List<String> languages = Setup.getApplicationContext().getBean(YayaAppContentHelper.class).getLanguageForMaid(housemaid);
                paramValue.put("maid_name", housemaid.getName());
                for (String lang : languages) {
                    send("Payroll_Late_Salary_Because_Of_Holiday_Notification_MV_Maids_SMS", "Payroll_Late_Salary_Because_Of_Holiday_Notification_MV_Maids_SMS",
                            lang, housemaid, paramValue, housemaid, null, housemaid);
                }
                paramValue.put("maid_name", housemaid.getName());

            } catch (Exception e) {
                logger.info(e.getMessage() + Arrays.toString(e.getStackTrace()));
            }

        }
        return true;

    }

    public boolean sendPaymentWhatsappMessageBGT(Long contractId, Boolean isPreCollected, Double totalAmount, Double monthlyFee, Double maidSalary){

        Contract contract = Setup.getRepository(ContractRepository.class).findOne(contractId);
        if (contract == null || contract.getHousemaid() == null) {
            return false;
        }

        Housemaid housemaid = contract.getHousemaid();
        Client client = contract.getClient();

        String maidFirstName = housemaid.getFirstName() != null ? housemaid.getFirstName() : "your domestic worker";

        String atmCardLocation = "";
        String documentLink = "";
        try{
            Map<String, Object> maidPayrollDetails = interModuleConnector.get(
                    PayrollManagementModule.URL_MODULE_STAFF_MGMT
                            + "/housemaid/maidPayrollDetails/"
                            + housemaid.getId(),
                    Map.class);

            if (maidPayrollDetails != null){

                if (maidPayrollDetails.containsKey("atmCardLocation")) {
                    atmCardLocation = maidPayrollDetails.get("atmCardLocation") != null ?
                            maidPayrollDetails.get("atmCardLocation").toString() : "";
                }

                if (maidPayrollDetails.containsKey("howToClaimSalaryDocument")) {
                    Object attachmentObj = maidPayrollDetails.get("howToClaimSalaryDocument");

                    if (attachmentObj instanceof Map) {
                        Map<String, Object> attachmentMap = (Map<String, Object>) attachmentObj;

                        Object uuidObj = attachmentMap.get("uuid");
                        if (uuidObj != null) {
                            String uuid = uuidObj.toString();
                            documentLink = shortener.shorten(getResourceLinkFromAttachment(uuid));
                        }
                    }
                }
            }
        } catch (Exception ex) {
            logger.log(SEVERE, "Error fetching maid payroll details from staff module", ex);
        }

        Map<String, String> templateFieldsParameters = buildNotificationParams(housemaid, totalAmount, monthlyFee, isPreCollected, maidSalary);
        Map<String, String> templateMessageTextParameters = housemaidService.determineTemplateParameters(housemaid, maidFirstName, documentLink, atmCardLocation);

        if (templateMessageTextParameters != null
                && templateMessageTextParameters.get("parameter1") != null && templateMessageTextParameters.get("parameter2") != null){

            templateFieldsParameters.put("parameter1", templateMessageTextParameters.getOrDefault("parameter1", ""));
            templateFieldsParameters.put("parameter2",templateMessageTextParameters.getOrDefault("parameter2", ""));

            String templateName = "MV_PAYMENT_RECEIVED_ON_X_MONTH_NOTIFICATION";
            Setup.getApplicationContext().getBean(MessagingService.class)
                    .send(templateName, null, null, client, templateFieldsParameters, contract, null, client);
            return true;

        }

        return false;
    }

    private Map<String, String> buildNotificationParams(Housemaid housemaid, Double totalAmount, Double monthlyFee, Boolean isPreCollected, Double maidSalary) {
        Map<String, String> params = new HashMap<>();
        params.put("total_amount", totalAmount != null ? totalAmount.toString() : "");
        params.put("maid_salary", maidSalary != null ? maidSalary.toString() : "");
        params.put("monthly_fee", monthlyFee != null ? monthlyFee.toString() : "");

        Date targetDate = isPreCollected ? new Date() : DateUtil.addMonths(new Date(), -1);
        Date fifthDayOfNextMonth = DateUtil.getDayOfMonthDate(DateUtil.addMonths(targetDate,1), 5);

        params.put("month", DateUtil.formatMonth(targetDate));
        params.put("5th_day_of_month", DateUtil.formatClientWithoutYearDate(fifthDayOfNextMonth));

        return params;
    }




}
