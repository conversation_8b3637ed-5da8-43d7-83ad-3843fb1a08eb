package com.magnamedia.service.message;


import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.module.type.CommunicationMethod;
import com.magnamedia.repository.OfficeStaffRepository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.magnamedia.service.message.MessagingService.isSyrianNumber;

// all method in this Class are execute before send any message
// all method in this Class must have same signature ()
public class PreSendAction {
    // template with isSecure
    public void action1(Template template,SpecialBehaviorConfig config){
        List<String> allTemplateWithIsSecureMethod = Arrays.asList(
                "Payroll_Sending_Transaction_Number",
                "Payroll_Vacation_Added_With_Airfare_Ticket",
                "Payroll_Vacation_Added_Off_Days",
                "Payroll_Vacation_Edit_Added_Off_Days",
                "Payroll_Vacation_Edit_And_Removed_Airfare_Ticket",
                "Payroll_Vacation_Deleted",
                "Payroll_Adding_Working_On_Weekly_Off_Day",
                "Payroll_Adding_Working_On_Public_Holiday",
                "Payroll_Adding_Paid_Working_On_Public_Holiday",
                "Payroll_OfficeStaffs_BirthDay_Reminder",
                "Payroll_Paid_Off_Days_For_Employee",
                "Payroll_Action_Taken_About_Compensation",
                "Payroll_Pending_Manager_Approval",
                "Payroll_Paid_Off_Days_Edited_For_Employee",
                "Payroll_Paid_Off_Days_Deleted_For_Employee",
                "PaydayCelebrationTemplate",
                "Payroll_Bank_Transfer_Report",
                "Payroll_Send_Change_Bank_Details_Email_To_Ansari",
                "Payroll_Final_Settlement_Of_OfficeStaff_To_CFO_Directly",
                "Payroll_Final_Settlement_Of_OfficeStaff",
                "Payroll_Authorize_Payroll_Transfer_Template",
                "Payroll_Request_Trustee_Approval_Notification",
                "Payroll_Request_Trustee_Rejection_Notification",
                "Payroll_Send_Payroll_Roster_File",
                "Payroll_Send_Payroll_Roster_File_Edited",
                "Payroll_Approve_Money_File_By_CFO",
                "Payroll_Send_New_Hire_Questionnaire",
                "Payroll_Final_Settlement_Of_OfficeStaff_To_CFO",
                "Payroll_Salary_Transfer_Notification",
                "Payroll_Requested_For_Termination_Employees_Email",
                "Payroll_Updated_Final_Settlement_Of_OfficeStaff",
                "User_Answer_About_Final_Settlement_Of_OfficeStaff",
                "CFO_Rejected_Final_Settlement_Of_OfficeStaff",
                "CFO_Ask_User_About_Final_Settlement_Of_OfficeStaff",
                "Payroll_Send_Final_Payroll_Files_To_Payroll_Auditors",
                "");
        if (allTemplateWithIsSecureMethod.contains(template.getName())){
            if (config.getTargetList() != null && !config.getTargetList().isEmpty()){
                config.getTargetList().forEach(messageTarget -> {
                    messageTarget.setSecure(true);
                });
            }
        }
    }

    // send message depending on preferredCommunicationMethod
    public void action2(Template template,SpecialBehaviorConfig config){
        List<String> allTemplateWithPreferredMethod = Arrays.asList(
                "Payroll_Vacation_Added_With_Airfare_Ticket",
                "Payroll_Vacation_Added_Off_Days",
                "Payroll_Vacation_Edit_Added_Off_Days",
                "Payroll_Vacation_Edit_And_Removed_Airfare_Ticket",
                "Payroll_Vacation_Deleted",
                "Payroll_Adding_Working_On_Weekly_Off_Day",
                "Payroll_Adding_Working_On_Public_Holiday",
                "Payroll_Adding_Paid_Working_On_Public_Holiday",
                "Payroll_New_Hire_Notification",
                "Payroll_OfficeStaff_Terminated",
                "Payroll_Accesses_Are_Revoked",
                "Payroll_Paid_Off_Days_For_Employee",
                "Payroll_Action_Taken_About_Compensation",
                "Payroll_Pending_Manager_Approval",
                "Payroll_Paid_Off_Days_Edited_For_Employee",
                "Payroll_Paid_Off_Days_Deleted_For_Employee",
                "Payroll_Wait_Accesses_To_Be_Revoked",
                "",
                "");
        if (allTemplateWithPreferredMethod.contains(template.getName())) {
            if (!config.getTargetList().isEmpty()){
                MessageTarget target = config.getTargetList().get(0);
                if (target.getEntityType() != null && target.getEntityType().equals("OfficeStaff")){
                    OfficeStaff staff = Setup.getRepository(OfficeStaffRepository.class)
                            .findOne(target.getId());
                    if (staff != null){
                        if ((staff.getPreferredCommunicationMethod() == null || CommunicationMethod.SMS.equals(staff.getPreferredCommunicationMethod())) && !isSyrianNumber(staff.getPhoneNumber())){
                            config.setChannelSpecificSettingType(ChannelSpecificSettingType.SMS);
                        }else if (target.getEmail() != null && !target.getEmail().isEmpty()){
                            config.setChannelSpecificSettingType(ChannelSpecificSettingType.Email);
                        }
                    }
                }
            }
        }
        return;
    }
    // template with senderName
    public void action3(Template template,SpecialBehaviorConfig config){
        List<String> allTemplateWithIsSecureMethod = Arrays.asList(
                "Payroll_Bank_Transfer_Report",
                "Payroll_Final_Settlement_Of_OfficeStaff_To_CFO_Directly",
                "Payroll_Final_Settlement_Of_OfficeStaff",
                "Payroll_Send_Change_Bank_Details_Email_To_Ansari",
                "Payroll_Approve_Money_File_By_CFO",
                "Payroll_Send_New_Hire_Questionnaire",
                "Payroll_Final_Settlement_Of_OfficeStaff_To_CFO",
                "PaydayCelebrationTemplate",
                "Payroll_Send_Payroll_Roster_File",
                "Payroll_Send_Payroll_Roster_File_Edited",
                "PayrollEmailTemplate",
                "Payroll_Updated_Final_Settlement_Of_OfficeStaff",
                "User_Answer_About_Final_Settlement_Of_OfficeStaff",
                "CFO_Rejected_Final_Settlement_Of_OfficeStaff",
                "CFO_Ask_User_About_Final_Settlement_Of_OfficeStaff",
                "Payroll_Send_Final_Payroll_Files_To_Payroll_Auditors");
        if (allTemplateWithIsSecureMethod.contains(template.getName())) {
            if (config.getTargetList() != null && !config.getTargetList().isEmpty()) {
                config.getTargetList().forEach(messageTarget -> {
                    messageTarget.setEmailSenderName("Maids.cc");
                });
            }
        }
    }

    // all method in this Class must have same signature ()
}
