package com.magnamedia.service.payroll.generation.newVersion2;

import com.magnamedia.controller.OfficestaffPayrollController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.entity.OfficeStaffPayrollBean;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.salarycalculation.OfficeStaffSalaryTransaction;
import com.magnamedia.salarycalculation.officestaff.*;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.newversion.LockDateService;
import org.joda.time.LocalDate;
import org.reflections.Reflections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class OfficeStaffPayrollPaymentServiceV2 {

    @Autowired
    private LockDateService lockDateService;

    @Autowired
    private PayrollAccountantTodoRepository payrollAccountantTodoRepository;

    @Autowired
    private OfficeStaffPayrollLogRepository officeStaffPayrollLogRepository;

    @Autowired
    private OfficeStaffPayrollBeanRepository officeStaffPayrollBeanRepository;

    @Autowired
    private MonthlyPaymentRuleRepository monthlyPaymentRuleRepository;

    @Autowired
    private OfficeStaffRepository officeStaffRepository;

    public SelectFilter getIncludedTargetListFilter(MonthlyPaymentRule monthlyPaymentRule, PayrollAccountantTodoType todoType) {

        SelectFilter typeFilter = null;

        Integer dayOfMonth = null;
        Date maxStartingDate;

        SelectFilter selfReceiver = new SelectFilter(new SelectFilter("selectedTransferDestination", "IS NULL", null)
                .or("selectedTransferDestination.selfReceiver", "=", true));

        SelectFilter nonSelfReceiver = new SelectFilter("selectedTransferDestination", "IS NOT NULL", null)
                .and("selectedTransferDestination.selfReceiver", "=", false);

        // Overseas type
        if (monthlyPaymentRule.isTargetingOverseas()) {
            SelectFilter overseasFilter = new SelectFilter("employeeType", "=", OfficeStaffType.OVERSEAS_STAFF);
            if (monthlyPaymentRule.getCountries().size() > 0) {

                overseasFilter.and(new SelectFilter(selfReceiver.and("country", "IN", monthlyPaymentRule.getCountries()))
                        .or(nonSelfReceiver.and("selectedTransferDestination.country", "IN", monthlyPaymentRule.getCountries())));
            }

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));

            typeFilter= new SelectFilter(overseasFilter);
        }
        // Expats type
        if (monthlyPaymentRule.isTargetingExpats()) {
            SelectFilter expatsFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EXPAT);
            if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
                expatsFilter.and("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
            }

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));


            typeFilter= typeFilter == null ? new SelectFilter(expatsFilter) : typeFilter.or(expatsFilter);
        }
        // Emiratis type
        if (monthlyPaymentRule.isTargetingEmiratis()) {
            SelectFilter emiratisFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EMARATI);

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EMIRATI));

            typeFilter= typeFilter == null ? new SelectFilter(emiratisFilter) : typeFilter.or(emiratisFilter);

        }

        maxStartingDate = DateUtil.getDayEnd(DateUtil.getDayOfMonthDate(new Date(monthlyPaymentRule.getPayrollMonth().getTime()), dayOfMonth));
        Set<Long> excludedStaff = officeStaffRepository.excludedStaff(OfficeStaffTodoType.CONFIRM_PAYROLL_QUESTIONNAIRE.toString(), DateUtil.getFirstOfMonthDate(monthlyPaymentRule.getPayrollMonth()), maxStartingDate);
        typeFilter.and("id", "not in", excludedStaff);

        // Not excluded from payroll
        SelectFilter notExcludedFromPayroll = new SelectFilter("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null);

        // Active and not excluded from payroll
        SelectFilter activeAndNotExcludedFromPayroll = new SelectFilter("status", "=", OfficeStaffStatus.ACTIVE)
                .and(notExcludedFromPayroll);

        // Terminated within payroll
        LocalDate payrollMonth = new LocalDate(monthlyPaymentRule.getPayrollMonth());
        SelectFilter terminatedWithinPayrollDate = new SelectFilter("terminationDate", "IS NOT NULL", null)
                .and("terminationDate", ">=", payrollMonth.withDayOfMonth(1).toDate())
                .and("terminationDate", "<=", payrollMonth.dayOfMonth().withMaximumValue().toDate());


        // Terminated within payroll date and not excluded form payroll (Old case)
        SelectFilter terminatedWithinPayrollNotExcluded = new SelectFilter("status", "=", OfficeStaffStatus.TERMINATED)
                .and("finalSettlement", "IS NULL", null)
                .and(terminatedWithinPayrollDate)
                .and(notExcludedFromPayroll);

        // Terminated within payroll date and Final settlement is approved before lock date (New case)
        SelectFilter terminatedWithinPayrollFinalSettlementApproved = new SelectFilter("finalSettlement", "IS NOT NULL", null)
                .and("finalSettlement.approvedByCFO", "=", true)
                .and("finalSettlement.includeInPayroll", "=", true)
                .and(terminatedWithinPayrollDate);

        SelectFilter baseFilter = new SelectFilter(typeFilter).and(new SelectFilter(activeAndNotExcludedFromPayroll).or(terminatedWithinPayrollNotExcluded)
                .or(terminatedWithinPayrollFinalSettlementApproved));


        PicklistItem philippines = Setup.getItem("countries", "philippines");

        // International transfer
        SelectFilter countryIsPhilippines = new SelectFilter("country", "IS NOT NULL", null)
                .and("country", "=", philippines);

        SelectFilter selectedCountryIsPhilippines = new SelectFilter("selectedTransferDestination.country", "IS NOT NULL", null)
                .and("selectedTransferDestination.country", "=", philippines);


        // Bank Transfer
        SelectFilter countryIsNotPhilippines = new SelectFilter("country", "IS NULL", null)
                .or("country", "<>", philippines);

        SelectFilter selectedCountryIsNotPhilippines = new SelectFilter("selectedTransferDestination.country", "IS NULL", null)
                .or("selectedTransferDestination.country", "<>", philippines);

        // Payment method
        if (monthlyPaymentRule.getPaymentMethod() == PaymentRulePaymentMethod.ACCORDING_TO_EMPLOYEE_PROFILE) {
            SelectFilter paymentMethodFilter = new SelectFilter("selectedTransferDestination", "IS NOT NULL", null);
            if(todoType == PayrollAccountantTodoType.BANK_TRANSFER) {
                paymentMethodFilter.and("selectedTransferDestination.receiveMoneyMethod", "=", ReceiveMoneyMethod.BANK_TRANSFER)
                        .and(

                                new SelectFilter(selfReceiver.and(countryIsNotPhilippines))
                                        .or(nonSelfReceiver.and(selectedCountryIsNotPhilippines))
                        );
            } else if(todoType == PayrollAccountantTodoType.INTERNATIONAL_TRANSFER) {
                paymentMethodFilter.and(new SelectFilter("selectedTransferDestination.receiveMoneyMethod", "=", ReceiveMoneyMethod.MONEY_TRANSFER_CENTER)
                        .or(new SelectFilter(selfReceiver.and(countryIsPhilippines)))
                        .or(new SelectFilter(nonSelfReceiver.and(selectedCountryIsPhilippines))));
            }
            baseFilter.and(paymentMethodFilter);
        }

        return baseFilter;
    }

    public List<OfficeStaff> getIncludedTargetList(MonthlyPaymentRule monthlyPaymentRule, PayrollAccountantTodoType todoType) {
        if (!monthlyPaymentRule.isTargetingOfficeStaff()) return new ArrayList<>();

        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.leftJoin("finalSettlement");
        query.leftJoin("selectedTransferDestination");

        query.filterBy(getIncludedTargetListFilter(monthlyPaymentRule, todoType));
        query.sortBy("name", true);
        return query.execute();
    }


    public SelectFilter getTargetListFilter(MonthlyPaymentRule monthlyPaymentRule, PayrollAccountantTodoType todoType) {

        SelectFilter typeFilter = null;

        Integer dayOfMonth = null;
        Date maxStartingDate;

        SelectFilter selfReceiver = new SelectFilter(new SelectFilter("selectedTransferDestination", "IS NULL", null)
                .or("selectedTransferDestination.selfReceiver", "=", true));

        SelectFilter nonSelfReceiver = new SelectFilter("selectedTransferDestination", "IS NOT NULL", null)
                .and("selectedTransferDestination.selfReceiver", "=", false);

        // Overseas type
        if (monthlyPaymentRule.isTargetingOverseas()) {
            SelectFilter overseasFilter = new SelectFilter("employeeType", "=", OfficeStaffType.OVERSEAS_STAFF);
            if (monthlyPaymentRule.getCountries().size() > 0) {

                overseasFilter.and(new SelectFilter(selfReceiver.and("country", "IN", monthlyPaymentRule.getCountries()))
                        .or(nonSelfReceiver.and("selectedTransferDestination.country", "IN", monthlyPaymentRule.getCountries())));
            }
            typeFilter= new SelectFilter(overseasFilter);

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));
        }
        // Expats type
        if (monthlyPaymentRule.isTargetingExpats()) {
            SelectFilter expatsFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EXPAT);
            if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
                expatsFilter.and("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
            }
            typeFilter= typeFilter == null ? new SelectFilter(expatsFilter) : typeFilter.or(expatsFilter);

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));
        }
        // Emiratis type
        if (monthlyPaymentRule.isTargetingEmiratis()) {
            SelectFilter emiratisFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EMARATI);

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EMIRATI));

            typeFilter= typeFilter == null ? new SelectFilter(emiratisFilter) : typeFilter.or(emiratisFilter);
        }

        maxStartingDate = DateUtil.getDayEnd(DateUtil.getDayOfMonthDate(new Date(monthlyPaymentRule.getPayrollMonth().getTime()), dayOfMonth));
        typeFilter.and("startingDate", "<=", maxStartingDate);

        // Active and not excluded from payroll
        SelectFilter active = new SelectFilter("status", "=", OfficeStaffStatus.ACTIVE);

        // Terminated within payroll
        LocalDate payrollMonth = new LocalDate(monthlyPaymentRule.getPayrollMonth());
        SelectFilter terminatedWithinPayrollDate = new SelectFilter("terminationDate", "IS NOT NULL", null)
                .and("terminationDate", ">=", payrollMonth.withDayOfMonth(1).toDate())
                .and("terminationDate", "<=", payrollMonth.dayOfMonth().withMaximumValue().toDate());



        // Terminated within payroll date and Final settlement is approved before lock date (New case)
        SelectFilter terminatedWithinPayrollFinalSettlementApproved = new SelectFilter("finalSettlement", "IS NOT NULL", null)
                .and(terminatedWithinPayrollDate);

        SelectFilter baseFilter = new SelectFilter(typeFilter).and(new SelectFilter(active).or(terminatedWithinPayrollFinalSettlementApproved));


        PicklistItem philippines = Setup.getItem("countries", "philippines");

        // International transfer
        SelectFilter countryIsPhilippines = new SelectFilter("country", "IS NOT NULL", null)
                .and("country", "=", philippines);

        SelectFilter selectedCountryIsPhilippines = new SelectFilter("selectedTransferDestination.country", "IS NOT NULL", null)
                .and("selectedTransferDestination.country", "=", philippines);


        // Bank Transfer
        SelectFilter countryIsNotPhilippines = new SelectFilter("country", "IS NULL", null)
                .or("country", "<>", philippines);

        SelectFilter selectedCountryIsNotPhilippines = new SelectFilter("selectedTransferDestination.country", "IS NULL", null)
                .or("selectedTransferDestination.country", "<>", philippines);

        // Payment method
        if (monthlyPaymentRule.getPaymentMethod() == PaymentRulePaymentMethod.ACCORDING_TO_EMPLOYEE_PROFILE) {
            SelectFilter paymentMethodFilter = new SelectFilter("selectedTransferDestination", "IS NOT NULL", null);
            if(todoType == PayrollAccountantTodoType.BANK_TRANSFER) {
                paymentMethodFilter.and("selectedTransferDestination.receiveMoneyMethod", "=", ReceiveMoneyMethod.BANK_TRANSFER)
                        .and(
                                new SelectFilter(selfReceiver.and(countryIsNotPhilippines))
                                .or(nonSelfReceiver.and(selectedCountryIsNotPhilippines))
                        );
            } else if(todoType == PayrollAccountantTodoType.INTERNATIONAL_TRANSFER) {
                paymentMethodFilter.and(new SelectFilter("selectedTransferDestination.receiveMoneyMethod", "=", ReceiveMoneyMethod.MONEY_TRANSFER_CENTER)
                        .or(new SelectFilter(selfReceiver.and(countryIsPhilippines)))
                        .or(new SelectFilter(nonSelfReceiver.and(selectedCountryIsPhilippines))));
            }
            baseFilter.and(paymentMethodFilter);
        }

        return baseFilter;
    }

    public SelectFilter geTerminatedListFilterForDetailedFile(MonthlyPaymentRule monthlyPaymentRule, PayrollAccountantTodoType todoType) {

        SelectFilter typeFilter = null;

        Integer dayOfMonth = null;
        Date maxStartingDate;

        SelectFilter selfReceiver = new SelectFilter(new SelectFilter("selectedTransferDestination", "IS NULL", null)
                .or("selectedTransferDestination.selfReceiver", "=", true));

        SelectFilter nonSelfReceiver = new SelectFilter("selectedTransferDestination", "IS NOT NULL", null)
                .and("selectedTransferDestination.selfReceiver", "=", false);

        // Overseas type
        if (monthlyPaymentRule.isTargetingOverseas()) {
            SelectFilter overseasFilter = new SelectFilter("employeeType", "=", OfficeStaffType.OVERSEAS_STAFF);
            if (monthlyPaymentRule.getCountries().size() > 0) {

                overseasFilter.and(new SelectFilter(selfReceiver.and("country", "IN", monthlyPaymentRule.getCountries()))
                        .or(nonSelfReceiver.and("selectedTransferDestination.country", "IN", monthlyPaymentRule.getCountries())));
            }

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));

            typeFilter= new SelectFilter(overseasFilter);
        }
        // Expats type
        if (monthlyPaymentRule.isTargetingExpats()) {
            SelectFilter expatsFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EXPAT);
            if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
                expatsFilter.and("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
            }

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));

            typeFilter= typeFilter == null ? new SelectFilter(expatsFilter) : typeFilter.or(expatsFilter);
        }
        // Emiratis type
        if (monthlyPaymentRule.isTargetingEmiratis()) {
            SelectFilter emiratisFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EMARATI);

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EMIRATI));

            typeFilter= typeFilter == null ? new SelectFilter(emiratisFilter) : typeFilter.or(emiratisFilter);
        }

        maxStartingDate = DateUtil.getDayEnd(DateUtil.getDayOfMonthDate(new Date(monthlyPaymentRule.getPayrollMonth().getTime()), dayOfMonth));
        typeFilter.and("startingDate", "<=", maxStartingDate);

        // Terminated within payroll
        LocalDate payrollMonth = new LocalDate(monthlyPaymentRule.getPayrollMonth());
        SelectFilter terminatedWithinPayrollDate = new SelectFilter("terminationDate", "IS NOT NULL", null)
                .and("terminationDate", ">=", payrollMonth.withDayOfMonth(1).toDate())
                .and("terminationDate", "<=", payrollMonth.dayOfMonth().withMaximumValue().toDate());

        SelectFilter baseFilter = new SelectFilter(typeFilter).and(new SelectFilter(terminatedWithinPayrollDate));

        PicklistItem philippines = Setup.getItem("countries", "philippines");

        // International transfer
        SelectFilter countryIsPhilippines = new SelectFilter("country", "IS NOT NULL", null)
                .and("country", "=", philippines);

        SelectFilter selectedCountryIsPhilippines = new SelectFilter("selectedTransferDestination.country", "IS NOT NULL", null)
                .and("selectedTransferDestination.country", "=", philippines);


        // Bank Transfer
        SelectFilter countryIsNotPhilippines = new SelectFilter("country", "IS NULL", null)
                .or("country", "<>", philippines);

        SelectFilter selectedCountryIsNotPhilippines = new SelectFilter("selectedTransferDestination.country", "IS NULL", null)
                .or("selectedTransferDestination.country", "<>", philippines);

        // Payment method
        if (monthlyPaymentRule.getPaymentMethod() == PaymentRulePaymentMethod.ACCORDING_TO_EMPLOYEE_PROFILE) {
            SelectFilter paymentMethodFilter = new SelectFilter("selectedTransferDestination", "IS NOT NULL", null);
            if(todoType == PayrollAccountantTodoType.BANK_TRANSFER) {
                paymentMethodFilter.and("selectedTransferDestination.receiveMoneyMethod", "=", ReceiveMoneyMethod.BANK_TRANSFER)
                        .and(
                                new SelectFilter(selfReceiver.and(countryIsNotPhilippines))
                                        .or(nonSelfReceiver.and(selectedCountryIsNotPhilippines))
                        );
            } else if(todoType == PayrollAccountantTodoType.INTERNATIONAL_TRANSFER) {
                paymentMethodFilter.and(new SelectFilter("selectedTransferDestination.receiveMoneyMethod", "=", ReceiveMoneyMethod.MONEY_TRANSFER_CENTER)
                        .or(new SelectFilter(selfReceiver.and(countryIsPhilippines)))
                        .or(new SelectFilter(nonSelfReceiver.and(selectedCountryIsPhilippines))));
            }
            baseFilter.and(paymentMethodFilter);
        }

        return baseFilter;
    }

    public SelectFilter geExcludedListFilterForDetailedFile(MonthlyPaymentRule monthlyPaymentRule, PayrollAccountantTodoType todoType) {

        SelectFilter typeFilter = null;

        Integer dayOfMonth = null;
        Set<Long> excludedStaffDueToStartDate;
        OfficeStaffType officeStaffType = null;

        SelectFilter officeStaffExcludedFilter;

        SelectFilter selfReceiver = new SelectFilter(new SelectFilter("selectedTransferDestination", "IS NULL", null)
                .or("selectedTransferDestination.selfReceiver", "=", true));

        SelectFilter nonSelfReceiver = new SelectFilter("selectedTransferDestination", "IS NOT NULL", null)
                .and("selectedTransferDestination.selfReceiver", "=", false);

        // Overseas type
        if (monthlyPaymentRule.isTargetingOverseas()) {
            SelectFilter overseasFilter = new SelectFilter("employeeType", "=", OfficeStaffType.OVERSEAS_STAFF);
            if (monthlyPaymentRule.getCountries().size() > 0) {

                overseasFilter.and(new SelectFilter(selfReceiver.and("country", "IN", monthlyPaymentRule.getCountries()))
                        .or(nonSelfReceiver.and("selectedTransferDestination.country", "IN", monthlyPaymentRule.getCountries())));
            }
            typeFilter= new SelectFilter(overseasFilter);

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));
            officeStaffType = OfficeStaffType.OVERSEAS_STAFF;
        }
        // Expats type
        if (monthlyPaymentRule.isTargetingExpats()) {
            SelectFilter expatsFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EXPAT);
            if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
                expatsFilter.and("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
            }
            typeFilter= typeFilter == null ? new SelectFilter(expatsFilter) : typeFilter.or(expatsFilter);

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));
            officeStaffType = OfficeStaffType.DUBAI_STAFF_EXPAT;
        }
        // Emiratis type
        if (monthlyPaymentRule.isTargetingEmiratis()) {
            SelectFilter emiratisFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EMARATI);
            typeFilter= typeFilter == null ? new SelectFilter(emiratisFilter) : typeFilter.or(emiratisFilter);

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EMIRATI));
            officeStaffType = OfficeStaffType.DUBAI_STAFF_EMARATI;
        }

        Date maxStartingDate = DateUtil.getDayEnd(DateUtil.getDayOfMonthDate(new Date(monthlyPaymentRule.getPayrollMonth().getTime()), dayOfMonth));
        excludedStaffDueToStartDate = officeStaffRepository.excludedStaffButNeedToGenerate(OfficeStaffTodoType.CONFIRM_PAYROLL_QUESTIONNAIRE.toString(), DateUtil.getFirstOfMonthDate(monthlyPaymentRule.getPayrollMonth()), maxStartingDate);
        officeStaffExcludedFilter = new SelectFilter("id", "in", excludedStaffDueToStartDate).and("employeeType", "=", officeStaffType);

        // excluded from payroll
        SelectFilter excludedFromPayroll = new SelectFilter("excludedFromPayroll", "=", true);

        // Active and not excluded from payroll
        SelectFilter activeAndExcludedFromPayroll = new SelectFilter("status", "=", OfficeStaffStatus.ACTIVE)
                .and(new SelectFilter(excludedFromPayroll).or(officeStaffExcludedFilter));

        SelectFilter baseFilter = new SelectFilter(typeFilter).and(new SelectFilter(activeAndExcludedFromPayroll));

        PicklistItem philippines = Setup.getItem("countries", "philippines");

        // International transfer
        SelectFilter countryIsPhilippines = new SelectFilter("country", "IS NOT NULL", null)
                .and("country", "=", philippines);

        SelectFilter selectedCountryIsPhilippines = new SelectFilter("selectedTransferDestination.country", "IS NOT NULL", null)
                .and("selectedTransferDestination.country", "=", philippines);


        // Bank Transfer
        SelectFilter countryIsNotPhilippines = new SelectFilter("country", "IS NULL", null)
                .or("country", "<>", philippines);

        SelectFilter selectedCountryIsNotPhilippines = new SelectFilter("selectedTransferDestination.country", "IS NULL", null)
                .or("selectedTransferDestination.country", "<>", philippines);

        // Payment method
        if (monthlyPaymentRule.getPaymentMethod() == PaymentRulePaymentMethod.ACCORDING_TO_EMPLOYEE_PROFILE) {
            SelectFilter paymentMethodFilter = new SelectFilter("selectedTransferDestination", "IS NOT NULL", null);
            if(todoType == PayrollAccountantTodoType.BANK_TRANSFER) {
                paymentMethodFilter.and("selectedTransferDestination.receiveMoneyMethod", "=", ReceiveMoneyMethod.BANK_TRANSFER)
                        .and(
                                new SelectFilter(selfReceiver.and(countryIsNotPhilippines))
                                        .or(nonSelfReceiver.and(selectedCountryIsNotPhilippines))
                        );
            } else if(todoType == PayrollAccountantTodoType.INTERNATIONAL_TRANSFER) {
                paymentMethodFilter.and(new SelectFilter("selectedTransferDestination.receiveMoneyMethod", "=", ReceiveMoneyMethod.MONEY_TRANSFER_CENTER)
                        .or(new SelectFilter(selfReceiver.and(countryIsPhilippines)))
                        .or(new SelectFilter(nonSelfReceiver.and(selectedCountryIsPhilippines))));
            }
            baseFilter.and(paymentMethodFilter);
        }

        return baseFilter;
    }

    public List<OfficeStaff> getTargetList(MonthlyPaymentRule monthlyPaymentRule, PayrollAccountantTodoType todoType) {
        if (!monthlyPaymentRule.isTargetingOfficeStaff()) return new ArrayList<>();
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.leftJoin("finalSettlement");
        query.leftJoin("selectedTransferDestination");
        SelectFilter selectFilter =  getTargetListFilter(monthlyPaymentRule, todoType);
        query.filterBy(selectFilter);
        query.sortBy("name", true);
        return query.execute();
    }

    public List<OfficeStaff> getTerminatedListForDetailedFile(MonthlyPaymentRule monthlyPaymentRule, PayrollAccountantTodoType todoType) {
        if (!monthlyPaymentRule.isTargetingOfficeStaff()) return new ArrayList<>();
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.leftJoin("selectedTransferDestination");
        query.filterBy(geTerminatedListFilterForDetailedFile(monthlyPaymentRule, todoType));
        query.sortBy("name", true);
        return query.execute();
    }

    public List<OfficeStaff> getExcludedListForDetailedFile(MonthlyPaymentRule monthlyPaymentRule, PayrollAccountantTodoType todoType) {
        if (!monthlyPaymentRule.isTargetingOfficeStaff()) return new ArrayList<>();
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.leftJoin("selectedTransferDestination");
        query.filterBy(geExcludedListFilterForDetailedFile(monthlyPaymentRule, todoType));
        query.sortBy("name", true);
        return query.execute();
    }

    public int countTargetList(MonthlyPaymentRule monthlyPaymentRule, PayrollAccountantTodoType todoType) {
        if (!monthlyPaymentRule.isTargetingOfficeStaff()) return 0;
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.leftJoin("finalSettlement");
        query.leftJoin("selectedTransferDestination");
        SelectFilter selectFilter = getTargetListFilter(monthlyPaymentRule, todoType);

        query.filterBy(selectFilter);

        AggregateQuery aggregateQuery = new AggregateQuery(query, Aggregate.Count, "id");
        return aggregateQuery.execute().intValue();
    }

    //for Pension Authority only
    public List<OfficeStaffPayrollLog> generatePayrollLogs(List<OfficeStaff> officeStaffs, PayrollAccountantTodo todo) {
        List<OfficeStaffPayrollLog> logs = new ArrayList<>();
        try {
            if (officeStaffs.isEmpty()) return new ArrayList<>();
            int index = 1;
            int excludedIndex = 1;
            for (OfficeStaff officeStaff : officeStaffs) {
                try {
                    // Only we need the original salary for the pension authority
                    OfficeStaffPayrollLog log = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).generatePayrollLog(officeStaff, todo);
                    log.setOfficeStaffPayrollBean(null);
                    log.setSn("OS-" + index);
                    if (todo.getIncludedofficeStaffs().contains(officeStaff)) {
                        if (log.getTotalSalary() > 1) {
                            log.setWillBeIncluded(true);
                            logs.add(log);
                            index++;
                        }
                    } else {
                        if (log.getTotalSalary() > 1) {
                            logs.add(log);
                            log.setSn("OS-" + excludedIndex);
                            excludedIndex++;
                        }
                    }
                } catch (Exception e) {
                    Logger.getLogger(OfficeStaffPayrollBean.class.getName()).log(Level.SEVERE, String.format("Error while generating payroll log for %s", officeStaff.getName()), e);
                    DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception while generating Office staff payroll log for " + officeStaff.getName(), false);
                }
            }
        }catch (Exception ex){
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Exception while generating Office staff payroll log", false);
        }
        return logs;
    }

    public List<OfficeStaffPayrollLog> getOrCreatePayrollLogs(List<OfficeStaff> officeStaffs, MonthlyPaymentRule monthlyPaymentRule, PayrollAccountantTodo todo, Boolean finalFile) {
        List<OfficeStaffPayrollLog> logs = new ArrayList<>();
        try {
            if (officeStaffs.isEmpty()) return new ArrayList<>();
//            officeStaffs.sort(Comparator.comparing(OfficeStaff::getName, String.CASE_INSENSITIVE_ORDER));
            officeStaffs.sort(Comparator.comparing(
                    // Use a null-safe comparator to handle null values
                    officeStaff -> officeStaff.getName() == null ? "" : officeStaff.getName().toLowerCase(),
                    Comparator.naturalOrder()
            ));

            LocalDate payrollStart = new LocalDate(getPayrollStartLockDate(monthlyPaymentRule));
            LocalDate payrollEnd = new LocalDate(getPayrollEndLockDate(monthlyPaymentRule));
            LocalDate payrollMonth = new LocalDate(monthlyPaymentRule.getPayrollMonth());

            Date payrollStartForManagerNotes = getPayrollStartLockDateForManagerNotes(monthlyPaymentRule);
            Date payrollEndForManagerNotes = getPayrollEndLockDateForManagerNotes(monthlyPaymentRule);


//            DebugHelper.sendMail("<EMAIL>", String.format("Before payroll Initialize for %d office staff - Payroll Start: %s - Payroll End: %s" +
//                            " - Payroll Start For Manager Notes: %s - Payroll End For Manager Notes: %s",
//                    officeStaffs.size(),
//                    DateUtil.formatDateDashed(payrollStart.toDate()),
//                    DateUtil.formatDateDashed(payrollEnd.toDate()),
//                    DateUtil.formatDateDashedWithTime(payrollStartForManagerNotes),
//                    DateUtil.formatDateDashedWithTime(payrollEndForManagerNotes)
//            ));

            Integer index = 1;
            Integer excludedIndex = 1;

            for (OfficeStaff officeStaff : officeStaffs) {
                OfficeStaffPayrollLog log = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).generateOneOfficeStaffLogBasedOnAll(officeStaff, monthlyPaymentRule, null, todo, payrollStart, payrollEnd, payrollStartForManagerNotes, payrollEndForManagerNotes, payrollMonth, finalFile, false);
                if(log!= null) {
                    log.setLogStatus(OfficeStaffPayrollLog.OfficeStaffPayrollLogStatus.FINAL);
                    OfficeStaffPayrollBean bean = log.getOfficeStaffPayrollBean();
                    bean.setNotFinal(!finalFile);
                    bean = officeStaffPayrollBeanRepository.save(bean);
                    log.setOfficeStaffPayrollBean(bean);
                    if (log.getTotalSalary() > 1) {
                        if (todo.getIncludedofficeStaffs().contains(officeStaff)) {
                            log.setSn("OS-" + index++);
                            log.setWillBeIncluded(true);
                            log = officeStaffPayrollLogRepository.save(log);
                        } else {
                            log.setSn("OS-" + excludedIndex++);
                            log = officeStaffPayrollLogRepository.save(log);
                        }
                    }
                    logs.add(log);
                }
            }
        }catch (Exception ex){
            Logger.getLogger(OfficeStaffPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, "Error while getOrCreatePayrollLogs: " + ex);
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Exception while getOrCreatePayrollLogs: ", false);
            throw ex;
        }
        return logs;
    }

    public void generatePayrollLogsBasedOnAll(List<OfficeStaff> officeStaffs, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, Boolean finalFile) {
        List<OfficeStaffPayrollLog> logs = new ArrayList<>();
        try {
            if (officeStaffs.isEmpty()) return;

            LocalDate payrollStart = new LocalDate(getPayrollStartLockDate(monthlyPaymentRule));
            LocalDate payrollEnd = new LocalDate(getPayrollEndLockDate(monthlyPaymentRule));
            LocalDate payrollMonth = new LocalDate(monthlyPaymentRule.getPayrollMonth());

            Date payrollStartForManagerNotes = getPayrollStartLockDateForManagerNotes(monthlyPaymentRule);
            Date payrollEndForManagerNotes = getPayrollEndLockDateForManagerNotes(monthlyPaymentRule);

//            DebugHelper.sendMail("<EMAIL>", String.format("Start generatePayrollLogsBasedOnAll for %d office staff - Payroll Start: %s - Payroll End: %s",officeStaffs.size(),DateUtil.formatDateDashed(payrollStart.toDate()),DateUtil.formatDateDashed(payrollEnd.toDate())));

            //generate Logs individually
            for (OfficeStaff officeStaff : officeStaffs) {
                OfficeStaffPayrollLog log = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).generateOneOfficeStaffLogBasedOnAll(officeStaff, monthlyPaymentRule, auditTodo, todo, payrollStart, payrollEnd, payrollStartForManagerNotes, payrollEndForManagerNotes, payrollMonth, finalFile, true);
                if(log!= null)
                    logs.add(log);
            }

//            if(auditTodo != null){
//                auditTodo = Setup.getRepository(PayrollAuditTodoRepository.class).getOne(auditTodo.getId());
//                auditTodo.setFinishedGeneratingLogs(true);
//                auditTodo = Setup.getRepository(PayrollAuditTodoRepository.class).save(auditTodo);
//            }
//            DebugHelper.sendMail("<EMAIL>", String.format("Finish generatePayrollLogsBasedOnAll for %d office staff - Payroll Start: %s - Payroll End: %s",officeStaffs.size(),DateUtil.formatDateDashed(payrollStart.toDate()),DateUtil.formatDateDashed(payrollEnd.toDate())));
        }catch (Exception ex){
            Logger.getLogger(OfficeStaffPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, "Error while generatePayrollLogsBasedOnAll: " + ex);
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Exception while generatePayrollLogsBasedOnAll: ", false);
            throw ex;
        }
        return;
    }

    @Async
    public void generatePayrollLogsBasedOnAllAsync(List<OfficeStaff> officeStaffs, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, Boolean finalFile) {
        generatePayrollLogsBasedOnAll(officeStaffs, monthlyPaymentRule, auditTodo, todo, finalFile);
        if(!monthlyPaymentRule.isTargetingHousemaid())
            // insert a new background task to send Initial Payroll Files
            Setup.getApplicationContext()
                    .getBean(BackgroundTaskService.class)
                    .addDirectCallBackgroundTaskForEntity(
                            "sendInitialPayrollFilesRevamp", "payrollAuditTodoService", "payroll",
                            "sendInitialPayrollFilesRevamp",
                            "MonthlyPaymentRule", monthlyPaymentRule.getId(), false,
                            false, new Class[]{Long.class}, new Object[]{monthlyPaymentRule.getId()});
    }

    @Transactional
    public OfficeStaffPayrollLog generateOneOfficeStaffLogBasedOnAll(OfficeStaff officeStaff, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, LocalDate payrollStart, LocalDate payrollEnd, Date payrollStartForManagerNotes, Date payrollEndForManagerNotes, LocalDate payrollMonth, Boolean finalFile, Boolean toSave){
        OfficeStaffPayrollLog log = null;
        List<MonthlyPaymentRule> listOfRelatedRules;
        if(!monthlyPaymentRule.isTargetingExpats())
            listOfRelatedRules = Arrays.asList(monthlyPaymentRule);
        else
            listOfRelatedRules = monthlyPaymentRuleRepository.findByPayrollMonthAndEmployeeTypeAndPayrollTypeAndLockDate(monthlyPaymentRule.getPayrollMonth(), PaymentRuleEmployeeType.EXPATS, monthlyPaymentRule.getPayrollType(), monthlyPaymentRule.getLockDate());
        try {

            SelectQuery<OfficeStaffPayrollLog> selectQuery = new SelectQuery<>(OfficeStaffPayrollLog.class);
            selectQuery.filterBy("officeStaff.id", "=", officeStaff.getId());
            selectQuery.filterBy("monthlyPaymentRule", "IN", listOfRelatedRules);
            if(auditTodo != null && auditTodo.getId()!= null)
                selectQuery.filterBy("payrollAuditTodo", "=", auditTodo);
//            if(todo != null && todo.getId()!= null)
//                selectQuery.filterBy("payrollAccountantTodo", "=", todo);
            selectQuery.filterBy("payrollMonth", "=", monthlyPaymentRule.getPayrollMonth());
            selectQuery.sortBy("id", false);
            List<OfficeStaffPayrollLog> logs =  selectQuery.execute();

            log = logs != null && logs.size() > 0 ? logs.get(0) : null;
            if(log == null) {
                OfficeStaffPayrollBean bean = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).runTransactions(officeStaff, payrollStart, payrollEnd, payrollStartForManagerNotes, payrollEndForManagerNotes, payrollMonth, finalFile);
                if(bean != null)
                    bean = officeStaffPayrollBeanRepository.save(bean);
                log = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).createPayrollLogBasedOnAll(officeStaff, bean, monthlyPaymentRule, auditTodo, todo);
                log.setOfficeStaffPayrollBean(bean);
                log.setLogStatus(OfficeStaffPayrollLog.OfficeStaffPayrollLogStatus.PENDING);
                if (toSave && log.getTotalSalary() > 1) {
                    log =officeStaffPayrollLogRepository.save(log);
                }
            }else {
                if(monthlyPaymentRule != null)
                    log.setMonthlyPaymentRule(monthlyPaymentRule);
                if(auditTodo != null)
                    log.setPayrollAuditTodo(auditTodo);
                if(todo != null)
                    log.setPayrollAccountantTodo(todo);
                if(toSave)
                    log = officeStaffPayrollLogRepository.save(log);
            }

        } catch (Exception e) {
            Logger.getLogger(OfficeStaffPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, String.format("Error while generating payroll log for %s", officeStaff.getName()), e);
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception while generateOneOfficeStaffLogBasedOnAll for " + officeStaff.getName(), false);
            throw e;
        }

        return log;
    }


    public List<OfficeStaffPayrollLog> getOfficeStaffPayrollLogsBasedOnAll(List<Long> officeStaffsIds, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo){
        if(officeStaffsIds.size() == 0)
            return new ArrayList<>();
        List<MonthlyPaymentRule> listOfRelatedRules;
        if(!monthlyPaymentRule.isTargetingExpats())
            listOfRelatedRules = Arrays.asList(monthlyPaymentRule);
        else
            listOfRelatedRules = monthlyPaymentRuleRepository.findByPayrollMonthAndEmployeeTypeAndPayrollTypeAndLockDate(monthlyPaymentRule.getPayrollMonth(), PaymentRuleEmployeeType.EXPATS, monthlyPaymentRule.getPayrollType(), monthlyPaymentRule.getLockDate());

        SelectQuery<OfficeStaffPayrollLog> selectQuery = new SelectQuery<>(OfficeStaffPayrollLog.class);
        selectQuery.filterBy("officeStaff.id", "IN", officeStaffsIds);
        selectQuery.filterBy("monthlyPaymentRule", "IN", listOfRelatedRules);
        if(auditTodo != null && auditTodo.getId()!= null)
            selectQuery.filterBy("payrollAuditTodo", "=", auditTodo);
//        if(todo != null && todo.getId()!= null)
//            selectQuery.filterBy("payrollAccountantTodo", "=", todo);
        selectQuery.filterBy("payrollMonth", "=", monthlyPaymentRule.getPayrollMonth());
        selectQuery.sortBy("officeStaff.name", true);
        return selectQuery.execute();
    }

    public List<OfficeStaff> getChangedOfficeStaffsBasedOnAll(List<OfficeStaff> officeStaffs, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo){
        List<MonthlyPaymentRule> listOfRelatedRules;
        if(!monthlyPaymentRule.isTargetingExpats())
            listOfRelatedRules = Arrays.asList(monthlyPaymentRule);
        else {
            listOfRelatedRules = monthlyPaymentRuleRepository.findByPayrollMonthAndEmployeeTypeAndPayrollTypeAndLockDate(monthlyPaymentRule.getPayrollMonth(), PaymentRuleEmployeeType.EXPATS, monthlyPaymentRule.getPayrollType(), monthlyPaymentRule.getLockDate());
        }

        OfficeStaffRepository officeStaffRepository = Setup.getRepository(OfficeStaffRepository.class);
        Set<OfficeStaff> changedStaffs = new HashSet<>(officeStaffRepository.getChangedOfficeStaffs(officeStaffs, listOfRelatedRules, auditTodo, todo, monthlyPaymentRule.getPayrollMonth()));
        changedStaffs.addAll(officeStaffRepository.getChangedOfficeStaffsByOldLogs(officeStaffs, listOfRelatedRules, auditTodo, todo, monthlyPaymentRule.getPayrollMonth()));
        return new ArrayList<>(changedStaffs);
    }

    public void clearDataForStaffs(List<OfficeStaff> changedOfficeStaffs, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo){
//        DebugHelper.sendMail("<EMAIL>", "clearDataForStaffs started" );
        for(OfficeStaff officeStaff : changedOfficeStaffs){
            Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).recoverSimulationDataBasedOnAll(officeStaff, monthlyPaymentRule, auditTodo, todo, monthlyPaymentRule.getPayrollMonth());
        }
//        DebugHelper.sendMail("<EMAIL>", "clearDataForStaffs finished" );
    }

    @Transactional
    public void recoverSimulationDataBasedOnAll(OfficeStaff officeStaff, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, java.sql.Date payrollMonDate){
        PayrollManagerNoteRepository payrollManagerNoteRepository = Setup.getRepository(PayrollManagerNoteRepository.class);
        payrollManagerNoteRepository.deleteByNotFinalAndOfficeStaffId(true, officeStaff.getId());

        List<MonthlyPaymentRule> listOfRelatedRules;
        if(!monthlyPaymentRule.isTargetingExpats())
            listOfRelatedRules = Arrays.asList(monthlyPaymentRule);
        else
            listOfRelatedRules = monthlyPaymentRuleRepository.findByPayrollMonthAndEmployeeTypeAndPayrollTypeAndLockDate(monthlyPaymentRule.getPayrollMonth(), PaymentRuleEmployeeType.EXPATS, monthlyPaymentRule.getPayrollType(), monthlyPaymentRule.getLockDate());

        SelectQuery<OfficeStaffPayrollLog> selectQuery = new SelectQuery<>(OfficeStaffPayrollLog.class);
        selectQuery.filterBy("officeStaff.id", "=", officeStaff.getId());
        selectQuery.filterBy("monthlyPaymentRule", "IN", listOfRelatedRules);
        if(auditTodo != null && auditTodo.getId()!= null)
            selectQuery.filterBy("payrollAuditTodo", "=", auditTodo);
//        if(todo != null && todo.getId()!= null)
//            selectQuery.filterBy("payrollAccountantTodo", "=", todo);
        selectQuery.filterBy("payrollMonth", "=", monthlyPaymentRule.getPayrollMonth());
        selectQuery.sortBy("id", false);
        List<OfficeStaffPayrollLog> logs =  selectQuery.execute();

        for(OfficeStaffPayrollLog log : logs){//should be one record only
            OfficeStaffPayrollBean bean = log.getOfficeStaffPayrollBean();
            officeStaffPayrollLogRepository.delete(log);
            officeStaffPayrollBeanRepository.delete(bean);
        }
    }

    public void clearOfficeStaffLogsForChangedAndRecreate(List<OfficeStaff> staffs, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, Boolean finalFile){
        try {
            List<OfficeStaff> changedOfficeStaffs = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).getChangedOfficeStaffsBasedOnAll(staffs, monthlyPaymentRule, auditTodo, todo);
//            DebugHelper.sendMail("<EMAIL>", "clearOfficeStaffLogsForChangedAndRecreate --> changedStaffs : " + changedOfficeStaffs.stream().map(BaseEntity::getId).collect(Collectors.toList()));
            if(changedOfficeStaffs.size() > 0) {
                Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).clearDataForStaffs(changedOfficeStaffs, monthlyPaymentRule, auditTodo, todo);
                Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).generatePayrollLogsBasedOnAll(changedOfficeStaffs, monthlyPaymentRule, auditTodo, todo, finalFile);
            }
        }catch (Exception e){
            Logger.getLogger(OfficeStaffPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, "Error while clearOfficeStaffLogsForChangedAndRecreate ", e);
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception while clearOfficeStaffLogsForChangedAndRecreate ", false);
            throw e;
        }
    }


    // this function only used for pension authority
    @Transactional
    public OfficeStaffPayrollLog generatePayrollLog(OfficeStaff officeStaff, PayrollAccountantTodo todo) {
        if (PayrollAccountantTodoType.valueOf(todo.getTaskName()) == PayrollAccountantTodoType.PENSION_AUTHORITY) {
            double employeePercentageToDeduct;
            double totalPercentageToDeduct;
            double totalContributionAmount = 0.0;

            if (officeStaff.getOnOrBefore31thOct2023()) {
                try {
                    employeePercentageToDeduct = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_PENSION_DEDUCTION_PERCENTAGE_ON_OR_BEFORE_31_10_2023));
                    totalPercentageToDeduct = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_PENSION_AUTHORITY_PERCENTAGE_ON_OR_BEFORE_31_10_2023));
                } catch (Exception e) {
                    employeePercentageToDeduct = 5.0;
                    totalPercentageToDeduct = 17.5;
                }
                if(officeStaff.getFixedPensionAmount() != null)
                    totalContributionAmount = officeStaff.getFixedPensionAmount();
                else
                    totalContributionAmount = officeStaff.getSalary() * (totalPercentageToDeduct / 100.0);
            } else {
                try {
                    employeePercentageToDeduct = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_PENSION_DEDUCTION_PERCENTAGE_AFTER_31_10_2023));
                    totalPercentageToDeduct = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_PENSION_AUTHORITY_PERCENTAGE_AFTER_31_10_2023));
                } catch (Exception e) {
                    employeePercentageToDeduct = 11.0;
                    totalPercentageToDeduct = 23.5;
                }
                PicklistItem deductionReason
                        = Setup.getRepository(PicklistItemRepository.class).findByListAndCodeIgnoreCase(
                        Setup.getRepository(PicklistRepository.class).findByCode("DeductionReasons"),
                        PicklistItem.getCode("pension_compensation"));

                Date firstDayOfMonth = DateUtil.getFirstOfMonthDate(DateUtil.getPreviousMonths(todo.getPayrollMonth(), 1));
                Date lastDayOfMonth = DateUtil.getLastOfMonthDate(DateUtil.getPreviousMonths(todo.getPayrollMonth(), 1));
                PayrollManagerNote note = Setup.getRepository(PayrollManagerNoteRepository.class)
                        .findTop1ByOfficeStaffAndNoteTypeAndDeductionReasonAndNoteDateGreaterThanEqualAndNoteDateLessThanEqual(
                                officeStaff,
                                AbstractPayrollManagerNote.ManagerNoteType.DEDUCTION,
                                deductionReason,
                                firstDayOfMonth,
                                lastDayOfMonth);

                if(officeStaff.getFixedPensionAmount() != null)
                    totalContributionAmount = officeStaff.getFixedPensionAmount();
                else
                    totalContributionAmount = officeStaff.getSalary() * (totalPercentageToDeduct / 100.0);

                if (note != null && (employeePercentageToDeduct - (note.getAmount() * 100 / officeStaff.getSalary()) != 0.0)) {
                    String subject = "ALERT - Difference between the paid pension amount and the % of the employee’s contribution";
                    List<EmailRecipient> recipients = Recipient.parseEmailsString(
                            Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_DIFFERENCE_BETWEEN_PENSION_AMOUNT_AND_CONTRIBUTION_RECIPIENTS));
                    Map<String, String> params = new HashMap<>();
                    params.put("employee_name", officeStaff.getName() != null ? officeStaff.getName() : "Staff");
                    params.put("amount_of_pension_todo", String.valueOf(totalContributionAmount));
                    params.put("pension_authority_percentage_after_31_10_2023", PayrollManagementModule.PARAMETER_PAYROLL_PENSION_AUTHORITY_PERCENTAGE_AFTER_31_10_2023);
                    params.put("pension_deduction_amount", String.valueOf(note.getAmount()));
                    params.put("pension_deduction_percentage_on_or_before_31_10_2023", PayrollManagementModule.PARAMETER_PAYROLL_PENSION_DEDUCTION_PERCENTAGE_ON_OR_BEFORE_31_10_2023);
                    params.put("pension_deduction_percentage_after_31_10_2023", PayrollManagementModule.PARAMETER_PAYROLL_PENSION_DEDUCTION_PERCENTAGE_AFTER_31_10_2023);
                    params.put("total_salary_amount", String.valueOf(officeStaff.getSalary()));
                    Date registrationDate =  officeStaff.getVisaNewRequest() != null ? officeStaff.getVisaNewRequest().findTaskMoveOutDate("Check Labour Card Approval") : null;
                    params.put("pension_registration_date", DateUtil.formatDateDashed(registrationDate));
                    Setup.getApplicationContext().getBean(MessagingService.class)
                            .send(recipients, null, "Payroll_Difference_Between_Pension_Amount_And_Contribution_Template", subject
                                    , null, null, params, officeStaff, null, officeStaff);
                }
            }
            return new OfficeStaffPayrollLog(officeStaff, officeStaff.getName(), officeStaff.getSalary(),
                    officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency().toString() : "", todo.getPayrollMonth(), employeePercentageToDeduct, totalPercentageToDeduct, totalContributionAmount);
        }
        throw new RuntimeException("Unable to generate payroll log for " + officeStaff.getName());
    }

    private String getBeneficiaryAddress(TransferDestination transferDestination) {
        StringBuilder result = new StringBuilder("");
        if (transferDestination != null) {
            if (transferDestination.getCountry() != null && transferDestination.getCountry().getName() != null && !transferDestination.getCountry().getName().isEmpty()) {
                result.append(transferDestination.getCountry().getName()).append(", ");
            }
            if (transferDestination.getCityAsString() != null) {
                result.append(transferDestination.getCityAsString()).append(", ");
            }
            if (transferDestination.getFullAddress() != null && !transferDestination.getFullAddress().isEmpty()) {
                result.append(transferDestination.getFullAddress());
            }
        }
        return result.toString();
    }

    @Transactional
    public OfficeStaffPayrollLog createPayrollLogBasedOnAll(OfficeStaff officeStaff, OfficeStaffPayrollBean bean, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo) {
        switch (monthlyPaymentRule.getPaymentMethod()) {
            case WPS:
                NewRequest visaNewRequest = officeStaff.getVisaNewRequest();
                return new OfficeStaffPayrollLog(officeStaff, monthlyPaymentRule, auditTodo, todo, "EDR", officeStaff.getName(), visaNewRequest,
                        getPayrollStartLockDate(monthlyPaymentRule), monthlyPaymentRule.getLockDate(), bean.getBalance(), monthlyPaymentRule.getPayrollMonth(), bean.getPreviouslyUnpaidSalaries(),officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency().toString() : "", bean.getLoanRepayment());
            case LOCAL_TRANSFER:
                String countryName = officeStaff.getCountry() != null ? officeStaff.getCountry().getName() : "";
                return new OfficeStaffPayrollLog(officeStaff, monthlyPaymentRule, auditTodo, todo, officeStaff.getName(), countryName, officeStaff.getPhoneNumber(),
                        bean.getBalance(), monthlyPaymentRule.getPayrollMonth(), bean.getPreviouslyUnpaidSalaries(),officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency().toString() : "", bean.getLoanRepayment());
            case ACCORDING_TO_EMPLOYEE_PROFILE: //Bank & International
                TransferDestination transferDestination = officeStaff.getSelectedTransferDestination();
                if(transferDestination == null)
                    return new OfficeStaffPayrollLog(officeStaff, monthlyPaymentRule, auditTodo, todo, "", "", "", "",
                            bean.getBalance(), officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency().toString() : "", "", "", "",
                            "", "", "", monthlyPaymentRule.getPayrollMonth(), bean.getPreviouslyUnpaidSalaries(),"", bean.getLoanRepayment());
                else
                    return new OfficeStaffPayrollLog(officeStaff, monthlyPaymentRule, auditTodo, todo, transferDestination.getName(), transferDestination.getFullNameInArabic(), transferDestination.getDetails(), transferDestination.getPhoneNumber(),
                            bean.getBalance(), officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency().toString() : "", transferDestination.getIban(), transferDestination.getAccountHolderName(), transferDestination.getAccountNumber(),
                            transferDestination.getBankName(), transferDestination.getSwiftCode(), getBeneficiaryAddress(transferDestination), monthlyPaymentRule.getPayrollMonth(), bean.getPreviouslyUnpaidSalaries(), transferDestination.getCountry() != null ? transferDestination.getCountry().getName() : "", bean.getLoanRepayment());
        }
        throw new RuntimeException("Unable to generate payroll log for " + officeStaff.getName());
    }

    @Transactional
    public OfficeStaffPayrollBean runTransactions(OfficeStaff officeStaff, LocalDate payrollStart, LocalDate payrollEnd, Date payrollStartForManagerNotes, Date payrollEndForManagerNotes, LocalDate payrollMonth, Boolean finalFile) {
        OfficeStaffPayrollBean bean = new OfficeStaffPayrollBean();
        Reflections reflections =
                new Reflections("com.magnamedia.salarycalculation.officestaff");
        Set<Class<? extends OfficeStaffSalaryTransaction>> allClasses =
                reflections.getSubTypesOf(OfficeStaffSalaryTransaction.class);
        List<String> managerNotesClassesNames = Arrays.asList(BasicSalaryTransaction.class.getSimpleName(), ManagerAdditionTransaction.class.getSimpleName(), ManagerDeductionTransaction.class.getSimpleName(), ManagerRaiseTransaction.class.getSimpleName(), ManagerReductionTransaction.class.getSimpleName());
        for (Class<?> clazz : allClasses) {
            try {
                Method calculateMethod =
                        clazz.getMethod(
                                "calculate",
                                OfficeStaff.class,
                                LocalDate.class,
                                LocalDate.class,
                                LocalDate.class,
                                Boolean.class);

                Method calculateMethodV2 =
                        clazz.getMethod(
                                "calculate",
                                OfficeStaff.class,
                                Date.class,
                                Date.class,
                                LocalDate.class,
                                Boolean.class);

                Method setInSalaryObjectMethod =
                        clazz.getMethod(
                                "setInSalaryObject",
                                OfficeStaffPayrollBean.class,
                                Double.class);
                OfficeStaffSalaryTransaction t = (OfficeStaffSalaryTransaction) clazz.newInstance();
                if (managerNotesClassesNames.contains(clazz.getSimpleName()))
                    bean = (OfficeStaffPayrollBean) setInSalaryObjectMethod.invoke(t, bean, (Double) calculateMethodV2.invoke(t, officeStaff, payrollStartForManagerNotes, payrollEndForManagerNotes, payrollMonth, finalFile));
                else
                    bean = (OfficeStaffPayrollBean) setInSalaryObjectMethod.invoke(t, bean, (Double) calculateMethod.invoke(t, officeStaff, payrollStart, payrollEnd, payrollMonth, finalFile));
            } catch (NoSuchMethodException | SecurityException | IllegalArgumentException | InvocationTargetException ex) {
                Logger.getLogger(OfficestaffPayrollController.class.getName()).log(Level.SEVERE, null, ex);
                DebugHelper.sendExceptionMail("<EMAIL>",ex , "Error in Office staff runTransactions: ", false);
            } catch (InstantiationException | IllegalAccessException ex) {
                Logger.getLogger(OfficeStaffPayrollBean.class.getName()).log(Level.SEVERE, null, ex);
                DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error in Office staff runTransactions: ", false);
            }
        }

        if(payrollStart != null)
            bean.setPayStartDate(new java.sql.Date(payrollStart.toDate().getTime()));
        if(payrollEnd != null)
            bean.setPayEndDate(new java.sql.Date(payrollEnd.toDate().getTime()));
        return bean;
    }

    public java.sql.Date getPayrollStartLockDate(MonthlyPaymentRule rule) {
        java.sql.Date lastMonthPayroll = new java.sql.Date(DateUtil.addMonths(rule.getPayrollMonth(), -1).getTime());
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(lastMonthPayroll);

        for (MonthlyPaymentRule monthlyPaymentRule : rules) {
            if (monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && ((rule.isTargetingOverseas() && monthlyPaymentRule.isTargetingOverseas()) ||
                    (rule.isTargetingExpats() && monthlyPaymentRule.isTargetingExpats()) ||
                    (rule.isTargetingEmiratis() && monthlyPaymentRule.isTargetingEmiratis()))) {
                return monthlyPaymentRule.getLockDate();
            }
        }

        LocalDate dt = new LocalDate(lastMonthPayroll);
        return new java.sql.Date(dt.withDayOfMonth(27).toDate().getTime());
    }

    public java.sql.Date getPayrollEndLockDate(MonthlyPaymentRule rule){
        return rule.getLockDate();
    }

    public Date getPayrollStartLockDateForManagerNotes(MonthlyPaymentRule rule) {
        java.sql.Date lastMonthPayroll = new java.sql.Date(DateUtil.addMonths(rule.getPayrollMonth(), -1).getTime());
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(lastMonthPayroll);

        for (MonthlyPaymentRule monthlyPaymentRule : rules) {
            if (monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && ((rule.isTargetingOverseas() && monthlyPaymentRule.isTargetingOverseas()) ||
                    (rule.isTargetingExpats() && monthlyPaymentRule.isTargetingExpats()) ||
                    (rule.isTargetingEmiratis() && monthlyPaymentRule.isTargetingEmiratis()))) {

                //return the complete date of the office staff audit to-do related to the monthly rule
                List<PayrollAuditTodo> auditTodos = Setup.getRepository(PayrollAuditTodoRepository.class).findByMonthlyPaymentRuleAndCompletedTrueAndTaskNameIn(monthlyPaymentRule, Arrays.asList(PaymentRuleEmployeeType.EXPATS.name(), PaymentRuleEmployeeType.OVERSEAS.name(), PaymentRuleEmployeeType.EMIRATI.name()));
                if (auditTodos != null && auditTodos.size() > 0)
                    return auditTodos.get(0).getLastMoveDate();
                return monthlyPaymentRule.getLockDate();
            }
        }

        LocalDate dt = new LocalDate(lastMonthPayroll);
        return dt.withDayOfMonth(27).toDate();
    }

    public Date getPayrollEndLockDateForManagerNotes(MonthlyPaymentRule rule){
        //if it's a fake monthly rule
        if (rule.getId() == null)
            return new Date();

        //return the complete date of the office staff audit to-do related to the monthly rule
        List<PayrollAuditTodo> auditTodos = Setup.getRepository(PayrollAuditTodoRepository.class).findByMonthlyPaymentRuleAndCompletedTrueAndTaskNameIn(rule, Arrays.asList(PaymentRuleEmployeeType.EXPATS.name(), PaymentRuleEmployeeType.OVERSEAS.name(), PaymentRuleEmployeeType.EMIRATI.name()));
        if (auditTodos != null && auditTodos.size() > 0)
            return auditTodos.get(0).getLastMoveDate();

        //no audit is closed then return current date
        return new Date();
    }
}
