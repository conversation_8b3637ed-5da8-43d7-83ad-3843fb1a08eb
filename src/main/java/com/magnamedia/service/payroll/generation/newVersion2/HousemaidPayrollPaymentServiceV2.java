package com.magnamedia.service.payroll.generation.newVersion2;

import com.magnamedia.controller.HousemaidPayrollController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.entity.ExcludedMVInfo;
import com.magnamedia.entity.projection.HousemaidContractProjection;
import com.magnamedia.entity.projection.payrollAudit.ExcludedMVInfoProjection;
import com.magnamedia.entity.HousemaidPayrollBean;
import com.magnamedia.extra.HousemaidPayrollLogProjection;
import com.magnamedia.extra.PayrollAuditTodoTaskNameType;
import com.magnamedia.extra.payroll.init.HousemaidContractInfoProjection;
import com.magnamedia.extra.payroll.init.HousemaidDateProjection;
import com.magnamedia.extra.payroll.init.HousemaidFieldProjection;
import com.magnamedia.extra.payroll.init.HousemaidPayrollInitializer;
import com.magnamedia.helper.*;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.salarycalculation.HousemaidSalaryTransaction;
import com.magnamedia.salarycalculation.HousemaidSalaryTransactionNew;
import com.magnamedia.salarycalculation.HousemaidSalaryTransactionNewPhaseTwo;
import com.magnamedia.salarycalculation.HousemaidSalaryTransactionNewPhaseZero;
import com.magnamedia.service.NegativeSalariesService;
import com.magnamedia.service.payroll.generation.newversion.HousemaidPayrollAuditService;
import com.magnamedia.service.payroll.generation.newversion.LockDateService;
import com.magnamedia.service.payroll.generation.newversion.PayrollGenerationHelperService;
import com.magnamedia.service.payroll.generation.newversion.ProRatedSalariesService;
import org.joda.time.LocalDate;
import org.reflections.Reflections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigInteger;
import java.sql.Date;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class HousemaidPayrollPaymentServiceV2 {

    public static HousemaidPayrollInitializer initializer = null;
    private final Logger logger = Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName());


    @Autowired
    private MonthlyPaymentRuleRepository monthlyPaymentRuleRepository;

    @Autowired
    private NegativeSalariesService negativeSalariesService;

    @Autowired
    private ProRatedSalariesService proRatedSalariesService;

    @Autowired
    private LockDateService lockDateService;

    @Autowired
    private HousemaidPayrollAuditService housemaidPayrollAuditService;

    @Autowired
    private PayrollAuditTodoRepository payrollAuditTodoRepository;

    @Autowired
    private HousemaidPayrollLogRepository housemaidPayrollLogRepository;

    @Autowired
    private HousemaidPayrollBeanRepository housemaidPayrollBeanRepository;

    @Autowired
    private PayrollGenerationHelperService payrollGenerationHelperService;

    /**
     * return all the manager notes picklistitem types that must be included even if we are in Secondary Payroll
     *
     * @return
     */
    public static List<PicklistItem> getMustBePaidManagerNotes() {
        // **who have salary dispute PAY-282
        // **who have Taxi Reimbursement PAY-344
        // **who have Forgive Deduction PAY-376
        List<PicklistItem> result = new ArrayList<>();
        result.add(PicklistHelper.getItem(
                PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                "salary_dispute"));
        result.add(PicklistHelper.getItem(
                PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                "taxi_reimbursement"));
        result.add(PicklistHelper.getItem(
                PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                "forgive_deduction"));
        result.add(PicklistHelper.getItem(
                PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                "airfare_ticket"));
        result.add(PicklistHelper.getItem(
                PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                "AR-1"));
        result.add(PicklistHelper.getItem(
                PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                "anti_attrition_incentive"));
        result.add(PicklistHelper.getItem(
                PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                "Maids_at_other_expenses"));
        result.add(PicklistHelper.getItem(
                PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                "medical_assistant"));
        return result;
    }

    /**
     * get the list of housemaid statuses that we need to generate a payroll log for it in Primary Payroll
     *
     * @param monthlyPaymentRule
     * @return
     */
    public static List<HousemaidStatus> getMustBeGeneratedHousemaidsStatuses(MonthlyPaymentRule monthlyPaymentRule) {
        List<HousemaidStatus> wpsHousemaidsStatuses = Arrays.asList(HousemaidStatus.WITH_CLIENT);
        List<HousemaidStatus> payCashHousemaidsStatuses = Arrays.asList(
                HousemaidStatus.VIP_RESERVATIONS, HousemaidStatus.AVAILABLE,
                HousemaidStatus.RESERVED_FOR_PROSPECT, HousemaidStatus.RESERVED_FOR_REPLACEMENT,
                HousemaidStatus.SICK_WITHOUT_CLIENT, HousemaidStatus.NO_SHOW,
                HousemaidStatus.ON_VACATION, HousemaidStatus.LANDED_IN_DUBAI,
                HousemaidStatus.PENDING_FOR_VIDEOSHOOT, HousemaidStatus.RESERVED_HOME_VISIT,
                HousemaidStatus.ASSIGNED_OFFICE_WORK, HousemaidStatus.PENDING_FOR_DISCIPLINE);
        if (monthlyPaymentRule.isTargetingWithClientOnly())
            return wpsHousemaidsStatuses;
        else if (monthlyPaymentRule.isTargetingInAccommodationOnly())
            return payCashHousemaidsStatuses;
        else {
            List<HousemaidStatus> all = new ArrayList<>(wpsHousemaidsStatuses);
            all.addAll(payCashHousemaidsStatuses);
            return all;
        }
    }

    public static HousemaidPayrollInitializer getHousemaidPayrollInitializer() {
        return initializer;
    }

    public static void prepareHousemaidPayrollInitializer(List<Long> housemaidIds, LocalDate start, LocalDate end, MonthlyPaymentRule rule) {
        if (initializer != null) {
            throw new RuntimeException("Housemaids payroll is already being generated. Please wait until it's finished and try again.");
        }
        initializer = new HousemaidPayrollInitializer(housemaidIds, start, end, rule);
    }

    public static void clearHousemaidPayrollInitializer() {
        initializer = null;
    }

    /**
     * return the filter of Included maids that will get their salaries
     *
     * @param monthlyPaymentRule
     * @return
     */
    public SelectFilter getIncludedTargetListFilter(MonthlyPaymentRule monthlyPaymentRule) {
        java.sql.Date payrollStart = getPayrollStartLockDate(monthlyPaymentRule);

        SelectFilter maidCCFilter = null;
        SelectFilter maidVisaFilter = null;
        Set<Long> excludedCCHousemaids = new HashSet<>();


        // MAID CC Filter
        if (monthlyPaymentRule.isTargetingMaidCC()) {
            maidCCFilter = new SelectFilter();
            maidCCFilter.and("housemaidType", "<>", HousemaidType.MAID_VISA);

            //delayed items
            List<PicklistItem> delayItems = getDelayedItems();

            SelectFilter wpsHousemaidsFilter = new SelectFilter("status", "=", HousemaidStatus.WITH_CLIENT);

            SelectFilter payCashHousemaidsFilter = new SelectFilter(
                    new SelectFilter("status", "=", HousemaidStatus.VIP_RESERVATIONS)
                            .or("status", "=", HousemaidStatus.AVAILABLE)
                            .or("status", "=", HousemaidStatus.RESERVED_FOR_PROSPECT)
                            .or("status", "=", HousemaidStatus.RESERVED_FOR_REPLACEMENT)
                            .or("status", "=", HousemaidStatus.SICK_WITHOUT_CLIENT)
                            //Jirra ACC-1005
                            //.or("status", "=", HousemaidStatus.ON_VACATION)
                            .or("status", "=", HousemaidStatus.LANDED_IN_DUBAI)
                            .or("status", "=", HousemaidStatus.PENDING_FOR_VIDEOSHOOT)
                            .or("status", "=", HousemaidStatus.RESERVED_HOME_VISIT)
                            //Jirra ACC-1223
                            .or("status", "=", HousemaidStatus.ASSIGNED_OFFICE_WORK)
                            .or(
                                    new SelectFilter("status", "=", HousemaidStatus.PENDING_FOR_DISCIPLINE)
                                            //Jirra ACC-1005
                                            .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION)
                                            .and("pendingStatus", "<>", PendingStatus.REPEAT_MEDICAL)
                                            .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION_TAWAFUQ)
                                            .and(new SelectFilter("reasonOfPending", "IS NULL", null)
                                                    .or(new SelectFilter("reasonOfPending", "NOT IN", delayItems))
                                                    .or(new SelectFilter("pendingSince", "IS NOT NULL", null)
                                                            .and("pendingSince", ">=", new LocalDate().toDate())
                                                            .or(new SelectFilter("pendingUntil", "IS NOT NULL", null)
                                                                    .and("pendingUntil", ">=", payrollStart))))
                            ));


            if (monthlyPaymentRule.isTargetingInAccommodationOnly()) {
                maidCCFilter.and(payCashHousemaidsFilter);
            } else if (monthlyPaymentRule.isTargetingWithClientOnly()) {
                maidCCFilter.and(wpsHousemaidsFilter);
            } else {
                maidCCFilter.and(new SelectFilter(payCashHousemaidsFilter).or(wpsHousemaidsFilter));
            }


            // exclude pre-paid vacation housemaids
            PicklistItem vacationType =
                    PicklistHelper.getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE,
                            "pre-paid_vacation");
            List<Long> prePaidVacationMaids = Setup.getRepository(ScheduledAnnualVacationRepository.class)
                    .findScheduledVacationsOfLastPayroll(vacationType, 0.0, lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -2, PaymentRuleEmployeeType.HOUSEMAIDS),
                            lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -1, PaymentRuleEmployeeType.HOUSEMAIDS));
            if (!prePaidVacationMaids.isEmpty())
                excludedCCHousemaids.addAll(prePaidVacationMaids);

        }

        Set<Long> excludedHousemaids = new HashSet<>();

        // exclude transferred before housemaids But not those who have important manager notes
        List<PicklistItem> mustBePaidManagerNotes = getMustBePaidManagerNotes();

        List<Long> haveMustBePaidManagerNotesMaids = Setup.getRepository(PayrollManagerNoteRepository.class)
                .findHousemaidsWithNonPaidSalaryDisputeAndTaxiNotes(payrollStart, new java.sql.Date(System.currentTimeMillis()),
                        AbstractPayrollManagerNote.ManagerNoteType.ADDITION, mustBePaidManagerNotes);

        List<Long> transferredIds;

        if (haveMustBePaidManagerNotesMaids.isEmpty()) {
            transferredIds = Setup.getRepository(HousemaidPayrollLogRepository.class).
                    findByPayrollMonthAndTransferredTrue(monthlyPaymentRule.getPayrollMonth());
        } else {
            transferredIds = Setup.getRepository(HousemaidPayrollLogRepository.class).
                    findByPayrollMonthAndTransferredTrue(monthlyPaymentRule.getPayrollMonth(), haveMustBePaidManagerNotesMaids);
        }

        if (!transferredIds.isEmpty())
            excludedHousemaids.addAll(transferredIds);

        // MAID VISA Filter
        if (monthlyPaymentRule.isTargetingMaidVisa()) {
            maidVisaFilter = new SelectFilter();
            maidVisaFilter.and("housemaidType", "=", HousemaidType.MAID_VISA);

            List<Long> paidHousemaidsIds = getPaidMVMaidsIdsNewV2(monthlyPaymentRule, transferredIds, false);

            // add the MV maids who were included by the auditor
            if (monthlyPaymentRule.getMustIncludedMVInfoList().size() > 0)
                paidHousemaidsIds.addAll(monthlyPaymentRule.getMustIncludedMVInfoList().stream().filter(x-> x.getPayrollMonth().equals(monthlyPaymentRule.getPayrollMonth())).map(ExcludedMVInfo::getHousemaidId).collect(Collectors.toList()));

            if(paidHousemaidsIds.size() > 0)
                maidVisaFilter.and("id", "IN", paidHousemaidsIds);
            else if (maidVisaFilter != null)
                maidVisaFilter = null;
            else
                return null;
        }


        SelectFilter baseFilter;
        if(maidCCFilter != null && maidVisaFilter != null)
            baseFilter = new SelectFilter(maidCCFilter.or(maidVisaFilter));
        else if (maidCCFilter != null)
            baseFilter = new SelectFilter(maidCCFilter);
        else
            baseFilter = new SelectFilter(maidVisaFilter);

        // Not excluded from payroll
        SelectFilter notExcludedFromPayroll = new SelectFilter(new SelectFilter("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null));
        baseFilter.and(notExcludedFromPayroll);

        //MOL Filter
        if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
            baseFilter.and("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
        }

        //excluded
        excludedHousemaids.addAll(excludedCCHousemaids);
        excludedHousemaids.addAll(monthlyPaymentRuleRepository.getExcludedMaidsDueToMissingFields(monthlyPaymentRule));
        excludedHousemaids.addAll(monthlyPaymentRuleRepository.getExcludedMaidsDueToOverLap(monthlyPaymentRule));
//        excludedHousemaids.addAll(monthlyPaymentRule.getExcludedMaidDueToEVisaIssue().stream().map(BaseEntity::getId).collect(Collectors.toSet()));
        excludedHousemaids.addAll(monthlyPaymentRuleRepository.getExcludedDueToMedicalTest(monthlyPaymentRule));

        if (!excludedHousemaids.isEmpty())
            baseFilter.and("id", "NOT IN", excludedHousemaids);

        //start date condition
        baseFilter.and("startDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate());
        baseFilter.and(new SelectFilter("replacementSalaryStartDate", "IS NULL", null).or("replacementSalaryStartDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate()));

        return baseFilter;
    }

    public SelectFilter getIncludedTargetListFilterNew(MonthlyPaymentRule monthlyPaymentRule) {
        java.sql.Date payrollStart = getPayrollStartLockDate(monthlyPaymentRule);

        SelectFilter maidCCFilter = null;
        SelectFilter maidVisaFilter = null;
        Set<Long> excludedCCHousemaids = new HashSet<>();

        if (monthlyPaymentRule.isTargetingMaidCC()) {
            maidCCFilter = new SelectFilter();
            maidCCFilter.and("housemaidType", "<>", HousemaidType.MAID_VISA);

            List<PicklistItem> delayItems = getDelayedItems();
            SelectFilter wpsHousemaidsFilter = new SelectFilter("status", "=", HousemaidStatus.WITH_CLIENT);

            SelectFilter payCashHousemaidsFilter = new SelectFilter(
                    new SelectFilter("status", "=", HousemaidStatus.VIP_RESERVATIONS)
                            .or("status", "=", HousemaidStatus.AVAILABLE)
                            .or("status", "=", HousemaidStatus.RESERVED_FOR_PROSPECT)
                            .or("status", "=", HousemaidStatus.RESERVED_FOR_REPLACEMENT)
                            .or("status", "=", HousemaidStatus.SICK_WITHOUT_CLIENT)
                            .or("status", "=", HousemaidStatus.LANDED_IN_DUBAI)
                            .or("status", "=", HousemaidStatus.PENDING_FOR_VIDEOSHOOT)
                            .or("status", "=", HousemaidStatus.RESERVED_HOME_VISIT)
                            .or("status", "=", HousemaidStatus.ASSIGNED_OFFICE_WORK)
                            .or(
                                    new SelectFilter("status", "=", HousemaidStatus.PENDING_FOR_DISCIPLINE)
                                            .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION)
                                            .and("pendingStatus", "<>", PendingStatus.REPEAT_MEDICAL)
                                            .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION_TAWAFUQ)
                                            .and(new SelectFilter("reasonOfPending", "IS NULL", null)
                                                    .or(new SelectFilter("reasonOfPending", "NOT IN", delayItems))
                                                    .or(new SelectFilter("pendingSince", "IS NOT NULL", null)
                                                            .and("pendingSince", ">=", new LocalDate().toDate())
                                                            .or(new SelectFilter("pendingUntil", "IS NOT NULL", null)
                                                                    .and("pendingUntil", ">=", payrollStart))))
                            ));

            if (monthlyPaymentRule.isTargetingInAccommodationOnly()) {
                maidCCFilter.and(payCashHousemaidsFilter);
            } else if (monthlyPaymentRule.isTargetingWithClientOnly()) {
                maidCCFilter.and(wpsHousemaidsFilter);
            } else {
                maidCCFilter.and(new SelectFilter(payCashHousemaidsFilter).or(wpsHousemaidsFilter));
            }

            PicklistItem vacationType =
                    PicklistHelper.getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE,
                            "pre-paid_vacation");
            List<Long> prePaidVacationMaids = Setup.getRepository(ScheduledAnnualVacationRepository.class)
                    .findScheduledVacationsOfLastPayroll(vacationType, 0.0, lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -2, PaymentRuleEmployeeType.HOUSEMAIDS),
                            lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -1, PaymentRuleEmployeeType.HOUSEMAIDS));
            if (!prePaidVacationMaids.isEmpty())
                excludedCCHousemaids.addAll(prePaidVacationMaids);
        }

        Set<Long> excludedHousemaids = new HashSet<>();

        List<PicklistItem> mustBePaidManagerNotes = getMustBePaidManagerNotes();

        List<Long> haveMustBePaidManagerNotesMaids = Setup.getRepository(PayrollManagerNoteRepository.class)
                .findHousemaidsWithNonPaidSalaryDisputeAndTaxiNotes(payrollStart, new java.sql.Date(System.currentTimeMillis()),
                        AbstractPayrollManagerNote.ManagerNoteType.ADDITION, mustBePaidManagerNotes);

        List<Long> transferredIds;

        if (haveMustBePaidManagerNotesMaids.isEmpty()) {
            transferredIds = Setup.getRepository(HousemaidPayrollLogRepository.class)
                    .findByPayrollMonthAndTransferredTrue(monthlyPaymentRule.getPayrollMonth());
        } else {
            transferredIds = Setup.getRepository(HousemaidPayrollLogRepository.class)
                    .findByPayrollMonthAndTransferredTrue(monthlyPaymentRule.getPayrollMonth(), haveMustBePaidManagerNotesMaids);
        }

        if (!transferredIds.isEmpty())
            excludedHousemaids.addAll(transferredIds);

        if (monthlyPaymentRule.isTargetingMaidVisa()) {
            maidVisaFilter = new SelectFilter();
            maidVisaFilter.and("housemaidType", "=", HousemaidType.MAID_VISA);
            java.sql.Date date = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).withDayOfMonth(1).toDate().getTime());

            List<Long> paidHousemaidsIds = Setup.getRepository(PaymentRepository.class).findPaidMaidVisaByHousemaidForNextPayroll(date);

            if (!monthlyPaymentRule.getMustIncludedMVInfoList().isEmpty())
                paidHousemaidsIds.addAll(monthlyPaymentRule.getMustIncludedMVInfoList().stream()
                        .filter(x -> x.getPayrollMonth().equals(monthlyPaymentRule.getPayrollMonth()))
                        .map(ExcludedMVInfo::getHousemaidId)
                        .collect(Collectors.toList()));

            if (!paidHousemaidsIds.isEmpty())
                maidVisaFilter.and("id", "IN", paidHousemaidsIds);
            else maidVisaFilter = null;
        }

        SelectFilter baseFilter;
        if (maidCCFilter != null && maidVisaFilter != null)
            baseFilter = new SelectFilter(maidCCFilter.or(maidVisaFilter));
        else if (maidCCFilter != null)
            baseFilter = new SelectFilter(maidCCFilter);
        else
            baseFilter = new SelectFilter(maidVisaFilter);

        SelectFilter notExcludedFromPayroll = new SelectFilter(new SelectFilter("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null));
        baseFilter.and(notExcludedFromPayroll);

        if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
            baseFilter.and("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
        }

        excludedHousemaids.addAll(excludedCCHousemaids);
        excludedHousemaids.addAll(monthlyPaymentRuleRepository.getExcludedMaidsDueToMissingFields(monthlyPaymentRule));
        excludedHousemaids.addAll(monthlyPaymentRuleRepository.getExcludedMaidsDueToOverLap(monthlyPaymentRule));
        excludedHousemaids.addAll(monthlyPaymentRuleRepository.getExcludedDueToMedicalTest(monthlyPaymentRule));

        if (!excludedHousemaids.isEmpty())
            baseFilter.and("id", "NOT IN", excludedHousemaids);

        baseFilter.and("startDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate());
        baseFilter.and(new SelectFilter("replacementSalaryStartDate", "IS NULL", null).or("replacementSalaryStartDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate()));

        return baseFilter;
    }

    /**
     * return the list of housemaid that will be included and get their salaries
     *
     * @param monthlyPaymentRule
     * @return
     */
    public List<Housemaid> getIncludedTargetList(MonthlyPaymentRule monthlyPaymentRule) {
        // if not targeting housemaid
        if (!monthlyPaymentRule.isTargetingHousemaid())
            return new ArrayList<>();

        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        query.filterBy(getIncludedTargetListFilter(monthlyPaymentRule));

        query.sortBy("name", true);
        return query.execute();
    }

    public List<Housemaid> getIncludedTargetListNew(MonthlyPaymentRule monthlyPaymentRule) {
        // if not targeting housemaid
        if (!monthlyPaymentRule.isTargetingHousemaid())
            return new ArrayList<>();

        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        query.filterBy(getIncludedTargetListFilterNew(monthlyPaymentRule));

        query.sortBy("name", true);
        return query.execute();
    }

    public List<Housemaid> getIncludedTargetListBasedOnHousemaidTypeAndHousemaidStatus(
            MonthlyPaymentRule monthlyPaymentRule,
            Boolean maidVisa,
            Boolean withClient
    ) { // ture = with_client - not needed in case of maid_visa

        // if not targeting housemaid
        if (!monthlyPaymentRule.isTargetingHousemaid())
            return new ArrayList<>();

        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        query.filterBy(getIncludedTargetListBasedOnHousemaidTypeAndHousemaidStatusFilter(monthlyPaymentRule, maidVisa, withClient));

        return query.execute();
    }

    public SelectFilter getIncludedTargetListBasedOnHousemaidTypeAndHousemaidStatusFilter(
            MonthlyPaymentRule monthlyPaymentRule,
            Boolean maidVisa,
            Boolean withClient
    ) {
        java.sql.Date payrollStart = getPayrollStartLockDate(monthlyPaymentRule);

        SelectFilter maidCCFilter = null;
        SelectFilter maidVisaFilter = null;
        Set<Long> excludedCCHousemaids = new HashSet<>();


        // MAID CC Filter
        if (!maidVisa) {
            maidCCFilter = new SelectFilter();
            maidCCFilter.and("housemaidType", "<>", HousemaidType.MAID_VISA);

            //delayed items
            List<PicklistItem> delayItems = getDelayedItems();

            SelectFilter wpsHousemaidsFilter = new SelectFilter();
            SelectFilter payCashHousemaidsFilter = new SelectFilter();

            if (withClient == null || withClient)
                wpsHousemaidsFilter = new SelectFilter("status", "=", HousemaidStatus.WITH_CLIENT);

            if (withClient == null || !withClient) {
                payCashHousemaidsFilter = new SelectFilter(
                        new SelectFilter("status", "=", HousemaidStatus.VIP_RESERVATIONS)
                                .or("status", "=", HousemaidStatus.AVAILABLE)
                                .or("status", "=", HousemaidStatus.RESERVED_FOR_PROSPECT)
                                .or("status", "=", HousemaidStatus.RESERVED_FOR_REPLACEMENT)
                                .or("status", "=", HousemaidStatus.SICK_WITHOUT_CLIENT)
                                //Jirra ACC-1005
                                //.or("status", "=", HousemaidStatus.ON_VACATION)
                                .or("status", "=", HousemaidStatus.LANDED_IN_DUBAI)
                                .or("status", "=", HousemaidStatus.PENDING_FOR_VIDEOSHOOT)
                                .or("status", "=", HousemaidStatus.RESERVED_HOME_VISIT)
                                //Jirra ACC-1223
                                .or("status", "=", HousemaidStatus.ASSIGNED_OFFICE_WORK)
                                .or(
                                        new SelectFilter("status", "=", HousemaidStatus.PENDING_FOR_DISCIPLINE)
                                                //Jirra ACC-1005
                                                .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION)
                                                .and("pendingStatus", "<>", PendingStatus.REPEAT_MEDICAL)
                                                .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION_TAWAFUQ)
                                                .and(new SelectFilter("reasonOfPending", "IS NULL", null)
                                                        .or(new SelectFilter("reasonOfPending", "NOT IN", delayItems))
                                                        .or(new SelectFilter("pendingSince", "IS NOT NULL", null)
                                                                .and("pendingSince", ">=", new LocalDate().toDate())
                                                                .or(new SelectFilter("pendingUntil", "IS NOT NULL", null)
                                                                        .and("pendingUntil", ">=", payrollStart))))
                                ));
            }

            if(withClient != null) {
                if (!withClient) {
                    maidCCFilter.and(payCashHousemaidsFilter);
                } else if (withClient) {
                    maidCCFilter.and(wpsHousemaidsFilter);
                }
            } else { //with_client = null
                maidCCFilter.and(new SelectFilter(payCashHousemaidsFilter).or(wpsHousemaidsFilter));
            }


            // exclude pre-paid vacation housemaids
            PicklistItem vacationType =
                    PicklistHelper.getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE,
                            "pre-paid_vacation");
            List<Long> prePaidVacationMaids = Setup.getRepository(ScheduledAnnualVacationRepository.class)
                    .findScheduledVacationsOfLastPayroll(vacationType, 0.0, lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -2, PaymentRuleEmployeeType.HOUSEMAIDS),
                            lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -1, PaymentRuleEmployeeType.HOUSEMAIDS));
            if (!prePaidVacationMaids.isEmpty())
                excludedCCHousemaids.addAll(prePaidVacationMaids);

        }

        Set<Long> excludedHousemaids = new HashSet<>();

        // exclude transferred before housemaids But not those who have important manager notes
        List<PicklistItem> mustBePaidManagerNotes = getMustBePaidManagerNotes();

        List<Long> haveMustBePaidManagerNotesMaids = Setup.getRepository(PayrollManagerNoteRepository.class)
                .findHousemaidsWithNonPaidSalaryDisputeAndTaxiNotes(payrollStart, new java.sql.Date(System.currentTimeMillis()),
                        AbstractPayrollManagerNote.ManagerNoteType.ADDITION, mustBePaidManagerNotes);

        List<Long> transferredIds;

        if (haveMustBePaidManagerNotesMaids.isEmpty()) {
            transferredIds = Setup.getRepository(HousemaidPayrollLogRepository.class).
                    findByPayrollMonthAndTransferredTrue(monthlyPaymentRule.getPayrollMonth());
        } else {
            transferredIds = Setup.getRepository(HousemaidPayrollLogRepository.class).
                    findByPayrollMonthAndTransferredTrue(monthlyPaymentRule.getPayrollMonth(), haveMustBePaidManagerNotesMaids);
        }

        if (!transferredIds.isEmpty())
            excludedHousemaids.addAll(transferredIds);

        // MAID VISA Filter
        if (maidVisa) {
            maidVisaFilter = new SelectFilter();
            maidVisaFilter.and("housemaidType", "=", HousemaidType.MAID_VISA);

            List<Long> paidHousemaidsIds = getPaidMVMaidsIdsNewV2(monthlyPaymentRule, transferredIds, false);

            // add the MV maids who were included by the auditor
            if (monthlyPaymentRule.getMustIncludedMVInfoList().size() > 0)
                paidHousemaidsIds.addAll(monthlyPaymentRule.getMustIncludedMVInfoList().stream().filter(x-> x.getPayrollMonth().equals(monthlyPaymentRule.getPayrollMonth())).map(ExcludedMVInfo::getHousemaidId).collect(Collectors.toList()));

            if(paidHousemaidsIds.size() > 0)
                maidVisaFilter.and("id", "IN", paidHousemaidsIds);
            else if (maidVisaFilter != null)
                maidVisaFilter = null;
            else
                return null;
        }


        SelectFilter baseFilter;
        if(maidCCFilter != null && maidVisaFilter != null)
            baseFilter = new SelectFilter(maidCCFilter.or(maidVisaFilter));
        else if (maidCCFilter != null)
            baseFilter = new SelectFilter(maidCCFilter);
        else
            baseFilter = new SelectFilter(maidVisaFilter);

        // Not excluded from payroll
        SelectFilter notExcludedFromPayroll = new SelectFilter(new SelectFilter("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null));
        baseFilter.and(notExcludedFromPayroll);

        //MOL Filter
        if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
            baseFilter.and("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
        }

        //excluded
        excludedHousemaids.addAll(excludedCCHousemaids);
        excludedHousemaids.addAll(monthlyPaymentRule.getExcludedMaidsDueToMissingFields().stream().map(BaseEntity::getId).collect(Collectors.toSet()));
        excludedHousemaids.addAll(monthlyPaymentRule.getExcludedMaidsDueToOverLap().stream().map(BaseEntity::getId).collect(Collectors.toSet()));
//        excludedHousemaids.addAll(monthlyPaymentRule.getExcludedMaidDueToEVisaIssue().stream().map(BaseEntity::getId).collect(Collectors.toSet()));
        excludedHousemaids.addAll(monthlyPaymentRule.getExcludedDueToMedicalTest().stream().map(BaseEntity::getId).collect(Collectors.toList()));

        if (!excludedHousemaids.isEmpty())
            baseFilter.and("id", "NOT IN", excludedHousemaids);

        //start date condition
        baseFilter.and("startDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate());
        baseFilter.and(new SelectFilter("replacementSalaryStartDate", "IS NULL", null).or("replacementSalaryStartDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate()));

        return baseFilter;
    }


    /**
     * return the filter Of PRIMARY Salary of maids that we will generate a Payroll Log for them
     *
     * @param monthlyPaymentRule
     * @return
     */
    public SelectFilter getPrimaryTargetListFilter(MonthlyPaymentRule monthlyPaymentRule) {

        SelectFilter maidCCFilter = new SelectFilter();
        SelectFilter maidVisaFilter = new SelectFilter();

        // Maid CC Filter
        if(monthlyPaymentRule.isTargetingMaidCC()) {
            //get all Housemaid Statuses that we need to generate payroll log for it
            maidCCFilter = new SelectFilter("status", "IN", getMustBeGeneratedHousemaidsStatuses(monthlyPaymentRule));
            maidCCFilter.and("housemaidType", "<>", HousemaidType.MAID_VISA);


        }

        // Maid Visa Filter
        if(monthlyPaymentRule.isTargetingMaidVisa()){

            maidVisaFilter = new SelectFilter("housemaidType", "=", HousemaidType.MAID_VISA);
        }

        if (monthlyPaymentRule.isTargetingMaidCCOnly()) {
            maidVisaFilter = null;
        } else if (monthlyPaymentRule.isTargetingMaidVisaOnly()) {
            maidCCFilter = null;
        }

        SelectFilter baseFilter;
        if(maidCCFilter != null && maidVisaFilter != null)
            baseFilter = new SelectFilter(maidCCFilter.or(maidVisaFilter));
        else if (maidCCFilter != null)
            baseFilter = new SelectFilter(maidCCFilter);
        else if (maidVisaFilter != null)
            baseFilter = new SelectFilter(maidVisaFilter);
        else
            return null;

        //MOL Filter
        if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
            baseFilter.and("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
        }


        Set<Long> excludedHousemaids = new HashSet<>();

        // exclude pre-paid vacation housemaids
        PicklistItem vacationType =
                PicklistHelper.getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE,
                        "pre-paid_vacation");
        List<Long> prePaidVacationMaids = Setup.getRepository(ScheduledAnnualVacationRepository.class)
                .findScheduledVacationsOfLastPayroll(vacationType, 0.0, lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -2, PaymentRuleEmployeeType.HOUSEMAIDS),
                        lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -1, PaymentRuleEmployeeType.HOUSEMAIDS));
        if (!prePaidVacationMaids.isEmpty()) excludedHousemaids.addAll(prePaidVacationMaids);

        if (!excludedHousemaids.isEmpty()) baseFilter.and("id", "NOT IN", excludedHousemaids);

        //startDate && replacementSalaryStartDate must be before 27 of the month
        baseFilter.and("startDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate());
        baseFilter.and(new SelectFilter("replacementSalaryStartDate", "IS NULL", null).or("replacementSalaryStartDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate()));

        return baseFilter;
    }

    /**
     * return the filter Of Secondary Salary of maids that we will check if we can give them their salary
     *
     * @param monthlyPaymentRule
     * @return
     */
    public SelectFilter getSecondaryTargetListFilter(MonthlyPaymentRule monthlyPaymentRule) {
        java.sql.Date payrollStart = getPayrollStartLockDate(monthlyPaymentRule);

        //get Included housemaids
        SelectFilter baseFilter = getIncludedTargetListFilter(monthlyPaymentRule);

//        //get all unpaid housemaid ids
//        List<Long> unpaidHousemaidIds = Setup.getRepository(HousemaidPayrollLogRepository.class).findByPayrollMonthAndTransferredFalse(monthlyPaymentRule.getPayrollMonth());
//
//        //if there are no unpaid maids then no need to handle anything
//        if(unpaidHousemaidIds.isEmpty())
//            return null;
//
//        baseFilter.and("id", "IN", unpaidHousemaidIds);

        return baseFilter;
    }

    public List<Housemaid> getTargetList(MonthlyPaymentRule monthlyPaymentRule) {
        if (!monthlyPaymentRule.isTargetingHousemaid()) return new ArrayList<>();

        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        SelectFilter selectFilter = !PayrollType.SECONDARY.equals(monthlyPaymentRule.getPayrollType())
                ? getPrimaryTargetListFilter(monthlyPaymentRule) : getSecondaryTargetListFilter(monthlyPaymentRule);

        //if filter is null then there is no maid to give here the salary
        if(selectFilter == null)
            return new ArrayList<>();


        query.filterBy(selectFilter);
        query.sortBy("name", true);
        return query.execute();
    }


//    public List<Housemaid> getUnpaidMaidsVisaList(MonthlyPaymentRule monthlyPaymentRule) {
//        SelectQuery<Housemaid> basicQuery = new SelectQuery<>(Housemaid.class);
//        List<Housemaid> unpaidMaidsVisaList = new ArrayList<>();
//
//        List<Long> unpaidIds = null;
//        if (monthlyPaymentRule.isTargetingMaidVisa()) {
//            java.sql.Date start = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).withDayOfMonth(1).toDate().getTime());
//            java.sql.Date end = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).dayOfMonth().withMaximumValue().toDate().getTime());
//            Double vatPercentage = 1.0 + (Integer.parseInt(Setup.getParameter(Setup.getModule("sales"),
//                    PayrollManagementModule.PARAMETER_SALES_VAT_PERCENT)) * 1.0 / 100);
//
//            List<BigInteger> paidIdsObj = Setup.getRepository(PaymentRepository.class)
//                    .findPaidMaidVisaClients(ContractStatus.ACTIVE.toString(), HousemaidType.MAID_VISA.toString(),
//                            PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId(),
//                            start, end, PaymentStatus.RECEIVED.toString(), vatPercentage);
//            List<Long> paidIds = paidIdsObj.stream().map(x -> x != null ? x.longValue() : 0L).collect(Collectors.toList());
//
//            // add the MV maids who were included by the auditor
//            if (monthlyPaymentRule.getMustIncludedMVList().size() > 0)
//                paidIds.addAll(monthlyPaymentRule.getMustIncludedMVList().stream().map(BaseEntity::getId).collect(Collectors.toList()));
//
//
//            if (paidIds.isEmpty()) {
//                unpaidIds = Setup.getRepository(PaymentRepository.class)
//                        .findUnPaidMaidVisaClientsV2(HousemaidType.MAID_VISA);
//            } else {
//                unpaidIds = Setup.getRepository(PaymentRepository.class)
//                        .findUnPaidMaidVisaClientsV2(HousemaidType.MAID_VISA, paidIds);
//            }
//        }
//
//        if (unpaidIds != null && !unpaidIds.isEmpty()) {
//            basicQuery.filterBy("id", "IN", unpaidIds);
//            unpaidMaidsVisaList = basicQuery.execute();
//        }
//
//        return unpaidMaidsVisaList;
//    }

    @Deprecated //TODO: REMOVE LATER : replaced and not used
    @Transactional
    public List<HousemaidPayrollLog> generatePayrollLogs(List<Housemaid> housemaids, PayrollAccountantTodo todo, boolean finalFile) {
        List<HousemaidPayrollLog> logs = new ArrayList<>();

        try {
            if (housemaids.isEmpty()) return new ArrayList<>();

            LocalDate payrollEnd = new LocalDate(getPayrollEndLockDate(todo.getMonthlyPaymentRule()));
            LocalDate payrollStart = new LocalDate(getPayrollStartLockDate(todo.getMonthlyPaymentRule()));
            LocalDate payrollMonth = new LocalDate(todo.getPayrollMonth());
            Reflections reflections =
                    new Reflections("com.magnamedia.salarycalculation.v2");
            Set<Class<? extends HousemaidSalaryTransaction>> allClasses =
                    reflections.getSubTypesOf(HousemaidSalaryTransaction.class);

//            DebugHelper.sendMail("<EMAIL>", String.format("Before payroll Initialize for %d housemaids - #Transactions %d - Payroll Start: %s - Payroll End: %s",
//                    housemaids.size(),
//                    allClasses.size(),
//                    DateUtil.formatDateDashed(payrollStart.toDate()),
//                    DateUtil.formatDateDashed(payrollEnd.toDate())
//            ));

            // Delete all simulation data
            Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).recoverSimulationData(todo.getPayrollMonth(), finalFile);

            List<Long> housemaidIds = housemaids.stream().map(BaseEntity::getId).collect(Collectors.toList());

            // Process all housemaids repayments
            prepareHousemaidPayrollInitializer(housemaidIds, payrollStart, payrollEnd, todo.getMonthlyPaymentRule());
//            getHousemaidPayrollInitializer().prepareCurrentMonthRepayments(housemaids, payrollStart.getMonthOfYear() == payrollMonth.getMonthOfYear() ? payrollStart : payrollMonth.withDayOfMonth(1), payrollStart.toDate(), payrollEnd.toDate(), finalFile);

            // process pro-rated salaries
//            DebugHelper.sendMail("<EMAIL>", String.format("Before processing pro-rated salaries for  %d housemaids", housemaids.size()));

            List<PayrollManagerNote> proRatedSalaries = proRatedSalariesService.processProRatedSalaries(housemaids, todo.getMonthlyPaymentRule(), payrollStart, payrollEnd, finalFile, getHousemaidPayrollInitializer());

//            DebugHelper.sendMail("<EMAIL>", String.format("%d housemaids have a pro-rated salaries", proRatedSalaries.size()));


            // Process negative salaries
//            DebugHelper.sendMail("<EMAIL>", String.format("Before processing negative salaries for  %d housemaids", housemaids.size()));

            List<HousemaidPayrollBean> beans = negativeSalariesService.processNegativeSalariesAndDeductionCap(housemaids, todo.getMonthlyPaymentRule(), payrollStart, payrollEnd, finalFile);

//            DebugHelper.sendMail("<EMAIL>", String.format("After payroll Initialize for %d housemaids", housemaids.size()));

            if (finalFile) {
                HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer().prepareHousemaidsInfo(housemaidIds);
            } else {
                getHousemaidPayrollInitializer().prepareVisaInfo(housemaidIds);
            }

            int index = 1;
            int notIncludedIndex = 1;
            for (HousemaidPayrollBean bean : beans) {
                Housemaid housemaid = bean.getHousemaid();
                try {
                    if (finalFile) {
                        bean = housemaidPayrollAuditService.setHousemaidInfo(bean, bean.getHousemaid(), payrollMonth, HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer());
                    }
                    HousemaidPayrollLog log = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).generatePayrollLog(housemaid, bean, todo);
                    log.setHousemaidPayrollBean(bean);

                    //mark the logs that will be included
                    if (todo.getIncludedHousemaids().contains(housemaid))
                        log.setWillBeIncluded(true);

                    int overLapDays = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_EXCLUDING_OVERLAP_MAIDS_DAYS_COUNT));
                    //if Maid Visa and not paid
                    if (HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType()) && !todo.getIncludedHousemaids().contains(housemaid)) {
                        if (todo != null && todo.getMonthlyPaymentRule() != null
                                && todo.getMonthlyPaymentRule().getExcludedMaidsDueToMissingFields().contains(housemaid))
                            log.setPaidOnStatus("Excluded due to her missing details");
                        else if (todo != null && todo.getMonthlyPaymentRule() != null
                                && todo.getMonthlyPaymentRule().getExcludedMaidsDueToOverLap().contains(housemaid))
                            log.setPaidOnStatus("Excluded due to overlap for more than " + overLapDays + " days");
                        else
                            log.setHousemaidUnpaidStatus(HousemaidUnpaidStatus.UNPAID_VISA_PAYMENT);
                        log.setSn("H-" + notIncludedIndex);
                        logs.add(log);
                        notIncludedIndex++;

                    } else if (housemaid.getStatus() == HousemaidStatus.ON_VACATION) {
                        log.setOnVacationDays(getHousemaidPayrollInitializer().getVacationDays(housemaid.getId()));
                        log.setHousemaidUnpaidStatus(HousemaidUnpaidStatus.ON_VACATION);
                        log.setSn("H-" + notIncludedIndex);
                        logs.add(log);
                        notIncludedIndex++;

                    } else if (housemaid.getStatus() == HousemaidStatus.NO_SHOW) {
                        log.setHousemaidUnpaidStatus(HousemaidUnpaidStatus.NO_SHOW);
                        log.setSn("H-" + notIncludedIndex);
                        logs.add(log);
                        notIncludedIndex++;
                    } else if (housemaid.getExcludedFromPayroll()) {
                        log.setHousemaidUnpaidStatus(HousemaidUnpaidStatus.EXCLUDED_FROM_PROFILE);
                        log.setSn("H-" + notIncludedIndex);
                        logs.add(log);
                        notIncludedIndex++;
                    } else if (log.getTotalSalary() > 0 && housemaid.getStatus() != HousemaidStatus.ON_VACATION
                            && housemaid.getStatus() != HousemaidStatus.NO_SHOW && log.getWillBeIncluded() != null && log.getWillBeIncluded()) {
                        log.setSn("H-" + index);
                        logs.add(log);
                        index++;
                    } else if (log.getTotalSalary() > 0 && housemaid.getStatus() != HousemaidStatus.ON_VACATION
                            && housemaid.getStatus() != HousemaidStatus.NO_SHOW) {
                        log.setSn("H-" + notIncludedIndex);
                        if (todo != null && todo.getMonthlyPaymentRule() != null
                                && todo.getMonthlyPaymentRule().getExcludedMaidsDueToMissingFields().contains(housemaid))
                            log.setPaidOnStatus("Excluded due to her missing details");
                        else if (todo != null && todo.getMonthlyPaymentRule() != null
                                && todo.getMonthlyPaymentRule().getExcludedMaidsDueToOverLap().contains(housemaid))
                            log.setPaidOnStatus("Excluded due to overlap for more than " + overLapDays + " days");
                        logs.add(log);
                        notIncludedIndex++;
                    }
                    log.setPaidOnStatus();

                } catch (Exception e) {
                    Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, String.format("Error while generating payroll log for houemaid %s", housemaid.getName()), e);
                    DebugHelper.sendExceptionMail("<EMAIL>", e, String.format("Error while generating payroll log for housemaid %s", housemaid.getName()), true);
                }
            }

//            DebugHelper.sendMail("<EMAIL>", String.format("After processing logs individually for %d housemaids", housemaids.size()));

            //Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).setCancelledContractsMaidVisaSalaryOnHold();
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, String.format("Error while generating housemaids logs for %d housemaids", housemaids.size()), false);
        } finally {
            clearHousemaidPayrollInitializer();
        }
        return logs;
    }




    ///////////////////////////////// NEW LOGIC HERE /////////////////////////////////////////////
    public List<HousemaidPayrollLog> getOrCreatePayrollLogs(List<Housemaid> housemaids, MonthlyPaymentRule monthlyPaymentRule, PayrollAccountantTodo todo, boolean finalFile) {
        List<HousemaidPayrollLog> logs = new ArrayList<>();
        if (housemaids.isEmpty()) return new ArrayList<>();
//        housemaids.sort(Comparator.comparing(Housemaid::getName, String.CASE_INSENSITIVE_ORDER));
        housemaids.sort(Comparator.comparing(
                // Use a null-safe comparator to handle null values
                housemaid -> housemaid.getName() == null ? "" : housemaid.getName().toLowerCase(),
                Comparator.naturalOrder()
        ));

        LocalDate payrollEnd = new LocalDate(getPayrollEndLockDate(monthlyPaymentRule));
        LocalDate payrollStart = new LocalDate(getPayrollStartLockDate(monthlyPaymentRule));
        LocalDate payrollMonth = new LocalDate(monthlyPaymentRule.getPayrollMonth());
        Date previousPayrollMonth = new Date(DateUtil.addMonths(monthlyPaymentRule.getPayrollMonth(), -1).getTime());
        LocalDate previousPayrollStart = new LocalDate(getPayrollStartLockDateOfPayrollMonth(previousPayrollMonth));
        Integer index = 1;
        Integer notIncludedIndex = 1;

        Set<Long> excludedMaidsDueToMissingFields = monthlyPaymentRuleRepository.getExcludedMaidsDueToMissingFields(monthlyPaymentRule);
        Set<Long> excludedMaidsDueToOverLap = monthlyPaymentRuleRepository.getExcludedMaidsDueToOverLap(monthlyPaymentRule);
        Set<Long> excludedDueToMedicalTest = monthlyPaymentRuleRepository.getExcludedDueToMedicalTest(monthlyPaymentRule);

//        DebugHelper.sendMail("<EMAIL>", String.format("Before payroll Initialize for %d housemaids - Payroll Start: %s - Payroll End: %s", housemaids.size(), DateUtil.formatDateDashed(payrollStart.toDate()), DateUtil.formatDateDashed(payrollEnd.toDate())));

        int overLapDays = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_EXCLUDING_OVERLAP_MAIDS_DAYS_COUNT));
        //generate for every housemaid
        List<MonthlyPaymentRule> listOfRelatedRules = monthlyPaymentRuleRepository.findByPayrollMonthAndEmployeeTypeAndPayrollTypeAndLockDate(monthlyPaymentRule.getPayrollMonth(), PaymentRuleEmployeeType.HOUSEMAIDS, monthlyPaymentRule.getPayrollType(), monthlyPaymentRule.getLockDate());
        for(Housemaid housemaid : housemaids) {
            HousemaidPayrollLog log;
            if(monthlyPaymentRule.isSecondaryMonthlyRule())
                log = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).generateOneHousemaidLogBasedOnAllForSecondary(housemaid, payrollMonth, monthlyPaymentRule, null, todo, finalFile, false, listOfRelatedRules, payrollEnd, payrollStart, previousPayrollStart, excludedMaidsDueToMissingFields, excludedMaidsDueToOverLap, excludedDueToMedicalTest);
            else
                log = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).generateOneHousemaidLogBasedOnAllForPrimary(housemaid, payrollMonth, monthlyPaymentRule, null, todo, finalFile, false, listOfRelatedRules, payrollEnd, payrollStart, previousPayrollStart, excludedMaidsDueToMissingFields, excludedMaidsDueToOverLap, excludedDueToMedicalTest);
            if(log != null) {
                if (todo.getIncludedHousemaids().contains(housemaid) && log.getTotalSalary() > 0)
                    log.setWillBeIncluded(true);

                //if Maid Visa and not paid
                if (HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType()) && !todo.getIncludedHousemaids().contains(housemaid)) {
                    if (monthlyPaymentRule != null
                            && excludedMaidsDueToMissingFields.contains(housemaid.getId()))
                        log.setPaidOnStatus("Excluded due to her missing details");
                    else if (monthlyPaymentRule != null
                            && excludedMaidsDueToOverLap.contains(housemaid.getId()))
                        log.setPaidOnStatus("Excluded due to overlap for more than " + overLapDays + " days");
//                    else if (monthlyPaymentRule != null
//                            && monthlyPaymentRule.getExcludedMaidDueToEVisaIssue().contains(housemaid))
//                        log.setPaidOnStatus("Excluded due to E-Visa not issued or Issued in previous/current month");
                    else if (monthlyPaymentRule != null && HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType())
                            && excludedDueToMedicalTest.contains(housemaid.getId())) {
                        log.setPaidOnStatus("Excluded due to medical test is not done");
                        log.setHousemaidUnpaidStatus(HousemaidUnpaidStatus.UNPAID_VISA_PAYMENT);
                    } else if(housemaid.getExcludedFromPayroll()) {
                        log.setHousemaidUnpaidStatus(HousemaidUnpaidStatus.UNPAID_VISA_PAYMENT);
                        log.setPaidOnStatus("Manually Excluded / No expected release date. Client's payment has not been received.");
                    } else
                        log.setHousemaidUnpaidStatus(HousemaidUnpaidStatus.UNPAID_VISA_PAYMENT);
                    log.setSn("H-" + notIncludedIndex);
                    logs.add(log);
                    notIncludedIndex++;

                } else if (housemaid.getStatus() == HousemaidStatus.ON_VACATION) {
                    log.setHousemaidUnpaidStatus(HousemaidUnpaidStatus.ON_VACATION);
                    log.setSn("H-" + notIncludedIndex);
                    logs.add(log);
                    notIncludedIndex++;

                } else if (housemaid.getStatus() == HousemaidStatus.NO_SHOW && !housemaid.getHousemaidType().equals(HousemaidType.MAID_VISA)) {
                    log.setHousemaidUnpaidStatus(HousemaidUnpaidStatus.NO_SHOW);
                    log.setSn("H-" + notIncludedIndex);
                    logs.add(log);
                    notIncludedIndex++;
                } else if (housemaid.getExcludedFromPayroll()) {
                    log.setHousemaidUnpaidStatus(HousemaidUnpaidStatus.EXCLUDED_FROM_PROFILE);
                    log.setSn("H-" + notIncludedIndex);
                    logs.add(log);
                    notIncludedIndex++;
                } else if (log.getTotalSalary() > 0 && log.getWillBeIncluded() != null && log.getWillBeIncluded()) {
                    log.setSn("H-" + index);
                    logs.add(log);
                    index++;
                } else {
                    log.setSn("H-" + notIncludedIndex);
                    if (monthlyPaymentRule != null && excludedMaidsDueToMissingFields.contains(housemaid.getId()))
                        log.setPaidOnStatus("Excluded due to her missing details");
                    else if (monthlyPaymentRule != null && excludedMaidsDueToOverLap.contains(housemaid.getId()))
                        log.setPaidOnStatus("Excluded due to overlap for more than " + overLapDays + " days");
                    logs.add(log);
                    notIncludedIndex++;
                }
                Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).makeHousemaidPayrollLogFinal(housemaid, log);

                if (log.getPaidOnStatus() == null || log.getPaidOnStatus().isEmpty())
                    log.setPaidOnStatus();

                log.setLogStatus(HousemaidPayrollLog.HousemaidPayrollLogStatus.FINAL);
                log = housemaidPayrollLogRepository.silentSave(log);
                logs.add(log);

//                if (monthlyPaymentRule != null
//                        && monthlyPaymentRule.getExcludedMaidDueToEVisaIssue().contains(housemaid)) {
//                    recoverSimulationDataBasedOnAll(housemaid.getId(), monthlyPaymentRule, null, null, monthlyPaymentRule.getPayrollMonth());
//                }
            }
        }

//        DebugHelper.sendMail("<EMAIL>", String.format("after processing salaries for %d housemaids", housemaids.size()));
        return logs;
    }

    @Transactional
    public void makeHousemaidPayrollLogFinal(Housemaid housemaid, HousemaidPayrollLog log){
        PayrollManagerNoteRepository payrollManagerNoteRepository = Setup.getRepository(PayrollManagerNoteRepository.class);
        RepaymentRepository repaymentRepository = Setup.getRepository(RepaymentRepository.class);
        EmployeeLoanRepository employeeLoanRepository = Setup.getRepository(EmployeeLoanRepository.class);
        UnpaidDeductionRepository unpaidDeductionRepository = Setup.getRepository(UnpaidDeductionRepository.class);
        UnpaidDeductionRepaymentRepository unpaidDeductionRepaymentRepository = Setup.getRepository(UnpaidDeductionRepaymentRepository.class);
        HousemaidBalancesHistoryRepository housemaidBalancesHistoryRepository = Setup.getRepository(HousemaidBalancesHistoryRepository.class);
        ExcludedMaidSalaryRepository excludedMaidSalaryRepository = Setup.getRepository(ExcludedMaidSalaryRepository.class);
        HousemaidPayrollBeanRepository housemaidPayrollBeanRepository = Setup.getRepository(HousemaidPayrollBeanRepository.class);

        payrollManagerNoteRepository.updateNotFinalNotesByHousemaid(housemaid);
//        List<PayrollManagerNote> notes = payrollManagerNoteRepository.findByNotFinalAndHousemaid(true, housemaid);
//        for (PayrollManagerNote note : notes){
//            note.setNotFinal(false);
//            payrollManagerNoteRepository.save(note);
//        }

        repaymentRepository.updateNotFinalNotesByHousemaid(housemaid);
//        List<Repayment> repayments = repaymentRepository.findByNotFinalAndHousemaid(true, housemaid);
//        for (Repayment repayment : repayments) {
//            repayment.setNotFinal(false);
//            repaymentRepository.save(repayment);
//        }

        employeeLoanRepository.updateNotFinalNotesByHousemaid(housemaid);
//        List<EmployeeLoan> loans = employeeLoanRepository.findByNotFinalAndHousemaid(true, housemaid);
//        for (EmployeeLoan loan : loans) {
//            loan.setNotFinal(false);
//            employeeLoanRepository.save(loan);
//        }

        unpaidDeductionRepository.updateNotFinalNotesByHousemaid(housemaid);
//        List<UnpaidDeduction> unpaidDeductions = unpaidDeductionRepository.findByNotFinalAndHousemaid(true, housemaid);
//        for (UnpaidDeduction unpaidDeduction : unpaidDeductions) {
//            unpaidDeduction.setNotFinal(false);
//            unpaidDeductionRepository.save(unpaidDeduction);
//        }

        unpaidDeductionRepaymentRepository.updateNotFinalNotesByHousemaid(housemaid);
//        List<UnpaidDeductionRepayment> unpaidDeductionRepayments = unpaidDeductionRepaymentRepository.findByNotFinalAndHousemaid(true, housemaid);
//        for (UnpaidDeductionRepayment unpaidDeductionRepayment : unpaidDeductionRepayments) {
//            unpaidDeductionRepayment.setNotFinal(false);
//            unpaidDeductionRepaymentRepository.save(unpaidDeductionRepayment);
//        }

        housemaidBalancesHistoryRepository.updateNotFinalNotesByHousemaid(housemaid);
//        List<HousemaidBalancesHistory> housemaidBalancesHistories = housemaidBalancesHistoryRepository.findByNotFinalAndHousemaid(true, housemaid);
//        for (HousemaidBalancesHistory h : housemaidBalancesHistories) {
//            h.setNotFinal(false);
//            housemaidBalancesHistoryRepository.save(h);
//        }

        Setup.getRepository(HousemaidPayrollMonthlyGroupRepository.class).updateNotFinalNotesByHousemaid(housemaid);


        HousemaidPayrollBean bean = log.getHousemaidPayrollBean();
        bean.setNotFinal(false);
        bean = housemaidPayrollBeanRepository.silentSave(bean);
        log.setHousemaidPayrollBean(bean);

    }


    // used as BGT
    @Transactional
    public void generatePayrollLogsBasedOnAllForBatch(List<String> ids, Long monthlyPaymentRuleId, Long payrollAuditTodoId, Long payrollAccountantTodoId, Boolean finalFile, Boolean toSave, Date payrollEnd, Date payrollStart, Date previousPayrollStart) {
        Set<Long> housemaids = ids.stream().map(Long::parseLong).collect(Collectors.toSet());
        MonthlyPaymentRule monthlyPaymentRule = monthlyPaymentRuleRepository.findOne(monthlyPaymentRuleId);
        PayrollAuditTodo payrollAuditTodo = payrollAuditTodoId != null? payrollAuditTodoRepository.findOne(payrollAuditTodoId) : null;
        PayrollAccountantTodo payrollAccountantTodo = null;
        if (payrollAccountantTodoId != null) {
            payrollAccountantTodo = Setup.getRepository(PayrollAccountantTodoRepository.class).findOne(payrollAccountantTodoId);
        }
        generatePayrollLogsBasedOnAll(housemaids, monthlyPaymentRule, payrollAuditTodo, payrollAccountantTodo, finalFile, toSave, new LocalDate(payrollEnd), new LocalDate(payrollStart), new LocalDate(previousPayrollStart));
        // validate finished all BGT
        SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);
        query.filterBy("name", "like", "generatePayrollLogsBasedOnAllForBatch#" + monthlyPaymentRuleId + "%");
        query.filterBy("status", "NOT IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
        List<BackgroundTask> activeTask = query.execute();
        // This BGT is the last in the set
        if (activeTask == null || activeTask.size() <= 5) {
            if (payrollAuditTodo != null && !Boolean.TRUE.equals(payrollAuditTodo.getFinishedGeneratingLogs())) {
                BackgroundTask backgroundTask = new BackgroundTask.builder("auditFinishedGeneratingLogs#" + payrollAuditTodo.getId(),
                        Setup.getCurrentModule().getCode(), "housemaidPayrollPaymentServiceV2",
                        "auditFinishedGeneratingLogs")
                        .withQueue(BackgroundTaskQueues.SequentialQueue)
                        .withParameters(new Class<?>[]{Long.class}, monthlyPaymentRuleId)
                        .withDelay((long) (1000*60*(1+Math.random())))
                        .build();
                Setup.getApplicationContext().getBean(BackgroundTaskService.class).create(backgroundTask);

            }
        }
    }

    @Transactional
    public Boolean auditFinishedGeneratingLogs(Long monthlyPaymentRuleId) {
        MonthlyPaymentRule monthlyPaymentRule = monthlyPaymentRuleRepository.findOne(monthlyPaymentRuleId);
        if (monthlyPaymentRule != null && !Boolean.TRUE.equals(monthlyPaymentRule.getFinishedGeneratingLogs())) {
            SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);
            query.filterBy("name", "like", "generatePayrollLogsBasedOnAllForBatch#" + monthlyPaymentRuleId + "%");
            query.filterBy("status", "NOT IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
            List<BackgroundTask> activeTask = query.execute();
            if (activeTask == null || activeTask.isEmpty()) {
                monthlyPaymentRule.setFinishedGeneratingLogs(true);
                monthlyPaymentRuleRepository.save(monthlyPaymentRule);
                // insert a new background task to send Initial Payroll Files
                BackgroundTask backgroundTask = new BackgroundTask.builder("sendInitialPayrollFilesRevamp",
                        Setup.getCurrentModule().getCode(),
                        "payrollAuditTodoService",
                        "sendInitialPayrollFilesRevamp")
                        .withDelay((long) (1000*60*(Math.random())))
                        .withParameters(new Class<?>[]{Long.class}, monthlyPaymentRule.getId()).build();

                Setup.getApplicationContext().getBean(BackgroundTaskService.class).create(backgroundTask);

                return true;
            }
        }
        return false;
    }


    public void generatePayrollLogsBasedOnAll(Set<Long> housemaids, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, boolean finalFile, boolean toSave, LocalDate payrollEnd, LocalDate payrollStart, LocalDate previousPayrollStart) {
        List<MonthlyPaymentRule> listOfRelatedRules = monthlyPaymentRuleRepository.findByPayrollMonthAndEmployeeTypeAndPayrollTypeAndLockDate(monthlyPaymentRule.getPayrollMonth(), PaymentRuleEmployeeType.HOUSEMAIDS, monthlyPaymentRule.getPayrollType(), monthlyPaymentRule.getLockDate());
        List<HousemaidPayrollLog> logs = new ArrayList<>();
        if (housemaids.isEmpty()) return;

        LocalDate payrollMonth = new LocalDate(monthlyPaymentRule.getPayrollMonth());

        Set<Long> excludedMaidsDueToMissingFields = monthlyPaymentRuleRepository.getExcludedMaidsDueToMissingFields(monthlyPaymentRule);
        Set<Long> excludedMaidsDueToOverLap = monthlyPaymentRuleRepository.getExcludedMaidsDueToOverLap(monthlyPaymentRule);
        Set<Long> excludedDueToMedicalTest = monthlyPaymentRuleRepository.getExcludedDueToMedicalTest(monthlyPaymentRule);

        //generate for every housemaid
        HousemaidRepository housemaidRepository = Setup.getRepository(HousemaidRepository.class);
        Housemaid housemaid = null;
        for(Long housemaid_id : housemaids) {
            housemaid = housemaidRepository.findOne(housemaid_id);
            HousemaidPayrollLog log;
            if(monthlyPaymentRule.isSecondaryMonthlyRule())
                log = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).generateOneHousemaidLogBasedOnAllForSecondary(housemaid, payrollMonth, monthlyPaymentRule, auditTodo, todo, finalFile, toSave, listOfRelatedRules, payrollEnd, payrollStart, previousPayrollStart, excludedMaidsDueToMissingFields, excludedMaidsDueToOverLap, excludedDueToMedicalTest);
            else
                log = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).generateOneHousemaidLogBasedOnAllForPrimary(housemaid, payrollMonth, monthlyPaymentRule, auditTodo, todo, finalFile, toSave, listOfRelatedRules, payrollEnd, payrollStart, previousPayrollStart, excludedMaidsDueToMissingFields, excludedMaidsDueToOverLap, excludedDueToMedicalTest);
            if(log != null) {
                logs.add(log);
            }
        }

//        if(auditTodo != null){
//            auditTodo = Setup.getRepository(PayrollAuditTodoRepository.class).getOne(auditTodo.getId());
//            auditTodo.setFinishedGeneratingLogs(true);
//            Setup.getRepository(PayrollAuditTodoRepository.class).save(auditTodo);
//        }

//        DebugHelper.sendMail("<EMAIL>", String.format("Finished generatePayrollLogsBasedOnAll for %d housemaids - Payroll Start: %s - Payroll End: %s",housemaids.size(),DateUtil.formatDateDashed(payrollStart.toDate()),DateUtil.formatDateDashed(payrollEnd.toDate())));
    }

    @Async
    @Transactional
    public void generatePayrollLogsBasedOnAllAsync(List<Housemaid> housemaids, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, boolean finalFile, boolean toSave) {
        Set<Long> housemaidsIds = housemaids.stream().map(BaseEntity::getId).collect(Collectors.toSet());
        List<String> housemaidsList = housemaidsIds.stream().map(Object::toString).collect(Collectors.toList());
        int chunkSize = 50;
        try {
            chunkSize = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_GENERATION_CHUNK_SIZE));
        } catch (Exception ignore) {
        }
        List<List<String>> batches = ListHelper.divideList(housemaidsList, chunkSize);
        int i = 0;
        Date payrollEnd = (getPayrollEndLockDate(monthlyPaymentRule));
        Date payrollStart = (getPayrollStartLockDate(monthlyPaymentRule));
        Date previousPayrollMonth = new Date(DateUtil.addMonths(monthlyPaymentRule.getPayrollMonth(), -1).getTime());
        Date previousPayrollStart = (getPayrollStartLockDateOfPayrollMonth(previousPayrollMonth));
        for (List<String> batch : batches) {
            i++;
            BackgroundTask backgroundTask = new BackgroundTask.builder("generatePayrollLogsBasedOnAllForBatch#" + monthlyPaymentRule.getId() + " batch: " + i,
                    Setup.getCurrentModule().getCode(), "housemaidPayrollPaymentServiceV2",
                    "generatePayrollLogsBasedOnAllForBatch")
                    .withQueue(BackgroundTaskQueues.HeavyOperationsQueue)
                    .withParameters(new Class<?>[]{List.class, Long.class, Long.class, Long.class, Boolean.class, Boolean.class, Date.class, Date.class, Date.class}
                            , batch, monthlyPaymentRule.getId(), auditTodo != null ? auditTodo.getId() : null, todo != null ? todo.getId() : null, finalFile, toSave, payrollEnd, payrollStart, previousPayrollStart)
                    .build();
            Setup.getApplicationContext().getBean(BackgroundTaskService.class).create(backgroundTask);
        }
    }

    @Transactional
    public HousemaidPayrollLog generateOneHousemaidLogBasedOnAllForPrimary(Housemaid housemaid, LocalDate payrollMonth, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, boolean finalFile, boolean toSave,  List<MonthlyPaymentRule> listOfRelatedRules, LocalDate payrollEnd, LocalDate payrollStart, LocalDate previousPayrollStart, Set<Long> excludedMaidsDueToMissingFields, Set<Long> excludedMaidsDueToOverLap, Set<Long> excludedDueToMedicalTest) {
        int overLapDays = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_EXCLUDING_OVERLAP_MAIDS_DAYS_COUNT));

        HousemaidPayrollLog log = null;
        try {
            SelectQuery<HousemaidPayrollLog> selectQuery = new SelectQuery<>(HousemaidPayrollLog.class);
            selectQuery.filterBy("housemaid", "=", housemaid);
            selectQuery.filterBy("monthlyPaymentRule", "IN", listOfRelatedRules);
            if(auditTodo != null && auditTodo.getId() != null)
                selectQuery.filterBy("payrollAuditTodo", "=", auditTodo);
//            if(todo != null && todo.getId() != null)
//                selectQuery.filterBy("payrollAccountantTodo", "=", todo);
            selectQuery.filterBy("payrollMonth", "=", new Date(payrollMonth.toDate().getTime()));
            selectQuery.sortBy("id", false);
            selectQuery.setLimit(1);
            List<HousemaidPayrollLog> logs = selectQuery.execute();

            log = logs != null && logs.size() > 0 ? logs.get(0) : null;
            if (log == null) {

                //1- run transactions
                HousemaidPayrollBean bean = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).runTransactionsNew(housemaid, monthlyPaymentRule, finalFile, payrollEnd, payrollStart, previousPayrollStart);

                //2- housemaid info
                bean = payrollGenerationHelperService.setHousemaidInfo(bean, housemaid, payrollMonth, finalFile);

                //3- saving the bean
                bean = housemaidPayrollBeanRepository.save(bean);

                //4- create the payroll log and save it
                log = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).createPayrollLogBasedOnAll(housemaid, bean, monthlyPaymentRule, auditTodo, todo);
                log.setLogStatus(HousemaidPayrollLog.HousemaidPayrollLogStatus.PENDING);
                log.setHousemaidPayrollBean(bean);

                if (monthlyPaymentRule != null && excludedMaidsDueToMissingFields.contains(housemaid.getId()))
                    log.setPaidOnStatus("Excluded due to her missing details");
                else if (monthlyPaymentRule != null && excludedMaidsDueToOverLap.contains(housemaid.getId()))
                    log.setPaidOnStatus("Excluded due to overlap for more than " + overLapDays + " days");
//                else if (monthlyPaymentRule != null && HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType())
//                        && monthlyPaymentRule.getExcludedMaidDueToEVisaIssue().contains(housemaid))
//                    log.setPaidOnStatus("Excluded due to E-Visa not issued or Issued in previous/current month");
                else if (monthlyPaymentRule != null && HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType())
                        && excludedDueToMedicalTest.contains(housemaid.getId()))
                    log.setPaidOnStatus("Excluded due to medical test is not done");

                if (log.getPaidOnStatus() == null || log.getPaidOnStatus().isEmpty())
                    log.setPaidOnStatus();

                if(toSave){
                    log = housemaidPayrollLogRepository.silentSave(log);

                }
            }else {
                if(monthlyPaymentRule != null)
                    log.setMonthlyPaymentRule(monthlyPaymentRule);
                if(auditTodo != null)
                    log.setPayrollAuditTodo(auditTodo);
                if(todo != null)
                    log.setPayrollAccountantTodo(todo);
                if(toSave)
                    log = housemaidPayrollLogRepository.save(log);
            }
        }catch (Exception ex){
            Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, String.format("Error while generating payroll log for %s", housemaid.getName()), ex);
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Exception while generateOneHousemaidLogBasedOnAllForPrimary for " + housemaid.getName(), false);
            throw ex;
        }

        return log;
    }

    @Transactional
    public HousemaidPayrollLog generateOneHousemaidLogBasedOnAllForSecondary(Housemaid housemaid, LocalDate payrollMonth, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, boolean finalFile, boolean toSave,  List<MonthlyPaymentRule> listOfRelatedRules, LocalDate payrollEnd, LocalDate payrollStart, LocalDate previousPayrollStart, Set<Long> excludedMaidsDueToMissingFields, Set<Long> excludedMaidsDueToOverLap, Set<Long> excludedDueToMedicalTest) {
        int overLapDays = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_EXCLUDING_OVERLAP_MAIDS_DAYS_COUNT));

        HousemaidPayrollLog log = null;
        try {
            SelectQuery<HousemaidPayrollLog> selectQuery = new SelectQuery<>(HousemaidPayrollLog.class);
            selectQuery.filterBy("housemaid", "=", housemaid);
            selectQuery.filterBy("monthlyPaymentRule", "IN", listOfRelatedRules);
            if(auditTodo != null && auditTodo.getId() != null)
                selectQuery.filterBy("payrollAuditTodo", "=", auditTodo);
//            if(todo != null && todo.getId() != null)
//                selectQuery.filterBy("payrollAccountantTodo", "=", todo);
            selectQuery.filterBy("payrollMonth", "=", new Date(payrollMonth.toDate().getTime()));
            selectQuery.sortBy("id", false);
            selectQuery.setLimit(1);
            List<HousemaidPayrollLog> logs = selectQuery.execute();

            log = logs != null && logs.size() > 0 ? logs.get(0) : null;
            if (log == null) {
                HousemaidPayrollBean targetBean;
                //1- run transactions
                HousemaidPayrollBean bean = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).runTransactionsNew(housemaid, monthlyPaymentRule, finalFile, payrollEnd, payrollStart, previousPayrollStart);

                //2- housemaid info : check if has paid primary Salary or not
                // has paid salary --> so use only new bean and new log
                if (payrollGenerationHelperService.hasPaidSalary(housemaid, monthlyPaymentRule.getPayrollMonth())) {
                    targetBean = bean;
                    targetBean = payrollGenerationHelperService.setHousemaidInfo(targetBean, housemaid, payrollMonth, finalFile);
                    log = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).createPayrollLogBasedOnAll(housemaid, targetBean, monthlyPaymentRule, auditTodo, todo);
                } else { // primary payroll wasn't paid so use it and add all changes from the new bean
                    HousemaidPayrollLog oldLog = Setup.getRepository(HousemaidPayrollLogRepository.class).findTopByHousemaidAndPayrollMonthAndMonthlyPaymentRuleNot(housemaid, monthlyPaymentRule.getPayrollMonth(), monthlyPaymentRule);
                    if (oldLog == null)
                        return null;
                    HousemaidPayrollBean oldBean = Setup.getRepository(HousemaidPayrollBeanRepository.class).findOne(oldLog.getHousemaidPayrollBean().getId());
                    targetBean = Setup.getApplicationContext().getBean(BaseControllerHelper.class).copyFromAnother(oldBean);
                    log = new HousemaidPayrollLog(oldLog);
                    log.setManagerAdditions(log.getManagerAdditions() + log.valueOrZero(bean.getManagerAddition()));
                    log.setTotalAddition(log.getTotalAddition() + bean.getTotatIcome());
                    log.setTotalSalary(log.getTotalSalary() + log.valueOrZero(bean.getBalance()));
                    targetBean = payrollGenerationHelperService.setHousemaidInfo(targetBean, housemaid, payrollMonth, finalFile);

                    double managerAddition = targetBean.getManagerAddition();
                    double totatIcom = targetBean.getTotatIcome();
                    double totalBalance = targetBean.getTotalBalance();
                    targetBean.setManagerAddition(managerAddition + bean.getManagerAddition());
                    targetBean.setTotatIcome(totatIcom + bean.getTotatIcome());
                    targetBean.setTotalBalance(totalBalance + bean.getTotalBalance());

                }

                //3- saving with cases
                targetBean = housemaidPayrollBeanRepository.save(targetBean);
                log.setLogStatus(HousemaidPayrollLog.HousemaidPayrollLogStatus.PENDING);
                log.setHousemaidPayrollBean(targetBean);
                if (monthlyPaymentRule != null && excludedMaidsDueToMissingFields.contains(housemaid.getId()))
                    log.setPaidOnStatus("Excluded due to her missing details");
                else if (monthlyPaymentRule != null && excludedMaidsDueToOverLap.contains(housemaid.getId()))
                    log.setPaidOnStatus("Excluded due to overlap for more than " + overLapDays + " days");
//                else if (monthlyPaymentRule != null && HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType())
//                        && monthlyPaymentRule.getExcludedMaidDueToEVisaIssue().contains(housemaid))
//                    log.setPaidOnStatus("Excluded due to E-Visa not issued or Issued in previous/current month");
                else if (monthlyPaymentRule != null && HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType())
                        && excludedDueToMedicalTest.contains(housemaid.getId()))
                    log.setPaidOnStatus("Excluded due to medical test is not done");

                if (log.getPaidOnStatus() == null || log.getPaidOnStatus().isEmpty())
                    log.setPaidOnStatus();
            }

            if(monthlyPaymentRule != null)
                log.setMonthlyPaymentRule(monthlyPaymentRule);
            if(auditTodo != null)
                log.setPayrollAuditTodo(auditTodo);
            if(todo != null)
                log.setPayrollAccountantTodo(todo);
            if(toSave)
                housemaidPayrollLogRepository.save(log);
        }catch (Exception ex){
            Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, String.format("Error while generating payroll log for %s", housemaid.getName()), ex);
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Exception while generateOneHousemaidLogBasedOnAllForSecondary for " + housemaid.getName(), false);
            throw ex;
        }

        return log;
    }

    public List<HousemaidPayrollLog> getHousemaidPayrollLogsBasedOnAll(List<Housemaid> housemaids, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo){
        if(housemaids.size() == 0)
            return new ArrayList<>();
        List<MonthlyPaymentRule> listOfRelatedRules = monthlyPaymentRuleRepository.findByPayrollMonthAndEmployeeTypeAndPayrollTypeAndLockDate(monthlyPaymentRule.getPayrollMonth(), PaymentRuleEmployeeType.HOUSEMAIDS, monthlyPaymentRule.getPayrollType(), monthlyPaymentRule.getLockDate());

        SelectQuery<HousemaidPayrollLog> selectQuery = new SelectQuery<>(HousemaidPayrollLog.class);
        selectQuery.filterBy("housemaid", "IN", housemaids);
        selectQuery.filterBy("monthlyPaymentRule", "IN", listOfRelatedRules);
        if(auditTodo != null && auditTodo.getId()!= null)
            selectQuery.filterBy("payrollAuditTodo", "=", auditTodo);
//        if(todo != null && todo.getId()!= null)
//            selectQuery.filterBy("payrollAccountantTodo", "=", todo);
        selectQuery.filterBy("payrollMonth", "=", monthlyPaymentRule.getPayrollMonth());
        selectQuery.sortBy("housemaid.name", true);

        return selectQuery.execute();
    }

    public Set<Long> getChangedHousemaidsBasedOnAll(List<Housemaid> housemaids, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, Boolean attendanceLogOnly, List<MonthlyPaymentRule> listOfRelatedRules){

        HousemaidRepository housemaidRepository = Setup.getRepository(HousemaidRepository.class);
        if (attendanceLogOnly != null && attendanceLogOnly) {
            return new HashSet<Long>(housemaidRepository.getChangedHousemaidGroups(housemaids, listOfRelatedRules, auditTodo, monthlyPaymentRule.getPayrollMonth()));
        }
        return new HashSet<Long>(housemaidRepository.getChangedHousemaids(housemaids, listOfRelatedRules, auditTodo));
    }

    public void clearDataForMaids(List<Housemaid> changedHousemaids, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo,  List<MonthlyPaymentRule> listOfRelatedRules) {
        for (Housemaid housemaid : changedHousemaids) {
            Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).recoverSimulationDataBasedOnAll(housemaid.getId(), monthlyPaymentRule, auditTodo, todo, monthlyPaymentRule.getPayrollMonth(), listOfRelatedRules);
        }
    }

    public void clearDataForMaidsBasedOnId(Set<Long> changedHousemaids, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, List<MonthlyPaymentRule> listOfRelatedRules){
        for(Long housemaid_id : changedHousemaids){
            Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).recoverSimulationDataBasedOnAll(housemaid_id, monthlyPaymentRule, auditTodo, todo, monthlyPaymentRule.getPayrollMonth(), listOfRelatedRules);
        }
    }

    public void clearHousemaidLogsForChangedAndRecreate(List<Housemaid> housemaids, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, Boolean finalFile, Boolean toSave){
        try {
            List<Housemaid> targetHousemaids = new ArrayList<>(housemaids);
            if(todo != null  && todo.getId() != null) {
                List<Long> alreadyHaveGeneratedLog = Setup.getRepository(HousemaidPayrollLogRepository.class).findDistinctHousemaidIdByPayrollAccountantTodoAndTransferredFalseAndPayrollMonthAndLogStatus(todo, monthlyPaymentRule.getPayrollMonth(), HousemaidPayrollLog.HousemaidPayrollLogStatus.FINAL);
                targetHousemaids = targetHousemaids.stream().filter(x -> !alreadyHaveGeneratedLog.contains(x.getId())).collect(Collectors.toList());
            }
            List<MonthlyPaymentRule> listOfRelatedRules = monthlyPaymentRuleRepository.findByPayrollMonthAndEmployeeTypeAndPayrollTypeAndLockDate(monthlyPaymentRule.getPayrollMonth(), PaymentRuleEmployeeType.HOUSEMAIDS, monthlyPaymentRule.getPayrollType(), monthlyPaymentRule.getLockDate());
            Set<Long> changedHousemaids = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getChangedHousemaidsBasedOnAll(targetHousemaids, monthlyPaymentRule, auditTodo, todo, false, listOfRelatedRules);
//            DebugHelper.sendMail("<EMAIL>", "clearHousemaidLogsForChangedAndRecreate --> changedMaids : " + changedHousemaids.stream().map(BaseEntity::getId).collect(Collectors.toList()));
            if(changedHousemaids.size() > 0) {
                LocalDate payrollEnd = new LocalDate(getPayrollEndLockDate(monthlyPaymentRule));
                LocalDate payrollStart = new LocalDate(getPayrollStartLockDate(monthlyPaymentRule));
                Date previousPayrollMonth = new Date(DateUtil.addMonths(monthlyPaymentRule.getPayrollMonth(), -1).getTime());
                LocalDate previousPayrollStart = new LocalDate(getPayrollStartLockDateOfPayrollMonth(previousPayrollMonth));
                Set<Long> changedHousemaidGroups = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getChangedHousemaidsBasedOnAll(targetHousemaids, monthlyPaymentRule, auditTodo, todo, true, listOfRelatedRules);
                Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).regeneratePayrollGroupForMaidList(changedHousemaidGroups, monthlyPaymentRule.getPayrollMonth(), finalFile);
                Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).clearDataForMaidsBasedOnId(changedHousemaids, monthlyPaymentRule, auditTodo, todo, listOfRelatedRules);
                Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).generatePayrollLogsBasedOnAll(changedHousemaids, monthlyPaymentRule, auditTodo, todo, finalFile, toSave, payrollEnd, payrollStart, previousPayrollStart);
            }
        }catch (Exception e){
            Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, "Error while clearHousemaidLogsForChangedAndRecreate ", e);
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception while clearHousemaidLogsForChangedAndRecreate ", false);
            throw e;
        }
    }

    @Transactional
    public void regeneratePayrollGroupForMaidList(Set<Long> changedHousemaidGroups, Date payrollMonth, Boolean finalFile) {
        for (Long id : changedHousemaidGroups) {
            Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(id);
            HousemaidPayrollMonthlyGroupRepository payrollMonthlyGroupRepository = Setup.getRepository(HousemaidPayrollMonthlyGroupRepository.class);
            payrollMonthlyGroupRepository.deleteByHousemaidAndPayrollMonth(housemaid, payrollMonth);
            Setup.getApplicationContext().getBean(PayrollGroupService.class).createPayrollMonthlyGroup(housemaid, payrollMonth, null, null, true, finalFile);
        }
    }

    @Transactional
    public List<HousemaidPayrollLog> generatePayrollLogsForSecondary(List<Housemaid> housemaids, PayrollAccountantTodo todo, boolean finalFile) {
        List<HousemaidPayrollLog> logs = new ArrayList<>();

        try {
            if (housemaids.isEmpty()) return new ArrayList<>();

            LocalDate payrollEnd = new LocalDate(getPayrollEndLockDate(todo.getMonthlyPaymentRule()));
            LocalDate payrollStart = new LocalDate(getPayrollStartLockDate(todo.getMonthlyPaymentRule()));
            LocalDate payrollMonth = new LocalDate(todo.getPayrollMonth());
            Reflections reflections =
                    new Reflections("com.magnamedia.salarycalculation.v2");
            Set<Class<? extends HousemaidSalaryTransaction>> allClasses =
                    reflections.getSubTypesOf(HousemaidSalaryTransaction.class);

//            DebugHelper.sendMail("<EMAIL>", String.format("Before secondary payroll Initialize for %d housemaids - #Transactions %d - Payroll Start: %s - Payroll End: %s",
//                    housemaids.size(),
//                    allClasses.size(),
//                    DateUtil.formatDateDashed(payrollStart.toDate()),
//                    DateUtil.formatDateDashed(payrollEnd.toDate())
//            ));

            // Delete all simulation data
            Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).recoverSimulationData(todo.getPayrollMonth(), finalFile);

            List<Long> housemaidIds = housemaids.stream().map(BaseEntity::getId).collect(Collectors.toList());

            // Process all housemaids repayments
            prepareHousemaidPayrollInitializer(housemaidIds, payrollStart, payrollEnd, todo.getMonthlyPaymentRule());

            // process pro-rated salaries
//            DebugHelper.sendMail("<EMAIL>", String.format("Secondary Before processing pro-rated salaries for  %d housemaids", housemaids.size()));

            List<PayrollManagerNote> proRatedSalaries = proRatedSalariesService.processProRatedSalaries(housemaids, todo.getMonthlyPaymentRule(), payrollStart, payrollEnd, finalFile, getHousemaidPayrollInitializer());

//            DebugHelper.sendMail("<EMAIL>", String.format("Secondary %d housemaids have a pro-rated salaries", proRatedSalaries.size()));

            // Process negative salaries
//            DebugHelper.sendMail("<EMAIL>", String.format("Secondary Before processing negative salaries for  %d housemaids", housemaids.size()));

            List<HousemaidPayrollBean> beans = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).processSalariesOfSecondaryPayroll(housemaids, todo.getMonthlyPaymentRule(), payrollStart, payrollEnd, finalFile);

//            DebugHelper.sendMail("<EMAIL>", String.format("Secondary After payroll Initialize for %d housemaids", housemaids.size()));

            if (finalFile) {
                HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer().prepareHousemaidsInfo(housemaidIds);
            } else {
                getHousemaidPayrollInitializer().prepareVisaInfo(housemaidIds);
            }

            int index = 1;
            for (HousemaidPayrollBean bean : beans) {
                Housemaid housemaid = bean.getHousemaid();
                HousemaidPayrollBean targetBean;
                try {
                    HousemaidPayrollLog log;
                    if (getHousemaidPayrollInitializer().hasPaidSalary(housemaid.getId())) {
                        targetBean = bean;
                        if (finalFile) {
                            targetBean = housemaidPayrollAuditService.setHousemaidInfo(targetBean, targetBean.getHousemaid(), payrollMonth, HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer());
                        }

                        log = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).generatePayrollLog(housemaid, targetBean, todo);
                        //set the new bean
//                        log.setHousemaidPayrollBean(bean);
                    } else {
                        log = null;//Setup.getRepository(HousemaidPayrollLogRepository.class).findTopByHousemaidAndPayrollMonthAndMonthlyPaymentRuleNot(housemaid, todo.getPayrollMonth());
                        if (log == null)
                            continue;
                        log.setManagerAdditions(log.getManagerAdditions() + log.valueOrZero(bean.getManagerAddition()));
                        log.setTotalAddition(log.getTotalAddition() + bean.getTotatIcome());
                        log.setTotalSalary(log.getTotalSalary() + log.valueOrZero(bean.getBalance()));

                        HousemaidBeanInfo oldBeanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).findTopByHousemaidPayrollLog(log);
                        if(oldBeanInfo == null) {
                            oldBeanInfo = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).createBeanInfoDetails(housemaid, log);
                            oldBeanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).save(oldBeanInfo);
                        }
                        targetBean = new HousemaidPayrollBean(oldBeanInfo, true);
                        targetBean.setManagerAddition(targetBean.getManagerAddition() + bean.getManagerAddition());
                        targetBean.setTotatIcome(targetBean.getTotatIcome() + bean.getTotatIcome());
                        targetBean.setTotalBalance(targetBean.getTotalBalance() + bean.getTotalBalance());

                    }

                    log.setHousemaidPayrollBean(targetBean);
                    log.setSn("H-" + index);

//                    //mark the logs that will be included (NO NEED TO CHECK IF INCLUDED HERE IN SECONDARY
                    log.setWillBeIncluded(true);

                    if (log.getTotalSalary() > 0) {
                        logs.add(log);
                        index++;
                    }

                } catch (Exception e) {
                    Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, String.format("Error while generating payroll log for houemaid %s", housemaid.getName()), e);
                    DebugHelper.sendExceptionMail("<EMAIL>", e, String.format("Error while generating payroll log for housemaid %s", housemaid.getName()), true);
                }
            }

//            DebugHelper.sendMail("<EMAIL>", String.format("After processing logs individually for %d housemaids", housemaids.size()));

            //Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).setCancelledContractsMaidVisaSalaryOnHold();
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, String.format("Error while generating housemaids logs for %d housemaids", housemaids.size()), false);
        } finally {
            clearHousemaidPayrollInitializer();
        }
        return logs;
    }

    @Transactional
    public List<HousemaidPayrollLog> getMVPayrollLogsForPreviousMonths(PayrollAccountantTodo payrollAccountantTodo, boolean isFinal) {
        List<HousemaidPayrollLog> logs = new ArrayList<>();
        if (payrollAccountantTodo.getMonthlyPaymentRule() == null
                || !payrollAccountantTodo.getMonthlyPaymentRule().isTargetingMaidVisa())
            return logs;
        MonthlyPaymentRule monthlyPaymentRule = payrollAccountantTodo.getMonthlyPaymentRule();

        List<Housemaid> housemaids;

        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        query.filterBy("housemaidType", "=", HousemaidType.MAID_VISA);
        if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
            query.filterBy("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
        }
        query.filterBy("startDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate())
            .and(new SelectFilter("replacementSalaryStartDate", "IS NULL", null).or("replacementSalaryStartDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate()));

        query.filterBy (new SelectFilter(new SelectFilter("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null)));
        //Todo --> check other conditions

        housemaids = query.execute();
        Long monthlyPaymentPickListItemId = PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId();
        try {
            if (housemaids.isEmpty()) return new ArrayList<>();

            java.sql.Date fromDate = new Date(DateUtil.addMonths(payrollAccountantTodo.getMonthlyPaymentRule().getPayrollMonth(), -2).getTime());
            java.sql.Date toDate = payrollAccountantTodo.getMonthlyPaymentRule().getPayrollMonth();

            List<HousemaidPayrollLog> previousMonthsPayrollLogsNotPreCollected = housemaidPayrollLogRepository.findByHousemaidsAndTransferredFalseAndPayrollMonthBetweenAndPreCollected(housemaids, fromDate, toDate, "false");
            List<HousemaidPayrollLog> previousMonthsPayrollLogsPreCollected = housemaidPayrollLogRepository.findByHousemaidsAndTransferredFalseAndPayrollMonthBetweenAndPreCollected(housemaids, fromDate, toDate, "true");
            if (previousMonthsPayrollLogsNotPreCollected.isEmpty() && previousMonthsPayrollLogsPreCollected.isEmpty()) return new ArrayList<>();

//            DebugHelper.sendMail("<EMAIL>", String.format("getMVPayrollLogsForPreviousMonths for %d housemaids - #old logs %d - Before the month: %s",
//                    housemaids.size(),
//                    previousMonthsPayrollLogs.size(),
//                    DateUtil.formatDateDashed(payrollAccountantTodo.getMonthlyPaymentRule().getPayrollMonth())
//            ));

            Double vatPercentage = 1.0 + (Integer.parseInt(Setup.getParameter(Setup.getModule("sales"),
                    PayrollManagementModule.PARAMETER_SALES_VAT_PERCENT)) * 1.0 / 100);

            for (HousemaidPayrollLog log : previousMonthsPayrollLogsNotPreCollected) {
                Housemaid housemaid = log.getHousemaid();

                MonthlyPaymentRule rule = new MonthlyPaymentRule();
                rule.setPayrollMonth(log.getPayrollMonth());
                Date start = new Date(new LocalDate(log.getPayrollMonth()).plusMonths(1).withDayOfMonth(1).toDate().getTime());
                Date end = new Date(new LocalDate(log.getPayrollMonth()).plusMonths(1).dayOfMonth().withMaximumValue().toDate().getTime());

                List<BigInteger> paidIdsObj = Setup.getRepository(PaymentRepository.class)
                        .findPaidMaidVisaClientsForMonth(ContractStatus.ACTIVE.toString(), housemaid.getId(),
                                monthlyPaymentPickListItemId,
                                start, end, PaymentStatus.RECEIVED.toString(), vatPercentage, log.getTotalSalary());

                List<Long> paidIds = paidIdsObj.stream().map(x -> x != null ? x.longValue() : 0L).collect(Collectors.toList());
                if (!paidIds.isEmpty()) {
                    if (isFinal) {
                        log.setWillBeIncluded(true);
                        log.setPayrollAccountantTodo(payrollAccountantTodo);
                        logs.add(log);
                        housemaidPayrollLogRepository.save(log);
                    } else
                        logs.add(log);
                }
            }
            for (HousemaidPayrollLog log : previousMonthsPayrollLogsPreCollected) {
                Housemaid housemaid = log.getHousemaid();

                MonthlyPaymentRule rule = new MonthlyPaymentRule();
                rule.setPayrollMonth(log.getPayrollMonth());
                java.sql.Date startOfPreCollectedSalary = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(1).toDate().getTime());
                java.sql.Date endOfPreCollectedSalary = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).dayOfMonth().withMaximumValue().toDate().getTime());

                List<BigInteger> paidIdsObj;
                //if start date = this month -> take the repayment < this month
                if((housemaid.getReplacementSalaryStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getReplacementSalaryStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)
                        ||(housemaid.getStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)) {

                    paidIdsObj = Setup.getRepository(PaymentRepository.class)
                            .findPaidMaidVisaClientsForMonthFor1stMonthAsPreCollectedContracts(ContractStatus.ACTIVE.toString(), housemaid.getId(),
                                    monthlyPaymentPickListItemId,
                                    endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), vatPercentage, log.getTotalSalary());
                } else {
                    paidIdsObj = Setup.getRepository(PaymentRepository.class)
                            .findPaidMaidVisaClientsForMonth(ContractStatus.ACTIVE.toString(), housemaid.getId(),
                                    monthlyPaymentPickListItemId,
                                    startOfPreCollectedSalary, endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), vatPercentage, log.getTotalSalary());

                }

                List<Long> paidIds = paidIdsObj.stream().map(x -> x != null ? x.longValue() : 0L).collect(Collectors.toList());
                if (!paidIds.isEmpty()) {
                    if (isFinal) {
                        log.setWillBeIncluded(true);
                        log.setPayrollAccountantTodo(payrollAccountantTodo);
                        logs.add(log);
                        housemaidPayrollLogRepository.save(log);
                    } else
                        logs.add(log);
                }
            }
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while getMVPayrollLogsForPreviousMonths for %d housemaids" +  housemaids.size(), false);
            throw ex;
        }
        return logs;
    }

    public List<HousemaidPayrollLog> getMVPayrollLogsForPreviousMonthsNew(MonthlyPaymentRule monthlyPaymentRule, PayrollAccountantTodo payrollAccountantTodo, boolean isFinal) {
        List<HousemaidPayrollLog> logs = new ArrayList<>();
        if (monthlyPaymentRule == null
                || !monthlyPaymentRule.isTargetingMaidVisa())
            return logs;

        HousemaidRepository housemaidRepository = Setup.getRepository(HousemaidRepository.class);
        List<Housemaid> housemaids;

        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        query.filterBy("housemaidType", "=", HousemaidType.MAID_VISA);
        if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
            query.filterBy("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
        }
        query.filterBy("startDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate())
                .and(new SelectFilter("replacementSalaryStartDate", "IS NULL", null).or("replacementSalaryStartDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate()));

        query.filterBy (new SelectFilter(new SelectFilter("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null)));

        housemaids = query.execute();
        Long monthlyPaymentPickListItemId = PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId();
        try {
            if (housemaids.isEmpty()) return new ArrayList<>();

            java.sql.Date fromDate = new Date(DateUtil.addMonths(monthlyPaymentRule.getPayrollMonth(), -2).getTime());
            java.sql.Date toDate = monthlyPaymentRule.getPayrollMonth();

            List<HousemaidPayrollLog> previousMonthsPayrollLogsNotPreCollected = housemaidPayrollLogRepository.findByHousemaidsAndTransferredFalseAndPayrollMonthBetweenAndPreCollected(housemaids, fromDate, toDate, "false");
            List<HousemaidPayrollLog> previousMonthsPayrollLogsPreCollected = housemaidPayrollLogRepository.findByHousemaidsAndTransferredFalseAndPayrollMonthBetweenAndPreCollected(housemaids, fromDate, toDate, "true");


            if (previousMonthsPayrollLogsNotPreCollected.isEmpty() && previousMonthsPayrollLogsPreCollected.isEmpty()) return new ArrayList<>();


//            DebugHelper.sendMail("<EMAIL>", String.format("getMVPayrollLogsForPreviousMonths for %d housemaids - #old logs %d - Before the month: %s",
//                    housemaids.size(),
//                    previousMonthsPayrollLogs.size(),
//                    DateUtil.formatDateDashed(monthlyPaymentRule.getPayrollMonth())
//            ));

            Double vatPercentage = 1.0 + (Integer.parseInt(Setup.getParameter(Setup.getModule("sales"),
                    PayrollManagementModule.PARAMETER_SALES_VAT_PERCENT)) * 1.0 / 100);

            for (HousemaidPayrollLog log : previousMonthsPayrollLogsNotPreCollected) {
                Housemaid housemaid = log.getHousemaid();

                MonthlyPaymentRule rule = new MonthlyPaymentRule();
                rule.setPayrollMonth(log.getPayrollMonth());
                Date start = new Date(new LocalDate(log.getPayrollMonth()).plusMonths(1).withDayOfMonth(1).toDate().getTime());
                Date end = new Date(new LocalDate(log.getPayrollMonth()).plusMonths(1).dayOfMonth().withMaximumValue().toDate().getTime());
                List<BigInteger> paidIdsObj =  Setup.getRepository(PaymentRepository.class)
                        .findPaidMaidVisaClientsForMonth(ContractStatus.ACTIVE.toString(), housemaid.getId(),
                                monthlyPaymentPickListItemId,
                                start, end, PaymentStatus.RECEIVED.toString(), vatPercentage, log.getTotalSalary());
                List<Long> paidIds = paidIdsObj.stream().map(x -> x != null ? x.longValue() : 0L).collect(Collectors.toList());
                if (!paidIds.isEmpty()) {
                    if (isFinal) {
                        log.setWillBeIncluded(true);
                        log.setPayrollAccountantTodo(payrollAccountantTodo);
                        logs.add(log);
                        housemaidPayrollLogRepository.save(log);
                    } else
                        logs.add(log);
                }
            }

            for (HousemaidPayrollLog log : previousMonthsPayrollLogsPreCollected) {
                Housemaid housemaid = log.getHousemaid();

                MonthlyPaymentRule rule = new MonthlyPaymentRule();
                rule.setPayrollMonth(log.getPayrollMonth());

                java.sql.Date startOfPreCollectedSalary = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(1).toDate().getTime());
                java.sql.Date endOfPreCollectedSalary = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).dayOfMonth().withMaximumValue().toDate().getTime());

                List<BigInteger> paidIdsObj;

                if((housemaid.getReplacementSalaryStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getReplacementSalaryStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)
                        ||(housemaid.getStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)) {


                    paidIdsObj = Setup.getRepository(PaymentRepository.class)
                            .findPaidMaidVisaClientsForMonthFor1stMonthAsPreCollectedContracts(ContractStatus.ACTIVE.toString(), housemaid.getId(),
                                    monthlyPaymentPickListItemId,
                                    endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), vatPercentage, log.getTotalSalary());
                } else {
                    paidIdsObj = Setup.getRepository(PaymentRepository.class)
                            .findPaidMaidVisaClientsForMonth(ContractStatus.ACTIVE.toString(), housemaid.getId(),
                                    monthlyPaymentPickListItemId,
                                    startOfPreCollectedSalary, endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), vatPercentage, log.getTotalSalary());
                }

                List<Long> paidIds = paidIdsObj.stream().map(x -> x != null ? x.longValue() : 0L).collect(Collectors.toList());
                if (!paidIds.isEmpty()) {
                    if (isFinal) {
                        log.setWillBeIncluded(true);
                        log.setPayrollAccountantTodo(payrollAccountantTodo);
                        logs.add(log);
                        housemaidPayrollLogRepository.save(log);
                    } else
                        logs.add(log);
                }
            }

            for (ExcludedMVInfo excludedMVInfo : monthlyPaymentRule.getMustIncludedMVInfoList()) {
                if (excludedMVInfo.getPayrollMonth().compareTo(monthlyPaymentRule.getPayrollMonth()) < 0){
                    HousemaidPayrollLog log = housemaidPayrollLogRepository.findTopByHousemaidAndPayrollMonth(housemaidRepository.findOne(excludedMVInfo.getHousemaidId()), excludedMVInfo.getPayrollMonth());
                    if (log != null) {
                        if (isFinal) {
                            log.setWillBeIncluded(true);
                            log.setPayrollAccountantTodo(payrollAccountantTodo);
                            logs.add(log);
                            housemaidPayrollLogRepository.save(log);
                        } else
                            logs.add(log);
                    }
                }
            }
        } catch (Exception ex) {
            logger.log(Level.WARNING, "Exception in getMVPayrollLogsForPreviousMonthsNew for rule: " + monthlyPaymentRule.getId() + ", exception: " + ex.getMessage());
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while getMVPayrollLogsForPreviousMonths for %d housemaids" +  housemaids.size(), false);
            throw ex;
        }
        return logs;
    }

    @Transactional
    public List<HousemaidPayrollBean> processSalariesOfSecondaryPayroll(List<Housemaid> housemaids, MonthlyPaymentRule rule, LocalDate payrollStart, LocalDate payrollEnd, boolean finalFile) {
        List<Long> housemaidIds = housemaids.stream().map(BaseEntity::getId).collect(Collectors.toList());
        HousemaidPayrollInitializer initializer = HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer();

        // Prepare calculations for transactions
        initializer.prepareAll(housemaidIds, payrollStart, payrollEnd, new Date(new LocalDate(rule.getPayrollMonth()).dayOfMonth().withMaximumValue().toDate().getTime()));

        LocalDate payrollMonth = new LocalDate(rule.getPayrollMonth());

        List<HousemaidPayrollBean> beans = new ArrayList<>();
        for (Housemaid housemaid : housemaids) {
            try {
                HousemaidPayrollBean bean = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).runTransactions(housemaid, rule);
                bean.setHousemaid(housemaid);
                housemaid.setPayrollBean(bean);
                beans.add(bean);
            } catch (Exception e) {
                Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, String.format("Error while generating payroll log for houemaid %s", housemaid.getName()), e);
                DebugHelper.sendExceptionMail("<EMAIL>", e, String.format("Error while generating payroll bean processSalariesOfSecondaryPayroll for housemaid %s", housemaid.getName()), true);
            }
        }

        return beans;
    }

    @Transactional
    public HousemaidPayrollLog generatePayrollLog(Housemaid housemaid, HousemaidPayrollBean bean, PayrollAccountantTodo todo) {
        switch (PayrollAccountantTodoType.valueOf(todo.getTaskName())) {
            case WPS:
                return new HousemaidPayrollLog(housemaid, "EDR", housemaid.getName(), getHousemaidPayrollInitializer(), bean.getBalance(),
                        todo.getMonthlyPaymentRule().getPayrollMonth(), bean.getStartDateDeduction(), bean.getManagerAddition(),
                        bean.getLoanRepayment(), getRemainingLoanBalance(bean.getRemainingLoanBalance()), bean.getAdditionToBalanceDeductionLimit(), bean.getTotalDeduction(), bean.getTotatIcome(), bean.getUnpaidDeduction(), bean.getUnpaidDeductionRepayment(),
                        housemaid.getName(), "UAE", housemaid.getPhoneNumber());
            case LOCAL_TRANSFER:
                return new HousemaidPayrollLog(getHousemaidPayrollInitializer(), housemaid, housemaid.getName(), "UAE", housemaid.getPhoneNumber(),
                        bean.getBalance(), todo.getMonthlyPaymentRule().getPayrollMonth(), bean.getStartDateDeduction(), bean.getManagerAddition(),
                        bean.getLoanRepayment(), getRemainingLoanBalance(bean.getRemainingLoanBalance()), bean.getAdditionToBalanceDeductionLimit(), bean.getTotalDeduction(), bean.getTotatIcome(), bean.getUnpaidDeduction(), bean.getUnpaidDeductionRepayment(),
                        "EDR", housemaid.getName());
            case CASH:
                return new HousemaidPayrollLog(getHousemaidPayrollInitializer(), housemaid, housemaid.getName(), bean.getBalance(),
                        todo.getMonthlyPaymentRule().getPayrollMonth(), bean.getStartDateDeduction(), bean.getManagerAddition(),
                        bean.getLoanRepayment(), getRemainingLoanBalance(bean.getRemainingLoanBalance()), bean.getAdditionToBalanceDeductionLimit(), bean.getTotalDeduction(), bean.getTotatIcome(), bean.getUnpaidDeduction(), bean.getUnpaidDeductionRepayment());
        }
        throw new RuntimeException("Unable to generate payroll log for housemaid " + housemaid.getName());
    }

    @Transactional
    public HousemaidPayrollLog createPayrollLogBasedOnAll(Housemaid housemaid, HousemaidPayrollBean bean, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo) {
        switch (monthlyPaymentRule.getPaymentMethod()) {
            case WPS:
                return new HousemaidPayrollLog(housemaid, monthlyPaymentRule, auditTodo, todo, "EDR", housemaid.getName(), bean.getBalance(),
                        monthlyPaymentRule.getPayrollMonth(), bean.getStartDateDeduction(), bean.getManagerAddition(),
                        bean.getLoanRepayment(), getRemainingLoanBalance(bean.getRemainingLoanBalance()), bean.getAdditionToBalanceDeductionLimit(), bean.getTotalDeduction(), bean.getTotatIcome(), bean.getUnpaidDeduction(), bean.getUnpaidDeductionRepayment(),
                        housemaid.getName(), "UAE", housemaid.getPhoneNumber());
            case LOCAL_TRANSFER:
                return new HousemaidPayrollLog(housemaid, monthlyPaymentRule, auditTodo, todo, housemaid.getName(), "UAE", housemaid.getPhoneNumber(),
                        bean.getBalance(), monthlyPaymentRule.getPayrollMonth(), bean.getStartDateDeduction(), bean.getManagerAddition(),
                        bean.getLoanRepayment(), getRemainingLoanBalance(bean.getRemainingLoanBalance()), bean.getAdditionToBalanceDeductionLimit(), bean.getTotalDeduction(), bean.getTotatIcome(), bean.getUnpaidDeduction(), bean.getUnpaidDeductionRepayment(),
                        "EDR", housemaid.getName());
            case CASH:
                return new HousemaidPayrollLog(housemaid, monthlyPaymentRule, auditTodo, todo, housemaid.getName(), bean.getBalance(),
                        monthlyPaymentRule.getPayrollMonth(), bean.getStartDateDeduction(), bean.getManagerAddition(),
                        bean.getLoanRepayment(), getRemainingLoanBalance(bean.getRemainingLoanBalance()), bean.getAdditionToBalanceDeductionLimit(), bean.getTotalDeduction(), bean.getTotatIcome(), bean.getUnpaidDeduction(), bean.getUnpaidDeductionRepayment());
        }
        throw new RuntimeException("Unable to generate payroll log for housemaid " + housemaid.getName());
    }

    @Transactional
    public HousemaidPayrollBean runTransactions(Housemaid housemaid, MonthlyPaymentRule monthlyPaymentRule) {

        LocalDate payrollEnd = new LocalDate(getPayrollEndLockDate(monthlyPaymentRule));
        LocalDate payrollStart = new LocalDate(getPayrollStartLockDate(monthlyPaymentRule));

        Reflections reflections =
                new Reflections("com.magnamedia.salarycalculation.v2");
        Set<Class<? extends HousemaidSalaryTransaction>> allClasses =
                reflections.getSubTypesOf(HousemaidSalaryTransaction.class);


        HousemaidPayrollBean bean = new HousemaidPayrollBean();
        for (Class<?> clazz : allClasses) {
            try {
                Method calculateMethod =
                        clazz.getMethod(
                                "calculate",
                                Housemaid.class,
                                LocalDate.class,
                                LocalDate.class,
                                Boolean.class);

                Method setInSalaryObjectMethod =
                        clazz.getMethod(
                                "setInSalaryObject",
                                HousemaidPayrollBean.class,
                                Double.class);
                HousemaidSalaryTransaction t = (HousemaidSalaryTransaction) clazz.newInstance();
                bean = (HousemaidPayrollBean) setInSalaryObjectMethod.invoke(t, bean, (Double) calculateMethod.invoke(t, housemaid, payrollStart, payrollEnd, monthlyPaymentRule.isSecondaryMonthlyRule()));
            } catch (NoSuchMethodException | SecurityException | IllegalArgumentException | InvocationTargetException ex) {
                Logger.getLogger(HousemaidPayrollController.class.getName()).log(Level.SEVERE, null, ex);
            } catch (InstantiationException | IllegalAccessException ex) {
                Logger.getLogger(HousemaidPayrollBean.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return bean;
    }

    public HousemaidPayrollBean runTransactionsNew(Housemaid housemaid, MonthlyPaymentRule monthlyPaymentRule, boolean finalFile, LocalDate payrollEnd, LocalDate payrollStart, LocalDate previousPayrollStart) {

        Boolean firstSalary = housemaid.isFirstSalary(monthlyPaymentRule.getPayrollMonth());

        Reflections reflections =
                new Reflections("com.magnamedia.salarycalculation.version2NewZeroPhase");
        Set<Class<? extends HousemaidSalaryTransactionNewPhaseZero>> allClassesPhaseZero =
                reflections.getSubTypesOf(HousemaidSalaryTransactionNewPhaseZero.class);


        HousemaidPayrollBean bean = new HousemaidPayrollBean();
        for (Class<?> clazz : allClassesPhaseZero) {
            try {
                Method calculateMethod =
                        clazz.getMethod(
                                "calculate",
                                Housemaid.class,
                                LocalDate.class,
                                LocalDate.class,
                                LocalDate.class,
                                Date.class,
                                Boolean.class,
                                Boolean.class,
                                Boolean.class,
                                HousemaidPayrollBean.class);

                Method setInSalaryObjectMethod =
                        clazz.getMethod(
                                "setInSalaryObject",
                                HousemaidPayrollBean.class,
                                Double.class);
                HousemaidSalaryTransactionNewPhaseZero t = (HousemaidSalaryTransactionNewPhaseZero) clazz.newInstance();
                Double value = (Double) calculateMethod.invoke(t, housemaid, payrollStart, payrollEnd, previousPayrollStart, monthlyPaymentRule.getPayrollMonth(), monthlyPaymentRule.isSecondaryMonthlyRule(), finalFile, firstSalary, bean);
                bean = (HousemaidPayrollBean) setInSalaryObjectMethod.invoke(t, bean, value);
            } catch (NoSuchMethodException | SecurityException | IllegalArgumentException | InvocationTargetException ex) {
                Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, "Exception first while running transaction for housemaid #" + housemaid.getId(), ex);
                DebugHelper.sendExceptionMail("<EMAIL>", ex, "Exception Zero while running transaction for housemaid #" + housemaid.getId(), false);
            } catch (InstantiationException | IllegalAccessException ex) {
                Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, "Exception second while running transaction for housemaid #" + housemaid.getId(), ex);
                DebugHelper.sendExceptionMail("<EMAIL>", ex, "Exception second while running transaction for housemaid #" + housemaid.getId(), false);
            }
        }

        reflections =
                new Reflections("com.magnamedia.salarycalculation.version2New");
        Set<Class<? extends HousemaidSalaryTransactionNew>> allClassesPhaseOne =
                reflections.getSubTypesOf(HousemaidSalaryTransactionNew.class);


        for (Class<?> clazz : allClassesPhaseOne) {
            try {
                Method calculateMethod =
                        clazz.getMethod(
                                "calculate",
                                Housemaid.class,
                                LocalDate.class,
                                LocalDate.class,
                                LocalDate.class,
                                Date.class,
                                Boolean.class,
                                Boolean.class,
                                Boolean.class,
                                HousemaidPayrollBean.class);

                Method setInSalaryObjectMethod =
                        clazz.getMethod(
                                "setInSalaryObject",
                                HousemaidPayrollBean.class,
                                Double.class);
                HousemaidSalaryTransactionNew t = (HousemaidSalaryTransactionNew) clazz.newInstance();
                Double value = (Double) calculateMethod.invoke(t, housemaid, payrollStart, payrollEnd, previousPayrollStart, monthlyPaymentRule.getPayrollMonth(), monthlyPaymentRule.isSecondaryMonthlyRule(), finalFile, firstSalary, bean);
                bean = (HousemaidPayrollBean) setInSalaryObjectMethod.invoke(t, bean, value);
            } catch (NoSuchMethodException | SecurityException | IllegalArgumentException | InvocationTargetException ex) {
                Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, "Exception third while running transaction for housemaid #" + housemaid.getId(), ex);
                DebugHelper.sendExceptionMail("<EMAIL>", ex, "Exception third while running transaction for housemaid #" + housemaid.getId(), false);
            } catch (InstantiationException | IllegalAccessException ex) {
                Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, "Exception forth while running transaction for housemaid #" + housemaid.getId(), ex);
                DebugHelper.sendExceptionMail("<EMAIL>", ex, "Exception forth while running transaction for housemaid #" + housemaid.getId(), false);
            }
        }

        reflections =
                new Reflections("com.magnamedia.salarycalculation.version2NewSecondPhase");
        Set<Class<? extends HousemaidSalaryTransactionNewPhaseTwo>> allClassesPhaseTwo =
                reflections.getSubTypesOf(HousemaidSalaryTransactionNewPhaseTwo.class);


        for (Class<?> clazz : allClassesPhaseTwo) {
            try {
                Method calculateMethod =
                        clazz.getMethod(
                                "calculate",
                                Housemaid.class,
                                LocalDate.class,
                                LocalDate.class,
                                LocalDate.class,
                                Date.class,
                                Boolean.class,
                                Boolean.class,
                                Boolean.class,
                                HousemaidPayrollBean.class);

                Method setInSalaryObjectMethod =
                        clazz.getMethod(
                                "setInSalaryObject",
                                HousemaidPayrollBean.class,
                                Double.class);
                HousemaidSalaryTransactionNewPhaseTwo t = (HousemaidSalaryTransactionNewPhaseTwo) clazz.newInstance();
                Double value = (Double) calculateMethod.invoke(t, housemaid, payrollStart, payrollEnd, previousPayrollStart, monthlyPaymentRule.getPayrollMonth(), monthlyPaymentRule.isSecondaryMonthlyRule(), finalFile, firstSalary, bean);
                bean = (HousemaidPayrollBean) setInSalaryObjectMethod.invoke(t, bean, value);
            } catch (NoSuchMethodException | SecurityException | IllegalArgumentException | InvocationTargetException ex) {
                Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, "Exception fifth while running transaction for housemaid #" + housemaid.getId(), ex);
                DebugHelper.sendExceptionMail("<EMAIL>", ex, "Exception third while running transaction for housemaid #" + housemaid.getId(), false);
            } catch (InstantiationException | IllegalAccessException ex) {
                Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, "Exception sixth while running transaction for housemaid #" + housemaid.getId(), ex);
                DebugHelper.sendExceptionMail("<EMAIL>", ex, "Exception forth while running transaction for housemaid #" + housemaid.getId(), false);
            }
        }
        bean.setNotFinal(!finalFile);
        return bean;
    }

    @Transactional
    public void recoverSimulationData(java.sql.Date payrollMonth, boolean finalFile) {

        PayrollManagerNoteRepository payrollManagerNoteRepository = Setup.getRepository(PayrollManagerNoteRepository.class);
        RepaymentRepository repaymentRepository = Setup.getRepository(RepaymentRepository.class);
        EmployeeLoanRepository employeeLoanRepository = Setup.getRepository(EmployeeLoanRepository.class);
        UnpaidDeductionRepository unpaidDeductionRepository = Setup.getRepository(UnpaidDeductionRepository.class);
        UnpaidDeductionRepaymentRepository unpaidDeductionRepaymentRepository = Setup.getRepository(UnpaidDeductionRepaymentRepository.class);
        HousemaidBalancesHistoryRepository housemaidBalancesHistoryRepository = Setup.getRepository(HousemaidBalancesHistoryRepository.class);


        List<PayrollManagerNote> notes = payrollManagerNoteRepository.findByNotFinal(true);
        for (PayrollManagerNote note : notes) payrollManagerNoteRepository.delete(note);

        List<Repayment> repayments = repaymentRepository.findByNotFinal(true);
        for (Repayment repayment : repayments) repaymentRepository.delete(repayment);

        //Jirra ACC-1093
        List<EmployeeLoan> loans = employeeLoanRepository.findByNotFinal(true);
        for (EmployeeLoan loan : loans) employeeLoanRepository.delete(loan);

        List<UnpaidDeduction> unpaidDeductions = unpaidDeductionRepository.findByNotFinal(true);
        for (UnpaidDeduction unpaidDeduction : unpaidDeductions) unpaidDeductionRepository.delete(unpaidDeduction);

        List<UnpaidDeductionRepayment> unpaidDeductionRepayments = unpaidDeductionRepaymentRepository.findByNotFinal(true);
        for (UnpaidDeductionRepayment unpaidDeductionRepayment : unpaidDeductionRepayments) unpaidDeductionRepaymentRepository.delete(unpaidDeductionRepayment);

        List<HousemaidBalancesHistory> housemaidBalancesHistories = housemaidBalancesHistoryRepository.findByNotFinal(true);
        for (HousemaidBalancesHistory h : housemaidBalancesHistories) housemaidBalancesHistoryRepository.delete(h);

        // if(finalFile) {
        List<ExcludedMaidSalary> excludedMaidSalaries = Setup.getRepository(ExcludedMaidSalaryRepository.class)
                .findByPayrollMonthAndTransferredFalse(payrollMonth);
        for (ExcludedMaidSalary excludedMaidSalary : excludedMaidSalaries) {
            Setup.getRepository(ExcludedMaidSalaryRepository.class).delete(excludedMaidSalary);
        }
        // }
    }

    @Transactional
    public void recoverSimulationDataBasedOnAll(Long housemaid, MonthlyPaymentRule monthlyPaymentRule, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, java.sql.Date payrollMonth,  List<MonthlyPaymentRule> listOfRelatedRules) {

        PayrollManagerNoteRepository payrollManagerNoteRepository = Setup.getRepository(PayrollManagerNoteRepository.class);
        RepaymentRepository repaymentRepository = Setup.getRepository(RepaymentRepository.class);
        EmployeeLoanRepository employeeLoanRepository = Setup.getRepository(EmployeeLoanRepository.class);
        UnpaidDeductionRepository unpaidDeductionRepository = Setup.getRepository(UnpaidDeductionRepository.class);
        UnpaidDeductionRepaymentRepository unpaidDeductionRepaymentRepository = Setup.getRepository(UnpaidDeductionRepaymentRepository.class);
        HousemaidBalancesHistoryRepository housemaidBalancesHistoryRepository = Setup.getRepository(HousemaidBalancesHistoryRepository.class);
        ExcludedMaidSalaryRepository excludedMaidSalaryRepository = Setup.getRepository(ExcludedMaidSalaryRepository.class);
        HousemaidPayrollBeanRepository housemaidPayrollBeanRepository = Setup.getRepository(HousemaidPayrollBeanRepository.class);
        HousemaidPayrollLogRepository housemaidPayrollLogRepository = Setup.getRepository(HousemaidPayrollLogRepository.class);

        payrollManagerNoteRepository.deleteByNotFinalAndHousemaidId(true, housemaid);

        repaymentRepository.deleteByNotFinalAndHousemaidId(true, housemaid);

        employeeLoanRepository.deleteByNotFinalAndHousemaidId(true, housemaid);

        unpaidDeductionRepository.deleteByNotFinalAndHousemaidId(true, housemaid);

        unpaidDeductionRepaymentRepository.deleteByNotFinalAndHousemaidId(true, housemaid);

        housemaidBalancesHistoryRepository.deleteByNotFinalAndHousemaidId(true, housemaid);

        excludedMaidSalaryRepository.deleteByPayrollMonthAndTransferredFalseAndHousemaidId(payrollMonth, housemaid);

        SelectQuery<HousemaidPayrollLog> selectQuery = new SelectQuery<>(HousemaidPayrollLog.class);
        selectQuery.filterBy("housemaid.id", "=", housemaid);
        selectQuery.filterBy("monthlyPaymentRule", "IN", listOfRelatedRules);
        if(auditTodo != null && auditTodo.getId() != null)
            selectQuery.filterBy("payrollAuditTodo", "=", auditTodo);
//        if(todo != null && todo.getId() != null)
//            selectQuery.filterBy("payrollAccountantTodo", "=", todo);
        selectQuery.filterBy("payrollMonth", "=", payrollMonth);
        selectQuery.sortBy("id", false);
        selectQuery.setLimit(1);
        List<HousemaidPayrollLog> logs = selectQuery.execute();

        for(HousemaidPayrollLog log : logs){ //should get one record only
            HousemaidPayrollBean bean = log.getHousemaidPayrollBean();
            housemaidPayrollLogRepository.deleteById(log.getId());
            if(bean != null)
                housemaidPayrollBeanRepository.deleteById(bean.getId());
        }
    }

    public java.sql.Date getPayrollEndLockDate(MonthlyPaymentRule rule) {
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(rule.getPayrollMonth());

        for (MonthlyPaymentRule monthlyPaymentRule : rules) {
            if (monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && monthlyPaymentRule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS)) {
                return monthlyPaymentRule.getLockDate();
            }
        }
        throw new RuntimeException("Can't find current month lock date for housemaids");
    }

    public java.sql.Date getPayrollStartPaymentDate(MonthlyPaymentRule rule) {
        java.sql.Date lastMonthPayroll = new java.sql.Date(DateUtil.addMonths(rule.getPayrollMonth(), -1).getTime());
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(lastMonthPayroll);

        for (MonthlyPaymentRule monthlyPaymentRule : rules) {
            if (monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && monthlyPaymentRule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS)) {
                return monthlyPaymentRule.getPaymentDate();
            }
        }

        LocalDate dt = new LocalDate(lastMonthPayroll);
        return new java.sql.Date(dt.withDayOfMonth(27).toDate().getTime());
    }

    public java.sql.Date getPayrollEndPaymentDate(MonthlyPaymentRule rule) {
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(rule.getPayrollMonth());

        for (MonthlyPaymentRule monthlyPaymentRule : rules) {
            if (monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && monthlyPaymentRule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS)) {
                return monthlyPaymentRule.getPaymentDate();
            }
        }
        throw new RuntimeException("Can't find current month payment date for housemaids");
    }

    public java.sql.Date getPayrollStartLockDate(MonthlyPaymentRule rule) {
        java.sql.Date lastMonthPayroll = new java.sql.Date(DateUtil.addMonths(rule.getPayrollMonth(), -1).getTime());
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(lastMonthPayroll);

        for (MonthlyPaymentRule monthlyPaymentRule : rules) {
            if (monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && monthlyPaymentRule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS)) {
                return monthlyPaymentRule.getLockDate();
            }
        }

        LocalDate dt = new LocalDate(lastMonthPayroll);
        return new java.sql.Date(dt.withDayOfMonth(27).toDate().getTime());
    }

    public java.sql.Date getPayrollStartLockDateOfPayrollMonth(Date payrollMoth) {
        java.sql.Date lastMonthPayroll = new java.sql.Date(DateUtil.addMonths(payrollMoth, -1).getTime());
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(lastMonthPayroll);

        for (MonthlyPaymentRule monthlyPaymentRule : rules) {
            if (monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && monthlyPaymentRule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS)) {
                return monthlyPaymentRule.getLockDate();
            }
        }

        LocalDate dt = new LocalDate(lastMonthPayroll);
        return new java.sql.Date(dt.withDayOfMonth(27).toDate().getTime());
    }

    public java.sql.Date getPayrollEndPaymentDateOfPayrollMonth(Date payrollMoth) {
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(payrollMoth);

        for (MonthlyPaymentRule monthlyPaymentRule : rules) {
            if (monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && monthlyPaymentRule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS)) {
                return monthlyPaymentRule.getPaymentDate();
            }
        }
        LocalDate dt = new LocalDate(payrollMoth);
        return new java.sql.Date(dt.withDayOfMonth(27).toDate().getTime());
    }

    public java.sql.Date getPayrollStartPaymentDateOfPayrollMonth(Date payrollMoth) {
        java.sql.Date lastMonthPayroll = new java.sql.Date(DateUtil.addMonths(payrollMoth, -1).getTime());
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(lastMonthPayroll);

        for (MonthlyPaymentRule monthlyPaymentRule : rules) {
            if (monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && monthlyPaymentRule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS)) {
                return monthlyPaymentRule.getPaymentDate();
            }
        }

        LocalDate dt = new LocalDate(lastMonthPayroll);
        return new java.sql.Date(dt.withDayOfMonth(27).toDate().getTime());
    }

    Double getRemainingLoanBalance(String loanBalance) {
        try {
            return Double.valueOf(loanBalance);
        } catch (Exception ex) {
            return 0d;
        }
    }

    public static List<PicklistItem> getDelayedItems() {
        PicklistRepository picklistRepository =
                Setup.getRepository(PicklistRepository.class);
        Picklist reasonOfPendingPicklist =
                picklistRepository.findByCode("reasonOfPending");

        PicklistItemRepository picklistItemRepository =
                Setup.getRepository(PicklistItemRepository.class);
        PicklistItem delayedOnSickWithoutClientPicklistItem
                = picklistItemRepository
                .findByListAndCodeIgnoreCase(reasonOfPendingPicklist,
                        "absent-sick_without_client");
        PicklistItem delayedOnReturningBackFromVacationPicklistItem
                = picklistItemRepository
                .findByListAndCodeIgnoreCase(reasonOfPendingPicklist,
                        "did_not_return_from_her_vacation");

        List<PicklistItem> delayItems = new ArrayList();
        if (delayedOnSickWithoutClientPicklistItem != null)
            delayItems.add(delayedOnSickWithoutClientPicklistItem);
        if (delayedOnReturningBackFromVacationPicklistItem != null)
            delayItems.add(delayedOnReturningBackFromVacationPicklistItem);

        return delayItems;
    }

    public static List<Long> getPaidMVMaidsIdsNewV2 (MonthlyPaymentRule monthlyPaymentRule, List<Long> transferredMaidsIds, Boolean forAudit) {
        java.sql.Date start = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).withDayOfMonth(1).toDate().getTime());
        java.sql.Date end = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).dayOfMonth().withMaximumValue().toDate().getTime());

        java.sql.Date startOfPreCollectedSalary = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(1).toDate().getTime());
        java.sql.Date endOfPreCollectedSalary = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).dayOfMonth().withMaximumValue().toDate().getTime());

        Double vatPercentage = 1.0 + (Integer.parseInt(Setup.getParameter(Setup.getModule("sales"),
                PayrollManagementModule.PARAMETER_SALES_VAT_PERCENT)) * 1.0 / 100);
        Long monthlyPaymentPickListItemId = PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId();

        //if we are getting paid MV maids for Audit then don't take into consideration MOL Condition
        Boolean withMol = forAudit ? null : monthlyPaymentRule.withOrWithoutMOL();
        List<HousemaidContractProjection> allMaidVisaMaidsWithActiveContracts;
        List<HousemaidContractProjection> allMaidVisaMaidsWithoutActiveContracts;
        List<Long> notNeededMaidsIds;
        if(monthlyPaymentRule != null && monthlyPaymentRule.getId() != null && PayrollType.SECONDARY.equals(monthlyPaymentRule.getPayrollType()) && transferredMaidsIds.size() > 0) {
            allMaidVisaMaidsWithActiveContracts = Setup.getRepository(HousemaidRepository.class).getTargetMVMaidsWithoutTransferredV2(HousemaidStatus.EMPLOYEMENT_TERMINATED, monthlyPaymentRule.getPayrollMonth(), withMol, transferredMaidsIds, ContractStatus.ACTIVE);
            notNeededMaidsIds = allMaidVisaMaidsWithActiveContracts.stream().map(x -> x.getHousemaidId()).collect(Collectors.toList());
            notNeededMaidsIds.addAll(transferredMaidsIds);
        } else {
            allMaidVisaMaidsWithActiveContracts = Setup.getRepository(HousemaidRepository.class).getTargetMVMaidsV2(HousemaidStatus.EMPLOYEMENT_TERMINATED, monthlyPaymentRule.getPayrollMonth(), withMol, ContractStatus.ACTIVE);
            notNeededMaidsIds = allMaidVisaMaidsWithActiveContracts.stream().map(x -> x.getHousemaidId()).collect(Collectors.toList());
        }

        allMaidVisaMaidsWithoutActiveContracts = Setup.getRepository(HousemaidRepository.class).getTargetMVMaidsFromContractRevision(HousemaidStatus.EMPLOYEMENT_TERMINATED.toString(), monthlyPaymentRule.getPayrollMonth(), withMol, notNeededMaidsIds);
        List<Long> normallyPaidHousemaidIds = new ArrayList<>();
        List<Long> cancelledContractsPaidHousemaidIds = new ArrayList<>();
//        DebugHelper.sendMail("<EMAIL>", "start getPaidMVMaidsIdsNew: for " + allMaidVisaMaids.size());
        Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, "allMaidVisaMaidsWithActiveContracts : " + allMaidVisaMaidsWithActiveContracts);
        Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, "allMaidVisaMaidsWithoutActiveContracts : " + allMaidVisaMaidsWithoutActiveContracts);

        //A- Active Contracts
        for(HousemaidContractProjection housemaidContractProjection : allMaidVisaMaidsWithActiveContracts) {
            List<BigInteger> paidIdsObj;

            // Check if the contract is 'preCollectedSalary'
            if(housemaidContractProjection.getPreCollectedSalary() != null && housemaidContractProjection.getPreCollectedSalary() == 1){
                //if start date = this month -> take the repayment < this month
                Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).getById(housemaidContractProjection.getHousemaidId());
                if((housemaid.getReplacementSalaryStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getReplacementSalaryStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)
                        ||(housemaid.getStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)){

                    paidIdsObj = Setup.getRepository(PaymentRepository.class)
                            .findPaidMaidVisaByHousemaidFor1stMonthAsPreCollectedContracts(ContractStatus.ACTIVE.toString(),
                                    monthlyPaymentPickListItemId,
                                    endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), vatPercentage, housemaidContractProjection.getHousemaidId());

                } else{ //That is not the 'start date' or 'replacement salary start date' month

                    paidIdsObj = Setup.getRepository(PaymentRepository.class)
                            .findPaidMaidVisaByHousemaid(ContractStatus.ACTIVE.toString(),
                                    monthlyPaymentPickListItemId,
                                    startOfPreCollectedSalary, endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), vatPercentage, housemaidContractProjection.getHousemaidId());
                }
            } else {
                paidIdsObj = Setup.getRepository(PaymentRepository.class)
                        .findPaidMaidVisaByHousemaid(ContractStatus.ACTIVE.toString(),
                                monthlyPaymentPickListItemId,
                                start, end, PaymentStatus.RECEIVED.toString(), vatPercentage, housemaidContractProjection.getHousemaidId());
            }

            if (paidIdsObj != null && paidIdsObj.size() > 0) {
                normallyPaidHousemaidIds.add(housemaidContractProjection.getHousemaidId());
            }
        }

        //B- Cancelled & Expired Contracts
        for(HousemaidContractProjection housemaidContractProjection : allMaidVisaMaidsWithoutActiveContracts) {

            Double basicSalary = housemaidContractProjection.getBasicSalary();
            basicSalary = basicSalary == null ? 0.0 : basicSalary;

            List<BigInteger> receivedPaymentsObj;

            // Check if the contract is 'preCollectedSalary'
            if(housemaidContractProjection.getPreCollectedSalary() != null && housemaidContractProjection.getPreCollectedSalary() == 1){
                //if start date = this month -> take the repayment < this month
                Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).getById(housemaidContractProjection.getHousemaidId());
                if((housemaid.getReplacementSalaryStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getReplacementSalaryStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)
                        ||(housemaid.getStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)) {

                    receivedPaymentsObj = Setup.getRepository(PaymentRepository.class).findPaidMaidVisaForCancelledContractsNewFor1stMonthAsPreCollectedContracts(monthlyPaymentPickListItemId,
                            endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), housemaidContractProjection.getContractId(), basicSalary, vatPercentage);

                } else {
                    receivedPaymentsObj = Setup.getRepository(PaymentRepository.class).findPaidMaidVisaForCancelledContractsNew(monthlyPaymentPickListItemId,
                            startOfPreCollectedSalary, endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), housemaidContractProjection.getContractId(), basicSalary, vatPercentage);
                }
            }else {
                receivedPaymentsObj = Setup.getRepository(PaymentRepository.class).findPaidMaidVisaForCancelledContractsNew(monthlyPaymentPickListItemId,
                        start, end, PaymentStatus.RECEIVED.toString(), housemaidContractProjection.getContractId(), basicSalary, vatPercentage);
            }

            if (receivedPaymentsObj != null && receivedPaymentsObj.size() > 0)
                cancelledContractsPaidHousemaidIds.add(housemaidContractProjection.getHousemaidId());
        }

        Logger.getLogger(HousemaidPayrollPaymentServiceV2.class.getName()).log(Level.SEVERE, "after getPaidMVMaidsIdsNew: normallyPaidHousemaidIds --> " + normallyPaidHousemaidIds + ", cancelledContractsPaidHousemaidIds --> " + cancelledContractsPaidHousemaidIds);
//        DebugHelper.sendMail("<EMAIL>", "after getPaidMVMaidsIdsNew: normallyPaidHousemaidIds --> " + normallyPaidHousemaidIds + ", cancelledContractsPaidHousemaidIds --> " + cancelledContractsPaidHousemaidIds);
        normallyPaidHousemaidIds.addAll(cancelledContractsPaidHousemaidIds);
        return normallyPaidHousemaidIds;
    }

    /**
     * get all MV maid info that can receive a salary
     * @param monthlyPaymentRule
     * @return
     */
    public static List<ExcludedMVInfo> getExcludedMVInfoList(MonthlyPaymentRule monthlyPaymentRule) {
//        List<Long> excludedMVMaidsIds = new ArrayList<>();
        List<ExcludedMVInfo> excludedMVInfoList = new ArrayList<>();
        Double vatPercentage = 1.0 + (Integer.parseInt(Setup.getParameter(Setup.getModule("sales"),
                PayrollManagementModule.PARAMETER_SALES_VAT_PERCENT)) * 1.0 / 100);

        java.sql.Date start = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).withDayOfMonth(1).toDate().getTime());
        java.sql.Date end = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).dayOfMonth().withMaximumValue().toDate().getTime());

        java.sql.Date startOfPreCollectedSalary = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(1).toDate().getTime());
        java.sql.Date endOfPreCollectedSalary = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).dayOfMonth().withMaximumValue().toDate().getTime());

//        Integer additionalToMaidVisaPayment = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
//                PayrollManagementModule.PARAMETER_ADDITIONAL_TO_MAID_VISA_PAYMENT));

        List<Long> transferredIds = Setup.getRepository(HousemaidPayrollLogRepository.class).
                findByPayrollMonthAndTransferredTrue(monthlyPaymentRule.getPayrollMonth());
        List<Long> alreadyMentionedBefore = Setup.getRepository(ExcludedMVInfoRepository.class).
                findHousemaidIdsByPayrollMonth(monthlyPaymentRule.getPayrollMonth());
        List<Long> notWantedMaidsIds = new ArrayList<>(transferredIds);
        notWantedMaidsIds.addAll(alreadyMentionedBefore);

        //startDate && replacementSalaryStartDate must be before 27 of the month
        java.util.Date dayOf27th =  new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate();

        //normal case (active contract with received payment less than salaries and fees)
        Set<ExcludedMVInfoProjection> normallyExcludedMVListInfo = notWantedMaidsIds.size() > 0 ?
                Setup.getRepository(PaymentRepository.class)
                .findUnPaidMaidVisaInfo(ContractStatus.ACTIVE.toString(), HousemaidType.MAID_VISA.toString(),
                        PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId(), start, end, PaymentStatus.RECEIVED.toString(), notWantedMaidsIds, monthlyPaymentRule.getPayrollMonth(), vatPercentage, dayOf27th, "false")
                :Setup.getRepository(PaymentRepository.class)
                .findUnPaidMaidVisaInfo(ContractStatus.ACTIVE.toString(), HousemaidType.MAID_VISA.toString(),
                        PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId(), start, end, PaymentStatus.RECEIVED.toString(), monthlyPaymentRule.getPayrollMonth(), vatPercentage, dayOf27th, "false");
//        List<Long> normallyExcludedMVListIds = normallyExcludedMVListInfo.stream().map(x -> x.getHousemaidId()).collect(Collectors.toList());

        Set<ExcludedMVInfoProjection> normallyExcludedMVListInfoPreCollected = notWantedMaidsIds.size() > 0 ?
                Setup.getRepository(PaymentRepository.class)
                        .findUnPaidMaidVisaInfo(ContractStatus.ACTIVE.toString(), HousemaidType.MAID_VISA.toString(),
                                PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId(), startOfPreCollectedSalary, endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), notWantedMaidsIds, monthlyPaymentRule.getPayrollMonth(), vatPercentage, dayOf27th, "true")
                :Setup.getRepository(PaymentRepository.class)
                .findUnPaidMaidVisaInfo(ContractStatus.ACTIVE.toString(), HousemaidType.MAID_VISA.toString(),
                        PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId(), startOfPreCollectedSalary, endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), monthlyPaymentRule.getPayrollMonth(), vatPercentage, dayOf27th, "true");

        Set<ExcludedMVInfoProjection> normallyExcludedMVListInfoPreCollected1stMonth = notWantedMaidsIds.size() > 0 ?
                Setup.getRepository(PaymentRepository.class)
                        .findUnPaidMaidVisaInfoFor1stMonthAsPreCollectedContracts(ContractStatus.ACTIVE.toString(), HousemaidType.MAID_VISA.toString(),
                                PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId(), endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), notWantedMaidsIds, monthlyPaymentRule.getPayrollMonth(), vatPercentage, dayOf27th)
                :Setup.getRepository(PaymentRepository.class)
                .findUnPaidMaidVisaInfoFor1stMonthAsPreCollectedContracts(ContractStatus.ACTIVE.toString(), HousemaidType.MAID_VISA.toString(),
                        PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId(), end, PaymentStatus.RECEIVED.toString(), monthlyPaymentRule.getPayrollMonth(), vatPercentage, dayOf27th);

        normallyExcludedMVListInfo.addAll(normallyExcludedMVListInfoPreCollected);
        normallyExcludedMVListInfo.addAll(normallyExcludedMVListInfoPreCollected1stMonth);

        //todo: check with Hakiim - any updates needed? as we don't sent start & end date in the parameters
        List<ExcludedMVInfoProjection> previousPayrollLogsInfo = Setup.getRepository(PaymentRepository.class)
                .findUnPaidMaidVisaInfoForPreviousLogs(ContractStatus.ACTIVE.toString(), HousemaidType.MAID_VISA.toString(),
                    PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId(), PaymentStatus.RECEIVED.toString(), monthlyPaymentRule.getPayrollMonth(), vatPercentage, dayOf27th);
        normallyExcludedMVListInfo.addAll(previousPayrollLogsInfo);

        for (ExcludedMVInfoProjection projection : normallyExcludedMVListInfo) {
            excludedMVInfoList.add(new ExcludedMVInfo(projection));
        }
//        DebugHelper.sendMail("<EMAIL>", "after getExcludedMVInfoList: \n normallyExcludedMVListIds --> " + excludedMVInfoList);



        //not regular case (terminated contract and date of termination is in the same month with recieved payment
        List<Long> housemaidWithActiveContracts = Setup.getRepository(PaymentRepository.class).getHousemaidWithActiveContracts(ContractStatus.ACTIVE, HousemaidType.MAID_VISA);
        List<Long> oldTerminatedMVMaids = Setup.getRepository(HousemaidRepository.class).getOldTerminatedMVMaids(HousemaidStatus.EMPLOYEMENT_TERMINATED, monthlyPaymentRule.getPayrollMonth());

        notWantedMaidsIds.addAll(housemaidWithActiveContracts);
        notWantedMaidsIds.addAll(oldTerminatedMVMaids);

        List<Long> housemaidWithoutActiveContracts = Setup.getRepository(HousemaidRepository.class)
                .getHousemaidsWithNoActiveContract(notWantedMaidsIds);

        Long monthlyPaymentPickListItemId = PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId();
//        List<Long> cancelledContractsExcludedHousemaidIds = new ArrayList<>();
        if(!housemaidWithoutActiveContracts.isEmpty()) {
            for(Long housemaidId : housemaidWithoutActiveContracts) {
                Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(housemaidId);
                Double basicSalary = housemaid.getBasicSalary();
                basicSalary = basicSalary == null ? 0.0 : basicSalary;

                HistorySelectQuery<Contract> historySelectQuery = new HistorySelectQuery<>(Contract.class);
                historySelectQuery.filterBy("housemaid", "=", housemaid);
                historySelectQuery.filterBy("dateOfTermination", ">=", start);
                historySelectQuery.filterBy("status", "IN", Arrays.asList(ContractStatus.EXPIRED, ContractStatus.CANCELLED));
                historySelectQuery.sortBy("lastModificationDate",false);
                historySelectQuery.setLimit(1);
                List<Contract> contracts = historySelectQuery.execute();
                if (contracts != null && contracts.size() > 0) {
                    Contract contract = Setup.getRepository(ContractRepository.class).findOne(contracts.get(0).getId());
                    if (contract != null) {
                        List<BigInteger> receivedPaymentsObj;

                        // Check if the contract is 'preCollectedSalary'
                        if(contract.hasBaseAdditionalInfo("preCollectedSalary") && contract.getBaseAdditionalInfoValue("preCollectedSalary").equals("true")) {

                            //if start date = this month -> take the repayment < this month
                            if((housemaid.getReplacementSalaryStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getReplacementSalaryStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)
                                    ||(housemaid.getStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)) {

                                receivedPaymentsObj = Setup.getRepository(PaymentRepository.class).findExcludedMaidVisaForCancelledContractsFor1stMonthAsPreCollectedContracts(monthlyPaymentPickListItemId,
                                        startOfPreCollectedSalary, endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), contract.getId(), basicSalary, vatPercentage);

                            } else {
                                receivedPaymentsObj = Setup.getRepository(PaymentRepository.class).findExcludedMaidVisaForCancelledContracts(monthlyPaymentPickListItemId,
                                        startOfPreCollectedSalary, endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), contract.getId(), basicSalary, vatPercentage);
                            }

                        } else {
                            receivedPaymentsObj = Setup.getRepository(PaymentRepository.class).findExcludedMaidVisaForCancelledContracts(monthlyPaymentPickListItemId,
                                    start, end, PaymentStatus.RECEIVED.toString(), contract.getId(), basicSalary, vatPercentage);
                        }
                        List<Long> receivedPayments = receivedPaymentsObj.stream().map(x -> x != null ? x.longValue() : null).collect(Collectors.toList());
                        if (receivedPayments.size() > 0) {
//                            cancelledContractsExcludedHousemaidIds.add(housemaidId);
                            List<ExcludedMVInfoProjection> excludedMVInfoProjections = Setup.getRepository(PaymentRepository.class).findUnPaidMaidVisaInfoForCancelled(housemaidId, housemaid.getName(), basicSalary,monthlyPaymentPickListItemId, start, end, PaymentStatus.RECEIVED.toString(), contract.getId(), monthlyPaymentRule.getPayrollMonth());
                            if (excludedMVInfoProjections.size() > 0){
                                excludedMVInfoList.add(new ExcludedMVInfo(excludedMVInfoProjections.get(0)));
                            }
                        }
                    }
                }
            }
        }

//        DebugHelper.sendMail("<EMAIL>", "after getExcludedMVInfoList: cancelledContractsExcludedHousemaidIds --> " + excludedMVInfoList);
//        normallyExcludedMVListIds.addAll(cancelledContractsExcludedHousemaidIds);
        return excludedMVInfoList;
    }

    public static HousemaidBeanInfo createBeanInfoDetails (Housemaid housemaid, HousemaidPayrollLog log){
        HousemaidBeanInfo housemaidBeanInfo = new HousemaidBeanInfo();

        housemaidBeanInfo.setHousemaid(housemaid);
        housemaidBeanInfo.setHousemaidPayrollLog(log);
        housemaidBeanInfo.setEmployeeUniqueId(log.getEmployeeUniqueId());
        housemaidBeanInfo.setAgentId(log.getAgentId());
        housemaidBeanInfo.setEmployeeAccountWithAgent(log.getEmployeeAccountWithAgent());
        housemaidBeanInfo.setHousemaidName(housemaid.getName());
        housemaidBeanInfo.setStartingDate(housemaid.getStartDate() != null ? new Date(housemaid.getStartDate().getTime()) : null);

        List<HousemaidDateProjection> housemaidRenewalDates = Setup.getRepository(RenewVisaRequestRepository.class).findRenewalDateForSingleHousemaid(housemaid.getId());
        for(HousemaidDateProjection projection: housemaidRenewalDates) {
            housemaidBeanInfo.setRenewalDate(projection.getDate() != null ? new Date(projection.getDate().getTime()) : null);
        }
        housemaidBeanInfo.setArrivalDate(housemaid.getLandedInDubaiDate() != null ? new Date(housemaid.getLandedInDubaiDate().getTime()) : null);
        housemaidBeanInfo.setNationality(housemaid.getNationality() != null ? housemaid.getNationality().getName() : "");

        List<HousemaidFieldProjection> housemaidClientNames = Setup.getRepository(ContractRepository.class).findClientNames(Arrays.asList(housemaid.getId()));
        for(HousemaidFieldProjection projection: housemaidClientNames) {
            housemaidBeanInfo.setClientName(projection.getField());
        }
        List<HousemaidContractInfoProjection> housemaidContractInfo = Setup.getRepository(ContractRepository.class).findContractInfo(Arrays.asList(housemaid.getId()));
        for(HousemaidContractInfoProjection projection: housemaidContractInfo) {
            if(projection.getContractId() != null)
                housemaidBeanInfo.setContractName("Contr-" + projection.getContractId().toString());
            if (projection.getContractType() != null && projection.getContractType().equals("maidvisa.ae_prospect")) {
                housemaidBeanInfo.setMaidVisaAEContract("Yes");
            } else {
                housemaidBeanInfo.setMaidVisaAEContract("No");
            }
        }

        housemaidBeanInfo.setStatus(housemaid.getStatus().toString());
        housemaidBeanInfo.setSource(housemaid.isIsAgency() ? "Agency" : ((housemaid.getFreedomMaid() != null && housemaid.getFreedomMaid()) ? "Freedom Operator" : "Exit"));
        housemaidBeanInfo.setFreedomOperatorName((housemaid.getFreedomMaid() != null && housemaid.getFreedomMaid() && housemaid.getFreedomOperator() != null) ? housemaid.getFreedomOperator().getName() : "");

        //Living place && Food allowance
        if (housemaid.getLiving() == HousemaidLiveplace.OUT) {
            housemaidBeanInfo.setLiving(HousemaidLiveplace.OUT);
        } else {
            housemaidBeanInfo.setLiving(HousemaidLiveplace.IN);
        }

        //Company Accommodate
        if (housemaid.getHousingAllowance() != null && housemaid.getHousingAllowance() > 0) {
            housemaidBeanInfo.setCompanyAccommodated("No");
        } else {
            housemaidBeanInfo.setCompanyAccommodated("Yes");
        }

        housemaidBeanInfo.setTotatIcome(log.getTotalSalary());

        return housemaidBeanInfo;
    }

    public List<Housemaid> getOnHoldMaids(PayrollAccountantTodo payrollAccountantTodo) {
        if (!payrollAccountantTodo.getMonthlyPaymentRule().isTargetingHousemaid() || payrollAccountantTodo.getSingleHousemaid()) return new ArrayList<>();

        SelectQuery<HousemaidPayrollLog> housemaidPayrollLogQuery = new SelectQuery<>(HousemaidPayrollLog.class);
        housemaidPayrollLogQuery.filterBy("payrollMonth", "=", payrollAccountantTodo.getPayrollMonth());
        housemaidPayrollLogQuery.filterBy("transferred", "=", true);
        List<Long> housemaidIds = housemaidPayrollLogQuery.execute().stream().map(x -> x.getHousemaid().getId()).collect(Collectors.toList());

        SelectQuery<Housemaid> baseQuery = new SelectQuery<>(Housemaid.class);
        baseQuery.filterBy("housemaidType", "<>", HousemaidType.MAID_VISA);
        baseQuery.filterBy(new SelectFilter(new SelectFilter("status", "=", HousemaidStatus.NO_SHOW)
                .or("status", "=", HousemaidStatus.ON_VACATION)));

        if (PayrollAccountantTodoType.valueOf(payrollAccountantTodo.getTaskName()).equals(PayrollAccountantTodoType.WPS))
            baseQuery.filterBy("withMolNumber", "=", true);
        else
            baseQuery.filterBy("withMolNumber", "=", false);

        // Not excluded from payroll
        SelectFilter notExcludedFromPayroll = new SelectFilter(new SelectFilter("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null));

        baseQuery.filterBy(notExcludedFromPayroll);
        if(housemaidIds != null && housemaidIds.size() > 0)
            baseQuery.filterBy("id", "NOT IN", housemaidIds);

        baseQuery.sortBy("name", true);
        return baseQuery.execute();
    }

    public List<Payment> getAllPaymentForHousemaid (MonthlyPaymentRule monthlyPaymentRule, Housemaid housemaid) {
        java.sql.Date start = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).withDayOfMonth(1).toDate().getTime());
        java.sql.Date end = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).dayOfMonth().withMaximumValue().toDate().getTime());
        Double vatPercentage = 1.0 + (Integer.parseInt(Setup.getParameter(Setup.getModule("sales"),
                PayrollManagementModule.PARAMETER_SALES_VAT_PERCENT)) * 1.0 / 100);
        if (housemaid != null) {
            Contract contract = housemaid.getActiveContract();
            Long monthlyPaymentPickListItemId = PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId();
            //A- Active Contracts
            if (contract != null) {
                PaymentRepository paymentRepo = Setup.getRepository(PaymentRepository.class);
                List<BigInteger> paidIdsObj = paymentRepo
                        .findAllPaymentByHousemaid(ContractStatus.ACTIVE.toString(),
                                monthlyPaymentPickListItemId,
                                start, end, housemaid.getId());

                if (paidIdsObj != null && paidIdsObj.size() > 0) {
                    List<Long> idsForPayments = paidIdsObj.stream().map(x -> x.longValue()).collect(Collectors.toList());
                    return paymentRepo.getAllByIds(idsForPayments);
                }
            }
        }
        return new ArrayList<>();
    }

    public java.util.Date getAuditTodoClosingDate(Date payrollMonth){
        PayrollAuditTodo auditTodo = payrollAuditTodoRepository.findTopByPayrollMonthAndTaskName(payrollMonth, PayrollAuditTodoTaskNameType.HOUSEMAIDS.toString());

        if (auditTodo == null || auditTodo.getCompleted() == null || !auditTodo.getCompleted())
            return new java.util.Date();
        return auditTodo.getLastMoveDate();
    }

    public Map<String, Object> getReceivedPaymentForHousemaid (MonthlyPaymentRule monthlyPaymentRule, Housemaid housemaid) {
        Map<String, Object> resultMap = new HashMap<>();
        java.sql.Date start = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).withDayOfMonth(1).toDate().getTime());
        java.sql.Date end = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).dayOfMonth().withMaximumValue().toDate().getTime());

        java.sql.Date startOfPreCollectedSalary = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(1).toDate().getTime());
        java.sql.Date endOfPreCollectedSalary = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).dayOfMonth().withMaximumValue().toDate().getTime());

        Double vatPercentage = 1.0 + (Integer.parseInt(Setup.getParameter(Setup.getModule("sales"),
                PayrollManagementModule.PARAMETER_SALES_VAT_PERCENT)) * 1.0 / 100);
        if(housemaid != null){
            Contract contract = housemaid.getActiveContract();
            Long monthlyPaymentPickListItemId = PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId();
            //A- Active Contracts
            if (contract != null){
                PaymentRepository paymentRepo = Setup.getRepository(PaymentRepository.class);
                List<Map<String, Object>> paidIdsObj;

                // Check if the contract is 'preCollectedSalary'
                if(contract.hasBaseAdditionalInfo("preCollectedSalary") && contract.getBaseAdditionalInfoValue("preCollectedSalary").equals("true")) {

                    //if start date = this month -> take the repayment < this month
                    if((housemaid.getReplacementSalaryStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getReplacementSalaryStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)
                            ||(housemaid.getStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)) {

                        paidIdsObj = paymentRepo
                                .findReceivedPaymentByHousemaidFor1stMonthAsPreCollectedContracts(ContractStatus.ACTIVE.toString(),
                                        monthlyPaymentPickListItemId,
                                        endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), vatPercentage, housemaid.getId());

                    } else {
                        paidIdsObj = paymentRepo
                                .findReceivedPaymentByHousemaid(ContractStatus.ACTIVE.toString(),
                                        monthlyPaymentPickListItemId,
                                        startOfPreCollectedSalary, endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), vatPercentage, housemaid.getId());

                    }
                } else {
                    paidIdsObj = paymentRepo
                            .findReceivedPaymentByHousemaid(ContractStatus.ACTIVE.toString(),
                                    monthlyPaymentPickListItemId,
                                    start, end, PaymentStatus.RECEIVED.toString(), vatPercentage, housemaid.getId());
                }

                if (paidIdsObj != null && paidIdsObj.size()>0) {
                    BigInteger paidPaymentId = (BigInteger) paidIdsObj.get(0).get("id");
                    Integer cases = (Integer) paidIdsObj.get(0).get("cases");
                    resultMap.put("payment", paymentRepo.getById(paidPaymentId.longValue()));
                    resultMap.put("cases", cases);
                    return resultMap;
                }
            }
            //B- Cancelled & Expired Contracts
            else {
                Double basicSalary = housemaid.getBasicSalary();
                basicSalary = basicSalary == null ? 0.0 : basicSalary;

                HistorySelectQuery<Contract> historySelectQuery = new HistorySelectQuery<>(Contract.class);
                historySelectQuery.filterBy("housemaid", "=", housemaid);
                historySelectQuery.filterBy("dateOfTermination", ">=", start);
                historySelectQuery.filterBy("status", "IN", Arrays.asList(ContractStatus.EXPIRED, ContractStatus.CANCELLED));
                historySelectQuery.sortBy("lastModificationDate",false);
                historySelectQuery.setLimit(1);
                List<Contract> contracts = historySelectQuery.execute();
                if (contracts != null && contracts.size() > 0) {
                    contract = Setup.getRepository(ContractRepository.class).findOne(contracts.get(0).getId());
                    if (contract != null) {
                        PaymentRepository paymentRepo = Setup.getRepository(PaymentRepository.class);
                        List<Map<String, Object>> receivedPaymentsObj;

                        // Check if the contract is 'preCollectedSalary'
                        if(contract.hasBaseAdditionalInfo("preCollectedSalary") && contract.getBaseAdditionalInfoValue("preCollectedSalary").equals("true")) {

                            //if start date = this month -> take the repayment < this month
                            if((housemaid.getReplacementSalaryStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getReplacementSalaryStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)
                                    ||(housemaid.getStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)) {

                                receivedPaymentsObj = paymentRepo
                                        .findPaidMaidVisaForCancelledContractsWithCasesFor1stMonthAsPreCollectedContracts(monthlyPaymentPickListItemId,
                                                endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), contract.getId(), basicSalary, vatPercentage);

                            } else {
                                receivedPaymentsObj = paymentRepo
                                        .findPaidMaidVisaForCancelledContractsWithCases(monthlyPaymentPickListItemId,
                                                startOfPreCollectedSalary, endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), contract.getId(), basicSalary, vatPercentage);

                            }
                        } else {
                            receivedPaymentsObj = paymentRepo
                                    .findPaidMaidVisaForCancelledContractsWithCases(monthlyPaymentPickListItemId,
                                            start, end, PaymentStatus.RECEIVED.toString(), contract.getId(), basicSalary, vatPercentage);
                        }
                        if (receivedPaymentsObj!= null && receivedPaymentsObj.size() > 0) {
                            BigInteger paidPaymentId = (BigInteger) receivedPaymentsObj.get(0).get("id");
                            int cases = (Integer) receivedPaymentsObj.get(0).get("cases");
                            resultMap.put("payment", paymentRepo.getById(paidPaymentId.longValue()));
                            resultMap.put("cases", cases);
                            return resultMap;
                        }
                    }
                }
            }
        }

        return null;
    }

    public static List<Map<String, Object>> getPaymentsRelatedToMaid(MonthlyPaymentRule monthlyPaymentRule, List<HousemaidPayrollLogProjection> housemaids) {
        java.sql.Date start = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).withDayOfMonth(1).toDate().getTime());
        java.sql.Date end = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).dayOfMonth().withMaximumValue().toDate().getTime());
        Double vatPercentage = 1.0 + (Integer.parseInt(Setup.getParameter(Setup.getModule("sales"),
                PayrollManagementModule.PARAMETER_SALES_VAT_PERCENT)) * 1.0 / 100);

        List<Map<String, Object>> result = new ArrayList<>();
        Long monthlyPaymentPickListItemId = PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId();
        for (HousemaidPayrollLogProjection housemaidPayrollLogProjection : housemaids) {
            Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(housemaidPayrollLogProjection.getHousemaid());
            if (housemaid == null) {
                continue;
            }
            Contract contract = housemaid.getActiveContract();

            //A- Active Contracts
            if (contract != null) {
                List<BigInteger> paidIdsObj = Setup.getRepository(PaymentRepository.class)
                        .findPaymentPaidMaidVisaByHousemaid(ContractStatus.ACTIVE.toString(),
                                monthlyPaymentPickListItemId,
                                start, end, PaymentStatus.RECEIVED.toString(), vatPercentage, housemaid.getId());

                if (paidIdsObj != null && paidIdsObj.size() > 0) {
                    Map<String, Object> element = new HashMap<>();
                    element.put("housemaidId", housemaidPayrollLogProjection.getHousemaid());
                    element.put("housemaidPayrollLogId", housemaidPayrollLogProjection.getPayrollLog());
                    element.put("paymentIds", paidIdsObj);
                    result.add(element);
                }
            }
            //B- Cancelled & Expired Contracts
            else {
                Double basicSalary = housemaid.getBasicSalary();
                basicSalary = basicSalary == null ? 0.0 : basicSalary;

                HistorySelectQuery<Contract> historySelectQuery = new HistorySelectQuery<>(Contract.class);
                historySelectQuery.filterBy("housemaid", "=", housemaid);
                historySelectQuery.filterBy("dateOfTermination", ">=", start);
                historySelectQuery.filterBy("status", "IN", Arrays.asList(ContractStatus.EXPIRED, ContractStatus.CANCELLED));
                historySelectQuery.sortBy("lastModificationDate", false);
                historySelectQuery.setLimit(1);
                List<Contract> contracts = historySelectQuery.execute();
                if (contracts != null && contracts.size() > 0) {
                    contract = Setup.getRepository(ContractRepository.class).findOne(contracts.get(0).getId());
                    if (contract != null) {
                        List<BigInteger> receivedPaymentsObj = Setup.getRepository(PaymentRepository.class).findPaidMaidVisaForCancelledContractsNew(monthlyPaymentPickListItemId,
                                start, end, PaymentStatus.RECEIVED.toString(), contract.getId(), basicSalary, vatPercentage);
                        if (receivedPaymentsObj != null && receivedPaymentsObj.size() > 0) {
                            Map<String, Object> element = new HashMap<>();
                            element.put("housemaidId", housemaidPayrollLogProjection.getHousemaid());
                            element.put("housemaidPayrollLogId", housemaidPayrollLogProjection.getPayrollLog());
                            element.put("paymentIds", receivedPaymentsObj);
                            result.add(element);
                        }

                    }
                }
            }

        }

        return result;
    }

    public Map<String, Object> getReceivedPaymentForHousemaidForMonth(MonthlyPaymentRule monthlyPaymentRule, Housemaid housemaid, Contract contract) {
        Map<String, Object> resultDate = new HashMap<>();
        java.sql.Date start = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).withDayOfMonth(1).toDate().getTime());
        java.sql.Date end = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).dayOfMonth().withMaximumValue().toDate().getTime());

        java.sql.Date startOfPreCollectedSalary = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(1).toDate().getTime());
        java.sql.Date endOfPreCollectedSalary = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).dayOfMonth().withMaximumValue().toDate().getTime());

        Double vatPercentage = 1.0 + (Integer.parseInt(Setup.getParameter(Setup.getModule("sales"),
                PayrollManagementModule.PARAMETER_SALES_VAT_PERCENT)) * 1.0 / 100);
        if (housemaid != null && contract != null) {
            Long monthlyPaymentPickListItemId = PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId();
            //A- Active Contracts & Cancelled Contract

            List<Map<String, Object>> receivedPaymentsObj;
            PaymentRepository paymentRepo = Setup.getRepository(PaymentRepository.class);
            Double basicSalary = housemaid.getBasicSalary();
            basicSalary = basicSalary == null ? 0.0 : basicSalary;

            // Check if the contract is 'preCollectedSalary'
            if(contract.hasBaseAdditionalInfo("preCollectedSalary") && contract.getBaseAdditionalInfoValue("preCollectedSalary").equals("true")) {

                //if start date = this month -> take the repayment < this month
                if((housemaid.getReplacementSalaryStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getReplacementSalaryStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)
                        ||(housemaid.getStartDate() != null && DateUtil.getDiffMonths(DateUtil.getFirstOfMonthDate(housemaid.getStartDate()), DateUtil.getFirstOfMonthDate(startOfPreCollectedSalary)) == 0)) {

                    receivedPaymentsObj = paymentRepo
                            .findPaidMaidVisaForCancelledContractsWithoutSalaryValidationFor1stMonthAsPreCollectedContracts(monthlyPaymentPickListItemId,
                                    endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), contract.getId(), basicSalary, vatPercentage);

                } else {
                    receivedPaymentsObj = paymentRepo
                            .findPaidMaidVisaForCancelledContractsWithoutSalaryValidation(monthlyPaymentPickListItemId,
                                    startOfPreCollectedSalary, endOfPreCollectedSalary, PaymentStatus.RECEIVED.toString(), contract.getId(), basicSalary, vatPercentage);

                }
            } else {
                receivedPaymentsObj = paymentRepo
                        .findPaidMaidVisaForCancelledContractsWithoutSalaryValidation(monthlyPaymentPickListItemId,
                                start, end, PaymentStatus.RECEIVED.toString(), contract.getId(), basicSalary, vatPercentage);
            }
            if (receivedPaymentsObj != null && receivedPaymentsObj.size() > 0) {
                BigInteger paidPaymentId = (BigInteger) receivedPaymentsObj.get(0).get("id");
                String note = (String) receivedPaymentsObj.get(0).get("note");
                resultDate.put("payment", paymentRepo.findOne(paidPaymentId.longValue()));
                resultDate.put("note", note);
                return resultDate;
            }
        }

        return null;
    }

}
