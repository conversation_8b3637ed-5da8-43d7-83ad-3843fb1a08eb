package com.magnamedia.service.payroll.generation.newversion;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.entity.HousemaidPayrollBean;
import com.magnamedia.entity.OfficeStaffPayrollBean;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.extra.payroll.init.HousemaidPayrollInitializer;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.helper.StringHelper;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.salarycalculation.HousemaidSalaryTransaction;
import com.magnamedia.service.NegativeSalariesService;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.OfficeStaffPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.PayrollExceptionsReportService;
import org.joda.time.LocalDate;
import org.reflections.Reflections;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class AuditFilesService {

    private final OfficeStaffPayrollAuditService officeStaffPayrollAuditService;
    private final HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2;
    private final HousemaidPayrollAuditService housemaidPayrollAuditService;
    private final OfficeStaffPayrollPaymentServiceV2 officeStaffPayrollPaymentServiceV2;
    private final TransferFilesService transferFilesService;
    private final ProRatedSalariesService proRatedSalariesService;
    private final NegativeSalariesService negativeSalariesService;

    public AuditFilesService(OfficeStaffPayrollAuditService officeStaffPayrollAuditService, HousemaidPayrollAuditService housemaidPayrollAuditService,
                             OfficeStaffPayrollPaymentServiceV2 officeStaffPayrollPaymentServiceV2, TransferFilesService transferFilesService,
                             HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2, ProRatedSalariesService proRatedSalariesService,
                             NegativeSalariesService negativeSalariesService) {
        this.officeStaffPayrollAuditService = officeStaffPayrollAuditService;
        this.housemaidPayrollAuditService = housemaidPayrollAuditService;
        this.officeStaffPayrollPaymentServiceV2 = officeStaffPayrollPaymentServiceV2;
        this.transferFilesService = transferFilesService;
        this.housemaidPayrollPaymentServiceV2 = housemaidPayrollPaymentServiceV2;
        this.proRatedSalariesService = proRatedSalariesService;
        this.negativeSalariesService = negativeSalariesService;
    }

    public Attachment generatePayrollAuditDetailFile(MonthlyPaymentRule rule, PayrollAuditTodo auditTodo, PaymentRuleEmployeeType employeeType) {
        rule = Setup.getRepository(MonthlyPaymentRuleRepository.class).findOne(rule.getId());

        if(employeeType == PaymentRuleEmployeeType.HOUSEMAIDS) {

            List<Housemaid> housemaids = housemaidPayrollAuditService.getTargetList(rule);
//            DebugHelper.sendMail("<EMAIL>", "generatePayrollAuditDetailFile for housemaids list: " + housemaids.size());
            housemaidPayrollPaymentServiceV2.clearHousemaidLogsForChangedAndRecreate(housemaids, rule, auditTodo, null, false, true);
            List<HousemaidPayrollLog> includedHousemaidPayrollLogs = housemaidPayrollPaymentServiceV2.getHousemaidPayrollLogsBasedOnAll(housemaids, rule, auditTodo, null);
            List<HousemaidPayrollLog> maidVisaLogsForPreviousMonths = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getMVPayrollLogsForPreviousMonthsNew(rule, null, false);
            Map<Long, List<HousemaidPayrollLog>> maidVisaLogsForPreviousMonthsMap = PayrollGenerationLibrary.convertListToMap(maidVisaLogsForPreviousMonths);
            return generateHousemaidFinalDetailedPayrollFileNew(includedHousemaidPayrollLogs, rule, maidVisaLogsForPreviousMonthsMap, null, false);
//            return generateHousemaidDetailedPayrollFile(housemaids, rule);
        } else {

            List<OfficeStaff> staffs = officeStaffPayrollAuditService.getTargetList(rule, employeeType);
            staffs.addAll(officeStaffPayrollAuditService.getTerminatedList(rule, employeeType));
            List<OfficeStaff> terminatedStaff = officeStaffPayrollAuditService.getTerminatedListForDetailedFile(rule, employeeType);
            List<OfficeStaff> excludedStaff = officeStaffPayrollAuditService.getExcludedList(rule, employeeType);

//            DebugHelper.sendMail("<EMAIL>", "generatePayrollAuditDetailFile for Office Staffs, active list: " + staffs.size() + ", terminated list: " + terminatedStaff.size() + ", excluded list: " + excludedStaff.size());
            staffs.sort(Comparator.comparing(OfficeStaff::getName));
            terminatedStaff.sort(Comparator.comparing(OfficeStaff::getName));
            excludedStaff.sort(Comparator.comparing(OfficeStaff::getName));
            List<Long> staffsIds = staffs.stream().map(BaseEntity::getId).collect(Collectors.toList());
            List<Long> terminatedStaffsIds = terminatedStaff.stream().map(BaseEntity::getId).collect(Collectors.toList());
            List<Long> excludedStaffsIds = excludedStaff.stream().map(BaseEntity::getId).collect(Collectors.toList());

            officeStaffPayrollPaymentServiceV2.clearOfficeStaffLogsForChangedAndRecreate(staffs, rule, auditTodo, null, false);
            officeStaffPayrollPaymentServiceV2.clearOfficeStaffLogsForChangedAndRecreate(terminatedStaff, rule, auditTodo, null, false);
            officeStaffPayrollPaymentServiceV2.clearOfficeStaffLogsForChangedAndRecreate(excludedStaff, rule, auditTodo, null, false);

            List<OfficeStaffPayrollLog> includedOfficeStaffPayrollLogs = officeStaffPayrollPaymentServiceV2.getOfficeStaffPayrollLogsBasedOnAll(staffsIds, rule, auditTodo, null);
            List<OfficeStaffPayrollLog> terminatedOfficeStaffPayrollLogs = officeStaffPayrollPaymentServiceV2.getOfficeStaffPayrollLogsBasedOnAll(terminatedStaffsIds, rule, auditTodo, null);
            List<OfficeStaffPayrollLog> excludedOfficeStaffPayrollLogs = officeStaffPayrollPaymentServiceV2.getOfficeStaffPayrollLogsBasedOnAll(excludedStaffsIds, rule, auditTodo, null);

            return generateOfficeStaffFinalDetailedPayrollFile(includedOfficeStaffPayrollLogs, terminatedOfficeStaffPayrollLogs, excludedOfficeStaffPayrollLogs, rule, null, false);

//            return generateOfficeStaffDetailedPayrollFile(staffs, terminatedStaff, excludedStaff, rule);
        }
    }

    public List<Attachment> generatePayrollAuditDetailFileRevamp(MonthlyPaymentRule rule) { //detailed file
        List<Attachment> detailedFiles = new ArrayList<>();
        rule = Setup.getRepository(MonthlyPaymentRuleRepository.class).findOne(rule.getId());

        for (PaymentRuleEmployeeType employeeType : rule.getEmployeeTypeList()) {
            if (employeeType == PaymentRuleEmployeeType.HOUSEMAIDS) {

                List<Housemaid> housemaids = housemaidPayrollAuditService.getTargetList(rule);
//            DebugHelper.sendMail("<EMAIL>", "generatePayrollAuditDetailFile for housemaids list: " + housemaids.size());
                housemaidPayrollPaymentServiceV2.clearHousemaidLogsForChangedAndRecreate(housemaids, rule, null, null, false, true);
                List<HousemaidPayrollLog> includedHousemaidPayrollLogs = housemaidPayrollPaymentServiceV2.getHousemaidPayrollLogsBasedOnAll(housemaids, rule, null, null);
                List<HousemaidPayrollLog> maidVisaLogsForPreviousMonths = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getMVPayrollLogsForPreviousMonthsNew(rule, null, false);
                Map<Long, List<HousemaidPayrollLog>> maidVisaLogsForPreviousMonthsMap = PayrollGenerationLibrary.convertListToMap(maidVisaLogsForPreviousMonths);
                detailedFiles.add(generateHousemaidFinalDetailedPayrollFileNew(includedHousemaidPayrollLogs, rule, maidVisaLogsForPreviousMonthsMap, null, false));
//            return generateHousemaidDetailedPayrollFile(housemaids, rule);
            } else {

                List<OfficeStaff> staffs = officeStaffPayrollAuditService.getTargetList(rule, employeeType);
                staffs.addAll(officeStaffPayrollAuditService.getTerminatedList(rule, employeeType));
                List<OfficeStaff> terminatedStaff = officeStaffPayrollAuditService.getTerminatedListForDetailedFile(rule, employeeType);
                List<OfficeStaff> excludedStaff = officeStaffPayrollAuditService.getExcludedList(rule, employeeType);

//            DebugHelper.sendMail("<EMAIL>", "generatePayrollAuditDetailFile for Office Staffs, active list: " + staffs.size() + ", terminated list: " + terminatedStaff.size() + ", excluded list: " + excludedStaff.size());
                staffs.sort(Comparator.comparing(OfficeStaff::getName));
                terminatedStaff.sort(Comparator.comparing(OfficeStaff::getName));
                excludedStaff.sort(Comparator.comparing(OfficeStaff::getName));
                List<Long> staffsIds = staffs.stream().map(BaseEntity::getId).collect(Collectors.toList());
                List<Long> terminatedStaffsIds = terminatedStaff.stream().map(BaseEntity::getId).collect(Collectors.toList());
                List<Long> excludedStaffsIds = excludedStaff.stream().map(BaseEntity::getId).collect(Collectors.toList());

                officeStaffPayrollPaymentServiceV2.clearOfficeStaffLogsForChangedAndRecreate(staffs, rule, null, null, false);
                officeStaffPayrollPaymentServiceV2.clearOfficeStaffLogsForChangedAndRecreate(terminatedStaff, rule, null, null, false);
                officeStaffPayrollPaymentServiceV2.clearOfficeStaffLogsForChangedAndRecreate(excludedStaff, rule, null, null, false);

                List<OfficeStaffPayrollLog> includedOfficeStaffPayrollLogs = officeStaffPayrollPaymentServiceV2.getOfficeStaffPayrollLogsBasedOnAll(staffsIds, rule, null, null);
                List<OfficeStaffPayrollLog> terminatedOfficeStaffPayrollLogs = officeStaffPayrollPaymentServiceV2.getOfficeStaffPayrollLogsBasedOnAll(terminatedStaffsIds, rule, null, null);
                List<OfficeStaffPayrollLog> excludedOfficeStaffPayrollLogs = officeStaffPayrollPaymentServiceV2.getOfficeStaffPayrollLogsBasedOnAll(excludedStaffsIds, rule, null, null);

                detailedFiles.add(generateOfficeStaffFinalDetailedPayrollFile(includedOfficeStaffPayrollLogs, terminatedOfficeStaffPayrollLogs, excludedOfficeStaffPayrollLogs, rule, null, false));

//            return generateOfficeStaffDetailedPayrollFile(staffs, terminatedStaff, excludedStaff, rule);
            }
        }
        return detailedFiles;
    }

//    public List<Attachment> generatePayrollPrePaymentFiles(MonthlyPaymentRule rule, PaymentRuleEmployeeType employeeType) {
//
//        List<Attachment> attachments = new ArrayList<>();
//
//        if (employeeType != PaymentRuleEmployeeType.HOUSEMAIDS) {
//            List<OfficeStaff> officeStaffs;
//            List<OfficeStaffPayrollLog> payrollLogs;
//
//            List<MonthlyPaymentRule> rules = new ArrayList<>();
//            if (rule.getPayrollType() == PayrollType.SECONDARY) {
//                rules.add(rule);
//            } else {
//                rules.addAll(Setup.getRepository(MonthlyPaymentRuleRepository.class)
//                        .findByPayrollMonthAndPayrollTypeAndFinishedFalse(rule.getPayrollMonth(), PayrollType.PRIMARY));
//            }
//            for (MonthlyPaymentRule monthlyPaymentRule : rules) {
//                boolean acceptedRule = (rule.isTargetingEmiratis() && monthlyPaymentRule.isTargetingEmiratis()) ||
//                        (rule.isTargetingExpats() && monthlyPaymentRule.isTargetingExpats()) ||
//                        (rule.isTargetingOverseas() && monthlyPaymentRule.isTargetingOverseas());
//
//                if (!acceptedRule) continue;
//
//                PayrollAccountantTodo mockToDo = new PayrollAccountantTodo();
//                mockToDo.setPayrollMonth(monthlyPaymentRule.getPayrollMonth());
//                mockToDo.setMonthlyPaymentRule(monthlyPaymentRule);
//
//                String info = " of " + DateUtil.formatSimpleMonth(monthlyPaymentRule.getPayrollMonth());
//                switch (monthlyPaymentRule.getPaymentMethod()) {
//                    case WPS:
//                        mockToDo.setTaskName(PayrollAccountantTodoType.WPS.toString());
//                        officeStaffs = officeStaffPayrollPaymentServiceV2.getIncludedTargetList(monthlyPaymentRule, PayrollAccountantTodoType.WPS);
//                        payrollLogs = officeStaffPayrollPaymentServiceV2.generatePayrollLogs(officeStaffs, mockToDo, false);
//
//                        try {
//                            attachments.add(transferFilesService.generateWPSTransferFile(monthlyPaymentRule, info, new ArrayList<>(), payrollLogs, new HashMap<Long, List<HousemaidPayrollLog>>(), 0, 0, null, null));
//                        } catch (Exception e) {
//                            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating pre payment payroll wps file!", false);
//                        }
//                        break;
//                    case LOCAL_TRANSFER:
//                        mockToDo.setTaskName(PayrollAccountantTodoType.LOCAL_TRANSFER.toString());
//                        officeStaffs = officeStaffPayrollPaymentServiceV2.getIncludedTargetList(monthlyPaymentRule, PayrollAccountantTodoType.LOCAL_TRANSFER);
//                        payrollLogs = officeStaffPayrollPaymentServiceV2.generatePayrollLogs(officeStaffs, mockToDo, false);
//                        try {
//                            attachments.add(transferFilesService.generateLocalTransferFile(monthlyPaymentRule, info, new ArrayList<>(), payrollLogs, new HashMap<Long, List<HousemaidPayrollLog>>()));
//                        } catch (Exception e) {
//                            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating pre payment payroll local file!", false);
//                        }
//                        break;
//                    case ACCORDING_TO_EMPLOYEE_PROFILE:
//
//                        if (officeStaffPayrollPaymentServiceV2.countTargetList(monthlyPaymentRule, PayrollAccountantTodoType.INTERNATIONAL_TRANSFER) > 0) {
//                            mockToDo.setTaskName(PayrollAccountantTodoType.INTERNATIONAL_TRANSFER.toString());
//                            officeStaffs = officeStaffPayrollPaymentServiceV2.getIncludedTargetList(monthlyPaymentRule, PayrollAccountantTodoType.INTERNATIONAL_TRANSFER);
//                            payrollLogs = officeStaffPayrollPaymentServiceV2.generatePayrollLogs(officeStaffs, mockToDo, false);
//                            try {
//                                attachments.add(transferFilesService.generateInternationalTransferFile(monthlyPaymentRule, info, payrollLogs));
//                            } catch (Exception e) {
//                                DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating pre payment payroll international file!", false);
//                            }
//                        }
//
//                        break;
//                }
//            }
//        } else {
//            List<Housemaid> housemaids;
//            List<HousemaidPayrollLog> payrollLogs;
//            List<MonthlyPaymentRule> rules = new ArrayList<>();
//            if (rule.getPayrollType() == PayrollType.SECONDARY) {
//                rules.add(rule);
//            } else {
//                rules.addAll(Setup.getRepository(MonthlyPaymentRuleRepository.class)
//                        .findByPayrollMonthAndPayrollTypeAndFinishedFalse(rule.getPayrollMonth(), PayrollType.PRIMARY));
//            }
//
//            for (MonthlyPaymentRule monthlyPaymentRule : rules) {
//                if (!monthlyPaymentRule.isTargetingHousemaid()) continue;
//                PayrollAccountantTodo mockToDo = new PayrollAccountantTodo();
//                mockToDo.setPayrollMonth(monthlyPaymentRule.getPayrollMonth());
//                mockToDo.setMonthlyPaymentRule(monthlyPaymentRule);
//                int daysInPeriod = monthlyPaymentRule.getPayrollMonth().toLocalDate().lengthOfMonth();
//                java.util.Date start = new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(1).toDate();
//                java.util.Date end = new LocalDate(monthlyPaymentRule.getPayrollMonth()).dayOfMonth().withMaximumValue().toDate();
//
//                List<HousemaidPayrollLog> maidVisaLogsForPreviousMonths;
//                Map<Long, List<HousemaidPayrollLog>> maidVisaLogsForPreviousMonthsMap;
//                String info = " of " + DateUtil.formatSimpleMonth(monthlyPaymentRule.getPayrollMonth());
//
//                switch (monthlyPaymentRule.getPaymentMethod()) {
//                    case WPS:
//                        mockToDo.setTaskName(PayrollAccountantTodoType.WPS.toString());
//                        housemaids = housemaidPayrollPaymentServiceV2.getIncludedTargetList(monthlyPaymentRule);
//                        mockToDo.setHousemaids(new HashSet<>(housemaids));
//                        mockToDo.setIncludedHousemaids(new HashSet<>(housemaids));
//                        payrollLogs = monthlyPaymentRule.isSecondaryMonthlyRule() ?
//                                housemaidPayrollPaymentServiceV2.
//                                        generatePayrollLogsForSecondary(new ArrayList<>(housemaids), mockToDo, false) :
//                                housemaidPayrollPaymentServiceV2.
//                                        generatePayrollLogs(new ArrayList<>(housemaids), mockToDo, false);
//
////                        payrollLogs = housemaidPayrollPaymentServiceV2.generatePayrollLogs(housemaids, mockToDo, false);
//                        maidVisaLogsForPreviousMonths = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getMVPayrollLogsForPreviousMonthsNew(rule, null, false);
//                        maidVisaLogsForPreviousMonths.sort(Comparator.comparing((HousemaidPayrollLog h1) -> h1.getHousemaid().getName()));
//                        maidVisaLogsForPreviousMonthsMap = PayrollGenerationLibrary.convertListToMap(maidVisaLogsForPreviousMonths);
//
//                        Set<Long> includedHousemaidIds = payrollLogs.stream().filter(x -> x.getWillBeIncluded()).map(BaseEntity::getId).collect(Collectors.toSet());
//                        includedHousemaidIds.addAll(maidVisaLogsForPreviousMonths.stream().map(BaseEntity::getId).collect(Collectors.toSet()));
//                        DebugHelper.sendMail("<EMAIL>", "includedHousemaidIds: " + includedHousemaidIds);
//                        int totalIncludedMaids = includedHousemaidIds.size();
//
//                        try {
//                            attachments.add(transferFilesService.generateWPSTransferFile(monthlyPaymentRule, info, payrollLogs, new ArrayList<>(), maidVisaLogsForPreviousMonthsMap, totalIncludedMaids, daysInPeriod, start, end));
//                        } catch (Exception e) {
//                            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating housemaids pre payment payroll wps file!", false);
//                        }
//                        break;
//                    case LOCAL_TRANSFER:
//                        mockToDo.setTaskName(PayrollAccountantTodoType.LOCAL_TRANSFER.toString());
//                        housemaids = housemaidPayrollPaymentServiceV2.getIncludedTargetList(monthlyPaymentRule);
//                        mockToDo.setHousemaids(new HashSet<>(housemaids));
//                        mockToDo.setIncludedHousemaids(new HashSet<>(housemaids));
//                        payrollLogs = monthlyPaymentRule.isSecondaryMonthlyRule() ?
//                                housemaidPayrollPaymentServiceV2.
//                                        generatePayrollLogsForSecondary(new ArrayList<>(housemaids), mockToDo, false) :
//                                housemaidPayrollPaymentServiceV2.
//                                        generatePayrollLogs(new ArrayList<>(housemaids), mockToDo, false);
//
//                        maidVisaLogsForPreviousMonths = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getMVPayrollLogsForPreviousMonthsNew(rule, null, false);
//                        maidVisaLogsForPreviousMonths.sort(Comparator.comparing((HousemaidPayrollLog h1) -> h1.getHousemaid().getName()));
//                        maidVisaLogsForPreviousMonthsMap = PayrollGenerationLibrary.convertListToMap(maidVisaLogsForPreviousMonths);
//
//                        try {
//                            attachments.add(transferFilesService.generateLocalTransferFile(monthlyPaymentRule, info, payrollLogs, new ArrayList<>(), maidVisaLogsForPreviousMonthsMap));
//                        } catch (Exception e) {
//                            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating housemaids pre payment payroll local file!", false);
//                        }
//                        break;
//                }
//            }
//        }
//
//        return attachments.stream().filter(att -> att != null && att.getUuid() != null && att.getTag() != null)
//                .collect(Collectors.toList());
//    }

    public List<Attachment> generatePayrollPrePaymentFilesNew(MonthlyPaymentRule rule, PayrollAuditTodo auditTodo, PaymentRuleEmployeeType employeeType) {
        List<Attachment> attachments = new ArrayList<>();

        if (employeeType != PaymentRuleEmployeeType.HOUSEMAIDS) {
            List<OfficeStaff> officeStaffs;
            List<Long> officeStaffsIds;
            List<OfficeStaffPayrollLog> payrollLogs;

            List<MonthlyPaymentRule> rules = new ArrayList<>();
            rules.addAll(Setup.getRepository(MonthlyPaymentRuleRepository.class)
                    .findByPayrollMonthAndLockDateAndFinishedFalse(rule.getPayrollMonth(), rule.getLockDate()));

            for (MonthlyPaymentRule monthlyPaymentRule : rules) {
                boolean acceptedRule = (rule.isTargetingEmiratis() && monthlyPaymentRule.isTargetingEmiratis()) ||
                        (rule.isTargetingExpats() && monthlyPaymentRule.isTargetingExpats()) ||
                        (rule.isTargetingOverseas() && monthlyPaymentRule.isTargetingOverseas());

                if (!acceptedRule) continue;


                String info = " of " + DateUtil.formatSimpleMonth(monthlyPaymentRule.getPayrollMonth());
//                DebugHelper.sendMail("<EMAIL>", "generatePayrollPrePaymentFilesNew for officestaffs: ");
                switch (monthlyPaymentRule.getPaymentMethod()) {
                    case WPS:

                        officeStaffs = officeStaffPayrollPaymentServiceV2.getIncludedTargetList(monthlyPaymentRule, PayrollAccountantTodoType.WPS);
                        officeStaffsIds = officeStaffs.stream().map(BaseEntity::getId).collect(Collectors.toList());
                        officeStaffPayrollPaymentServiceV2.clearOfficeStaffLogsForChangedAndRecreate(officeStaffs, monthlyPaymentRule, auditTodo, null, false);
                        payrollLogs = officeStaffPayrollPaymentServiceV2.getOfficeStaffPayrollLogsBasedOnAll(officeStaffsIds, monthlyPaymentRule, auditTodo,null);

                        try {
                            attachments.add(transferFilesService.generateWPSTransferFile(monthlyPaymentRule, info, new ArrayList<>(), payrollLogs, new HashMap<Long, List<HousemaidPayrollLog>>(), 0, 0, null, null));
                        } catch (Exception e) {
                            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating pre payment payroll wps file!", false);
                        }
                        break;
                    case LOCAL_TRANSFER:
                        officeStaffs = officeStaffPayrollPaymentServiceV2.getIncludedTargetList(monthlyPaymentRule, PayrollAccountantTodoType.LOCAL_TRANSFER);
                        officeStaffsIds = officeStaffs.stream().map(BaseEntity::getId).collect(Collectors.toList());
                        officeStaffPayrollPaymentServiceV2.clearOfficeStaffLogsForChangedAndRecreate(officeStaffs, monthlyPaymentRule, auditTodo, null, false);
                        payrollLogs = officeStaffPayrollPaymentServiceV2.getOfficeStaffPayrollLogsBasedOnAll(officeStaffsIds, monthlyPaymentRule, auditTodo,null);
                        try {
                            attachments.add(transferFilesService.generateLocalTransferFile(monthlyPaymentRule, info, new ArrayList<>(), payrollLogs, new HashMap<Long, List<HousemaidPayrollLog>>()));
                        } catch (Exception e) {
                            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating pre payment payroll local file!", false);
                        }
                        break;
                    case ACCORDING_TO_EMPLOYEE_PROFILE:

                        if (officeStaffPayrollPaymentServiceV2.countTargetList(monthlyPaymentRule, PayrollAccountantTodoType.INTERNATIONAL_TRANSFER) > 0) {
                            officeStaffs = officeStaffPayrollPaymentServiceV2.getIncludedTargetList(monthlyPaymentRule, PayrollAccountantTodoType.INTERNATIONAL_TRANSFER);
                            officeStaffsIds = officeStaffs.stream().map(BaseEntity::getId).collect(Collectors.toList());
                            officeStaffPayrollPaymentServiceV2.clearOfficeStaffLogsForChangedAndRecreate(officeStaffs, monthlyPaymentRule, auditTodo, null, false);
                            payrollLogs = officeStaffPayrollPaymentServiceV2.getOfficeStaffPayrollLogsBasedOnAll(officeStaffsIds, monthlyPaymentRule, auditTodo,null);
                            try {
                                attachments.add(transferFilesService.generateInternationalTransferFile(monthlyPaymentRule, info, payrollLogs));
                            } catch (Exception e) {
                                DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating pre payment payroll international file!", false);
                            }
                        }

                        break;
                }
            }
        } else {
            List<Housemaid> housemaids;
            List<Long> housemaidsIds;
            List<HousemaidPayrollLog> payrollLogs;
            List<MonthlyPaymentRule> rules = new ArrayList<>();

            rules.addAll(Setup.getRepository(MonthlyPaymentRuleRepository.class)
                    .findByPayrollMonthAndLockDateAndFinishedFalse(rule.getPayrollMonth(), rule.getLockDate()));

//            DebugHelper.sendMail("<EMAIL>", "start generatePayrollPrePaymentFilesNew for housemaids: ");
            for (MonthlyPaymentRule monthlyPaymentRule : rules) {
                if (!monthlyPaymentRule.isTargetingHousemaid()) continue;
                int daysInPeriod = monthlyPaymentRule.getPayrollMonth().toLocalDate().lengthOfMonth();
                java.util.Date start = new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(1).toDate();
                java.util.Date end = new LocalDate(monthlyPaymentRule.getPayrollMonth()).dayOfMonth().withMaximumValue().toDate();

                List<HousemaidPayrollLog> maidVisaLogsForPreviousMonths;
                Map<Long, List<HousemaidPayrollLog>> maidVisaLogsForPreviousMonthsMap;
                String info = " of " + DateUtil.formatSimpleMonth(monthlyPaymentRule.getPayrollMonth());

                switch (monthlyPaymentRule.getPaymentMethod()) {
                    case WPS:

                        housemaids = housemaidPayrollPaymentServiceV2.getIncludedTargetList(monthlyPaymentRule);
                        housemaidPayrollPaymentServiceV2.clearHousemaidLogsForChangedAndRecreate(housemaids, monthlyPaymentRule, auditTodo, null, false, true);
                        payrollLogs = housemaidPayrollPaymentServiceV2.
                                        getHousemaidPayrollLogsBasedOnAll(new ArrayList<>(housemaids), monthlyPaymentRule, auditTodo, null);

                        maidVisaLogsForPreviousMonths = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getMVPayrollLogsForPreviousMonthsNew(monthlyPaymentRule, null, false);
                        maidVisaLogsForPreviousMonths.sort(Comparator.comparing((HousemaidPayrollLog h1) -> h1.getHousemaid().getName()));
                        maidVisaLogsForPreviousMonthsMap = PayrollGenerationLibrary.convertListToMap(maidVisaLogsForPreviousMonths);

                        Set<Long> includedHousemaidIds = payrollLogs.stream().map(x -> x.getHousemaid().getId()).collect(Collectors.toSet());
                        includedHousemaidIds.addAll(maidVisaLogsForPreviousMonths.stream().map(x -> x.getHousemaid().getId()).collect(Collectors.toSet()));
//                        DebugHelper.sendMail("<EMAIL>", "includedHousemaidIds: " + includedHousemaidIds);
                        int totalIncludedMaids = includedHousemaidIds.size();

                        try {
                            attachments.add(transferFilesService.generateWPSTransferFile(monthlyPaymentRule, info, payrollLogs, new ArrayList<>(), maidVisaLogsForPreviousMonthsMap, totalIncludedMaids, daysInPeriod, start, end));
                        } catch (Exception e) {
                            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating housemaids pre payment payroll wps file!", false);
                        }
                        break;
                    case LOCAL_TRANSFER:
                        housemaids = housemaidPayrollPaymentServiceV2.getIncludedTargetList(monthlyPaymentRule);
                        housemaidPayrollPaymentServiceV2.clearHousemaidLogsForChangedAndRecreate(housemaids, monthlyPaymentRule, auditTodo, null, false, true);
                        payrollLogs = housemaidPayrollPaymentServiceV2.
                                        getHousemaidPayrollLogsBasedOnAll(new ArrayList<>(housemaids), monthlyPaymentRule, auditTodo, null);

                        maidVisaLogsForPreviousMonths = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getMVPayrollLogsForPreviousMonthsNew(monthlyPaymentRule, null, false);
                        maidVisaLogsForPreviousMonths.sort(Comparator.comparing((HousemaidPayrollLog h1) -> h1.getHousemaid().getName()));
                        maidVisaLogsForPreviousMonthsMap = PayrollGenerationLibrary.convertListToMap(maidVisaLogsForPreviousMonths);

                        try {
                            attachments.add(transferFilesService.generateLocalTransferFile(monthlyPaymentRule, info, payrollLogs, new ArrayList<>(), maidVisaLogsForPreviousMonthsMap));
                        } catch (Exception e) {
                            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating housemaids pre payment payroll local file!", false);
                        }
                        break;
                }
            }
        }

        return attachments.stream().filter(att -> att != null && att.getUuid() != null && att.getTag() != null)
                .collect(Collectors.toList());
    }

    public List<Attachment> generatePayrollPrePaymentFilesRevamp(MonthlyPaymentRule rule) {  //WPS Files
        //For OfficeStaff
        List<Attachment> attachments = new ArrayList<>();
        List<Long> officeStaffsIds;
        List<OfficeStaffPayrollLog> officeStaffsPayrollLogs = new ArrayList<>();

        //For Housemaid
        int daysInPeriod = rule.getPayrollMonth().toLocalDate().lengthOfMonth();
        java.util.Date start = new LocalDate(rule.getPayrollMonth()).withDayOfMonth(1).toDate();
        java.util.Date end = new LocalDate(rule.getPayrollMonth()).dayOfMonth().withMaximumValue().toDate();

        List<HousemaidPayrollLog> maidVisaLogsForPreviousMonths;
        Map<Long, List<HousemaidPayrollLog>> maidVisaLogsForPreviousMonthsMap = new HashMap<>();
        String info = " of " + DateUtil.formatSimpleMonth(rule.getPayrollMonth());

        List<Housemaid> housemaids;
        List<Long> housemaidsIds;
        List<HousemaidPayrollLog> housemaidPayrollLogs = new ArrayList<>();
        int totalIncludedMaids = 0;

        switch (rule.getPaymentMethod()) {
            case WPS:
                if(rule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.EXPATS) || rule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.OVERSEAS)) {
                    List<OfficeStaff> officeStaffs;
                    officeStaffs = officeStaffPayrollPaymentServiceV2.getIncludedTargetList(rule, PayrollAccountantTodoType.WPS);
                    officeStaffsIds = officeStaffs.stream().map(BaseEntity::getId).collect(Collectors.toList());
                    officeStaffPayrollPaymentServiceV2.clearOfficeStaffLogsForChangedAndRecreate(officeStaffs, rule, null, null, false);
                    officeStaffsPayrollLogs = officeStaffPayrollPaymentServiceV2.getOfficeStaffPayrollLogsBasedOnAll(officeStaffsIds, rule, null, null);
                }
                if(rule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS)){

                    housemaids = housemaidPayrollPaymentServiceV2.getIncludedTargetList(rule);
                    housemaidPayrollPaymentServiceV2.clearHousemaidLogsForChangedAndRecreate(housemaids, rule, null, null, false, true);
                    housemaidPayrollLogs = housemaidPayrollPaymentServiceV2.
                            getHousemaidPayrollLogsBasedOnAll(new ArrayList<>(housemaids), rule, null, null);

                    maidVisaLogsForPreviousMonths = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getMVPayrollLogsForPreviousMonthsNew(rule, null, false);
                    maidVisaLogsForPreviousMonths.sort(Comparator.comparing((HousemaidPayrollLog h1) -> h1.getHousemaid().getName()));
                    maidVisaLogsForPreviousMonthsMap = PayrollGenerationLibrary.convertListToMap(maidVisaLogsForPreviousMonths);

                    Set<Long> includedHousemaidIds = housemaidPayrollLogs.stream().map(x -> x.getHousemaid().getId()).collect(Collectors.toSet());
                    includedHousemaidIds.addAll(maidVisaLogsForPreviousMonths.stream().map(x -> x.getHousemaid().getId()).collect(Collectors.toSet()));
                    totalIncludedMaids = includedHousemaidIds.size();
                }
                if(rule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.EMIRATI)){

                }
                try {
                    attachments.add(transferFilesService.generateWPSTransferFile(rule, info, housemaidPayrollLogs, officeStaffsPayrollLogs, maidVisaLogsForPreviousMonthsMap, totalIncludedMaids, daysInPeriod, start, end));
                } catch (Exception e) {
                    DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating housemaids pre payment payroll wps file!", false);
                }
                break;
            case ACCORDING_TO_EMPLOYEE_PROFILE:
                //For InternationalTransferFile
                if (officeStaffPayrollPaymentServiceV2.countTargetList(rule, PayrollAccountantTodoType.INTERNATIONAL_TRANSFER) > 0) {
                    List<OfficeStaff> officeStaffs;
                    officeStaffs = officeStaffPayrollPaymentServiceV2.getIncludedTargetList(rule, PayrollAccountantTodoType.INTERNATIONAL_TRANSFER);
                    officeStaffsIds = officeStaffs.stream().map(BaseEntity::getId).collect(Collectors.toList());
                    officeStaffPayrollPaymentServiceV2.clearOfficeStaffLogsForChangedAndRecreate(officeStaffs, rule, null, null, false);
                    officeStaffsPayrollLogs = officeStaffPayrollPaymentServiceV2.getOfficeStaffPayrollLogsBasedOnAll(officeStaffsIds, rule, null,null);
                    try {
                        attachments.add(transferFilesService.generateInternationalTransferFile(rule, info, officeStaffsPayrollLogs));
                    } catch (Exception e) {
                        DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating pre payment payroll international file!", false);
                    }
                }
                break;

        }
        return attachments.stream().filter(att -> att != null && att.getUuid() != null && att.getTag() != null)
                .collect(Collectors.toList());
    }

    public List<Attachment> generatePayrollExceptionsReportFileRevamp(MonthlyPaymentRule rule) {  //Payroll Exceptions Report
        List<Attachment> attachmentList = new ArrayList<>();

        List<Housemaid> housemaids = Setup.getApplicationContext().getBean(HousemaidPayrollAuditService.class).getTargetList(rule);
        List<Housemaid> ccMaids = housemaids.stream().filter(x -> !x.getHousemaidType().equals(HousemaidType.MAID_VISA)).collect(Collectors.toList());
        List<Housemaid> visaMaids = housemaids.stream().filter(x -> x.getHousemaidType().equals(HousemaidType.MAID_VISA)).collect(Collectors.toList());

        PaymentRuleEmployeeType employeeType = rule.isTargetingEmiratis() ? PaymentRuleEmployeeType.EMIRATI : (rule.isTargetingExpats() ? PaymentRuleEmployeeType.EXPATS : (rule.isTargetingOverseas() ? PaymentRuleEmployeeType.OVERSEAS : null));
        List<OfficeStaff> staffs = employeeType != null ? Setup.getApplicationContext().getBean(OfficeStaffPayrollAuditService.class).getTargetList(rule, employeeType) : new ArrayList<>();
        Attachment payrollExceptionReport = Setup.getApplicationContext().getBean(PayrollExceptionsReportService.class).generatePayrollExceptionsReport(ccMaids, visaMaids, staffs, rule, rule.getEmployeesTypes());
        if (payrollExceptionReport != null)
            attachmentList.add(payrollExceptionReport);
        return attachmentList;
    }

//    public Attachment generateHousemaidDetailedPayrollFile(List<Housemaid> housemaids, MonthlyPaymentRule rule) {
//        try {
//
//            LocalDate payrollEnd = new LocalDate(this.housemaidPayrollPaymentServiceV2.getPayrollEndLockDate(rule));
//            LocalDate payrollStart = new LocalDate(this.housemaidPayrollPaymentServiceV2.getPayrollStartLockDate(rule));
//            LocalDate payrollMonth = new LocalDate(rule.getPayrollMonth());
//            Reflections reflections =
//                    new Reflections("com.magnamedia.salarycalculation.v2");
//            Set<Class<? extends HousemaidSalaryTransaction>> allClasses =
//                    reflections.getSubTypesOf(HousemaidSalaryTransaction.class);
//
//            // Delete all simulation data
//            this.housemaidPayrollPaymentServiceV2.recoverSimulationData(rule.getPayrollMonth(), false);
//
//            List<Long> housemaidIds = housemaids.stream().map(BaseEntity::getId).collect(Collectors.toList());
//
//            // Process all housemaids repayments
//            HousemaidPayrollPaymentServiceV2.prepareHousemaidPayrollInitializer(housemaidIds, payrollStart, payrollEnd, rule);
////            HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer().prepareCurrentMonthRepayments(housemaids, payrollStart.getMonthOfYear() == payrollMonth.getMonthOfYear() ? payrollStart : payrollMonth.withDayOfMonth(1),payrollStart.toDate(), payrollEnd.toDate(), false);
//
//            HousemaidPayrollInitializer initializer = HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer();
//
//            List<HousemaidPayrollBean> beans;
//
//            if(rule.isSecondaryMonthlyRule())
//                beans = housemaidPayrollPaymentServiceV2.processSalariesOfSecondaryPayroll(housemaids, rule, payrollStart, payrollEnd, false);
//            else {
//                proRatedSalariesService.processProRatedSalaries(housemaids, rule, payrollStart, payrollEnd, false, HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer());
//                beans = negativeSalariesService.processNegativeSalariesAndDeductionCap(housemaids, rule, payrollStart, payrollEnd, false);
//            }
//
//            DebugHelper.sendMail("<EMAIL>", "Before initialize housemaids info!");
//
//            HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer().prepareHousemaidsInfo(housemaidIds);
//
//            DebugHelper.sendMail("<EMAIL>", "After initialize housemaids info!");
//
//            PayrollAccountantTodo payrollAccountantTodo = new PayrollAccountantTodo();
//            payrollAccountantTodo.setMonthlyPaymentRule(rule);
//            payrollAccountantTodo.setPayrollMonth(rule.getPayrollMonth());
//
//            List<HousemaidPayrollLog> maidVisaLogsForPreviousMonths = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getMVPayrollLogsForPreviousMonths(payrollAccountantTodo, false);
//            maidVisaLogsForPreviousMonths.sort(Comparator.comparing((HousemaidPayrollLog h1) -> h1.getHousemaid().getName()));
//            Map<Long, List<HousemaidPayrollLog>> maidVisaLogsForPreviousMonthsMap = PayrollGenerationLibrary.convertListToMap(maidVisaLogsForPreviousMonths);
//
//            Map<Long, List<HousemaidPayrollLog>> previousLogsMap;
//            if(maidVisaLogsForPreviousMonthsMap != null)
//                previousLogsMap = new HashMap<>(maidVisaLogsForPreviousMonthsMap);
//            else
//                previousLogsMap = new HashMap<>();
//
//            int counter = 0;
//            Double totalAnsari = 0d;
//            List<HousemaidPayrollBean> result = new ArrayList<>();
//
//            for(HousemaidPayrollBean bean: beans) {
//
//                Housemaid housemaid = bean.getHousemaid();
//                HousemaidPayrollBean targetBean;
//                if(rule.isSecondaryMonthlyRule() && !initializer.hasPaidSalary(housemaid.getId())) {
//                    HousemaidPayrollLog oldLog = Setup.getRepository(HousemaidPayrollLogRepository.class).findTopByHousemaidAndPayrollMonth(housemaid, rule.getPayrollMonth());
//                    if(oldLog == null)
//                        continue;
//                    HousemaidBeanInfo oldBeanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).findTopByHousemaidPayrollLog(oldLog);
//                    if(oldBeanInfo == null) {
//                        oldBeanInfo = housemaidPayrollPaymentServiceV2.createBeanInfoDetails(housemaid, oldLog);
//                        oldBeanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).save(oldBeanInfo);
//                    }
//
//                    targetBean = new HousemaidPayrollBean(oldBeanInfo, true);
//                    targetBean.setManagerAddition(targetBean.getManagerAddition() + bean.getManagerAddition());
//                    targetBean.setTotatIcome(targetBean.getTotatIcome() + bean.getTotatIcome());
//                }else{
//                    targetBean = bean;
//
//                    //fill prorated fields from the Payroll log
//                    //if paid on primary then no need to get prorated amounts
//                    if (!initializer.hasPaidSalary(housemaid.getId())) {
//
//                        Map<String, Object> breakDown = Setup.getApplicationContext().getBean(ProRatedSalariesService.class).getSalaryBreakDown(housemaid,
//                                payrollMonth,
//                                new java.sql.Date(payrollMonth.withDayOfMonth(1).toDate().getTime()),
//                                new java.sql.Date(payrollMonth.dayOfMonth().withMaximumValue().toDate().getTime()),
//                                HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer().getGroup1Days(housemaid.getId()),
//                                initializer.getPreviousVacationDays(housemaid.getId()),
//                                initializer.getVacationDays(housemaid.getId()),
//                                initializer.getLastVacationDatesMap());
//
//                        targetBean.setFullSalary(valueOrZero(housemaid.getBasicSalary()));
//                        targetBean.setMohreSalary(valueOrZero(housemaid.getPrimarySalary()));
//                        targetBean.setGroupOneDays((Integer) breakDown.get("group1Days"));
//                        targetBean.setGroupTwoDays((Integer) breakDown.get("group2Days"));
//                        targetBean.setEarningInGroupOne(1d * Math.round((Double) breakDown.get("group1Salary")));
//                        targetBean.setEarningInGroupTwo(1d * Math.round((Double) breakDown.get("group2Salary")));
//                    }
//                }
//                targetBean = housemaidPayrollAuditService.setHousemaidInfo(targetBean, housemaid, payrollMonth, HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer());
//                targetBean.setIndexNum(String.valueOf(++counter));
//                targetBean.setSn("H-" + (counter));
//
//
//                // set balance to current balance + the previous months in case maid visa and got payments for old months
//                Double previousMonthsHousemaidSalary = 0.0;
//                if (HousemaidType.MAID_VISA.equals(targetBean.getHousemaid().getHousemaidType())) {
//                    Long housemaidId = targetBean.getHousemaid().getId();
//                    List<HousemaidPayrollLog> previousMonthsLogs = previousLogsMap.get(housemaidId);
//
//                    if (previousMonthsLogs != null) {
//                        for (HousemaidPayrollLog previousLog : previousMonthsLogs)
//                            previousMonthsHousemaidSalary += previousLog.getTotalSalary();
//
//                        previousLogsMap.remove(housemaidId);
//                        Double totalBalance = targetBean.getTotalBalance() != null ? targetBean.getTotalBalance() : 0.0;
//                        totalBalance += previousMonthsHousemaidSalary;
//                        targetBean.setPreviouslyUnpaidSalaries(previousMonthsHousemaidSalary);
//                        targetBean.setTotatIcome(bean.getTotatIcome() + previousMonthsHousemaidSalary);
//                        targetBean.setTotalBalance(totalBalance);
////                        bean.setBalance(bean.getBalance() + previousMonthsHousemaidSalary);
//                    }
//                }
//
//                totalAnsari += targetBean.getTotalBalance();
//                result.add(targetBean);
//            }
//
//            for (Map.Entry<Long, List<HousemaidPayrollLog>> entry : previousLogsMap.entrySet()) {
//                List<HousemaidPayrollLog> previousLogs = entry.getValue();
//                Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(previousLogs.get(0).getHousemaid().getId());
//
//                Double housemaidSalary = 0.0;
//                for (HousemaidPayrollLog log : previousLogs)
//                    housemaidSalary += log.getTotalSalary();
//
//                //all are the same needed date
//                HousemaidPayrollLog log = previousLogs.get(0);
//
//                HousemaidBeanInfo housemaidBeanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).findTopByHousemaidPayrollLog(log);
//                if(housemaidBeanInfo == null) {
//                    housemaidBeanInfo = housemaidPayrollPaymentServiceV2.createBeanInfoDetails(housemaid, log);
//                    housemaidBeanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).save(housemaidBeanInfo);
//                }
//                HousemaidPayrollBean targetBean = new HousemaidPayrollBean(housemaidBeanInfo, false);
//                targetBean.setPreviouslyUnpaidSalaries(housemaidSalary);
//                targetBean.setTotatIcome(housemaidSalary);
//                targetBean.setBalance(housemaidSalary);
//                targetBean.setTotalBalance(housemaidSalary);
//
//                totalAnsari += targetBean.getTotalBalance();
//                targetBean.setIndexNum(String.valueOf(++counter));
//                targetBean.setSn("H-" + (counter));
//                result.add(targetBean);
//
//            }
//
//            DebugHelper.sendMail("<EMAIL>", "After fill housemaids info!");
//
//            HousemaidPayrollPaymentServiceV2.clearHousemaidPayrollInitializer();
//
//            String fileName = "Housemaids Payroll of " + DateUtil.formatSimpleMonth(rule.getPayrollMonth()) + " NOT_FINAL.xlsx";
//
//            return PayrollGenerationLibrary.generateHousemaidPayrollFile(fileName, result, totalAnsari);
//
//        } catch (Exception e) {
//            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating housemaids detailed payroll file!", false);
//        } finally {
//            HousemaidPayrollPaymentServiceV2.clearHousemaidPayrollInitializer();
//        }
//        return null;
//    }

//    public Attachment generateHousemaidDetailedPayrollFileNew(List<Housemaid> housemaids, MonthlyPaymentRule rule) {
//        try {
//
//            LocalDate payrollEnd = new LocalDate(this.housemaidPayrollPaymentServiceV2.getPayrollEndLockDate(rule));
//            LocalDate payrollStart = new LocalDate(this.housemaidPayrollPaymentServiceV2.getPayrollStartLockDate(rule));
//            LocalDate payrollMonth = new LocalDate(rule.getPayrollMonth());
//            Reflections reflections =
//                    new Reflections("com.magnamedia.salarycalculation.v2");
//            Set<Class<? extends HousemaidSalaryTransaction>> allClasses =
//                    reflections.getSubTypesOf(HousemaidSalaryTransaction.class);
//
//            // Delete all simulation data
//            this.housemaidPayrollPaymentServiceV2.recoverSimulationData(rule.getPayrollMonth(), false);
//
//            List<Long> housemaidIds = housemaids.stream().map(BaseEntity::getId).collect(Collectors.toList());
//
//            // Process all housemaids repayments
//            HousemaidPayrollPaymentServiceV2.prepareHousemaidPayrollInitializer(housemaidIds, payrollStart, payrollEnd, rule);
////            HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer().prepareCurrentMonthRepayments(housemaids, payrollStart.getMonthOfYear() == payrollMonth.getMonthOfYear() ? payrollStart : payrollMonth.withDayOfMonth(1),payrollStart.toDate(), payrollEnd.toDate(), false);
//
//            HousemaidPayrollInitializer initializer = HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer();
//
//            List<HousemaidPayrollBean> beans;
//
//            if(rule.isSecondaryMonthlyRule())
//                beans = housemaidPayrollPaymentServiceV2.processSalariesOfSecondaryPayroll(housemaids, rule, payrollStart, payrollEnd, false);
//            else {
//                proRatedSalariesService.processProRatedSalaries(housemaids, rule, payrollStart, payrollEnd, false, HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer());
//                beans = negativeSalariesService.processNegativeSalariesAndDeductionCap(housemaids, rule, payrollStart, payrollEnd, false);
//            }
//
//            DebugHelper.sendMail("<EMAIL>", "Before initialize housemaids info!");
//
//            HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer().prepareHousemaidsInfo(housemaidIds);
//
//            DebugHelper.sendMail("<EMAIL>", "After initialize housemaids info!");
//
//            PayrollAccountantTodo payrollAccountantTodo = new PayrollAccountantTodo();
//            payrollAccountantTodo.setMonthlyPaymentRule(rule);
//            payrollAccountantTodo.setPayrollMonth(rule.getPayrollMonth());
//
//            List<HousemaidPayrollLog> maidVisaLogsForPreviousMonths = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getMVPayrollLogsForPreviousMonths(payrollAccountantTodo, false);
//            maidVisaLogsForPreviousMonths.sort(Comparator.comparing((HousemaidPayrollLog h1) -> h1.getHousemaid().getName()));
//            Map<Long, List<HousemaidPayrollLog>> maidVisaLogsForPreviousMonthsMap = PayrollGenerationLibrary.convertListToMap(maidVisaLogsForPreviousMonths);
//
//            Map<Long, List<HousemaidPayrollLog>> previousLogsMap;
//            if(maidVisaLogsForPreviousMonthsMap != null)
//                previousLogsMap = new HashMap<>(maidVisaLogsForPreviousMonthsMap);
//            else
//                previousLogsMap = new HashMap<>();
//
//            int counter = 0;
//            Double totalAnsari = 0d;
//            List<HousemaidPayrollBean> result = new ArrayList<>();
//
//            for(HousemaidPayrollBean bean: beans) {
//
//                Housemaid housemaid = bean.getHousemaid();
//                HousemaidPayrollBean targetBean;
//                if(rule.isSecondaryMonthlyRule() && !initializer.hasPaidSalary(housemaid.getId())) {
//                    HousemaidPayrollLog oldLog = Setup.getRepository(HousemaidPayrollLogRepository.class).findTopByHousemaidAndPayrollMonth(housemaid, rule.getPayrollMonth());
//                    if(oldLog == null)
//                        continue;
//                    HousemaidBeanInfo oldBeanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).findTopByHousemaidPayrollLog(oldLog);
//                    if(oldBeanInfo == null) {
//                        oldBeanInfo = housemaidPayrollPaymentServiceV2.createBeanInfoDetails(housemaid, oldLog);
//                        oldBeanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).save(oldBeanInfo);
//                    }
//
//                    targetBean = new HousemaidPayrollBean(oldBeanInfo, true);
//                    targetBean.setManagerAddition(targetBean.getManagerAddition() + bean.getManagerAddition());
//                    targetBean.setTotatIcome(targetBean.getTotatIcome() + bean.getTotatIcome());
//                }else{
//                    targetBean = bean;
//
//                    //fill prorated fields from the Payroll log
//                    //if paid on primary then no need to get prorated amounts
//                    if (!initializer.hasPaidSalary(housemaid.getId())) {
//
//                        Map<String, Object> breakDown = Setup.getApplicationContext().getBean(ProRatedSalariesService.class).getSalaryBreakDown(housemaid,
//                                payrollMonth,
//                                new java.sql.Date(payrollMonth.withDayOfMonth(1).toDate().getTime()),
//                                new java.sql.Date(payrollMonth.dayOfMonth().withMaximumValue().toDate().getTime()),
//                                HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer().getGroup1Days(housemaid.getId()),
//                                initializer.getPreviousVacationDays(housemaid.getId()),
//                                initializer.getVacationDays(housemaid.getId()),
//                                initializer.getLastVacationDatesMap());
//
//                        targetBean.setFullSalary(valueOrZero(housemaid.getBasicSalary()));
//                        targetBean.setMohreSalary(valueOrZero(housemaid.getPrimarySalary()));
//                        targetBean.setGroupOneDays((Integer) breakDown.get("group1Days"));
//                        targetBean.setGroupTwoDays((Integer) breakDown.get("group2Days"));
//                        targetBean.setEarningInGroupOne(1d * Math.round((Double) breakDown.get("group1Salary")));
//                        targetBean.setEarningInGroupTwo(1d * Math.round((Double) breakDown.get("group2Salary")));
//                    }
//                }
//                targetBean = housemaidPayrollAuditService.setHousemaidInfo(targetBean, housemaid, payrollMonth, HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer());
//                targetBean.setIndexNum(String.valueOf(++counter));
//                targetBean.setSn("H-" + (counter));
//
//
//                // set balance to current balance + the previous months in case maid visa and got payments for old months
//                Double previousMonthsHousemaidSalary = 0.0;
//                if (HousemaidType.MAID_VISA.equals(targetBean.getHousemaid().getHousemaidType())) {
//                    Long housemaidId = targetBean.getHousemaid().getId();
//                    List<HousemaidPayrollLog> previousMonthsLogs = previousLogsMap.get(housemaidId);
//
//                    if (previousMonthsLogs != null) {
//                        for (HousemaidPayrollLog previousLog : previousMonthsLogs)
//                            previousMonthsHousemaidSalary += previousLog.getTotalSalary();
//
//                        previousLogsMap.remove(housemaidId);
//                        Double totalBalance = targetBean.getTotalBalance() != null ? targetBean.getTotalBalance() : 0.0;
//                        totalBalance += previousMonthsHousemaidSalary;
//                        targetBean.setPreviouslyUnpaidSalaries(previousMonthsHousemaidSalary);
//                        targetBean.setTotatIcome(bean.getTotatIcome() + previousMonthsHousemaidSalary);
//                        targetBean.setTotalBalance(totalBalance);
////                        bean.setBalance(bean.getBalance() + previousMonthsHousemaidSalary);
//                    }
//                }
//
//                totalAnsari += targetBean.getTotalBalance();
//                result.add(targetBean);
//            }
//
//            for (Map.Entry<Long, List<HousemaidPayrollLog>> entry : previousLogsMap.entrySet()) {
//                List<HousemaidPayrollLog> previousLogs = entry.getValue();
//                Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(previousLogs.get(0).getHousemaid().getId());
//
//                Double housemaidSalary = 0.0;
//                for (HousemaidPayrollLog log : previousLogs)
//                    housemaidSalary += log.getTotalSalary();
//
//                //all are the same needed date
//                HousemaidPayrollLog log = previousLogs.get(0);
//
//                HousemaidBeanInfo housemaidBeanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).findTopByHousemaidPayrollLog(log);
//                if(housemaidBeanInfo == null) {
//                    housemaidBeanInfo = housemaidPayrollPaymentServiceV2.createBeanInfoDetails(housemaid, log);
//                    housemaidBeanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).save(housemaidBeanInfo);
//                }
//                HousemaidPayrollBean targetBean = new HousemaidPayrollBean(housemaidBeanInfo, false);
//                targetBean.setPreviouslyUnpaidSalaries(housemaidSalary);
//                targetBean.setTotatIcome(housemaidSalary);
//                targetBean.setBalance(housemaidSalary);
//                targetBean.setTotalBalance(housemaidSalary);
//
//                totalAnsari += targetBean.getTotalBalance();
//                targetBean.setIndexNum(String.valueOf(++counter));
//                targetBean.setSn("H-" + (counter));
//                result.add(targetBean);
//
//            }
//
//            DebugHelper.sendMail("<EMAIL>", "After fill housemaids info!");
//
//            HousemaidPayrollPaymentServiceV2.clearHousemaidPayrollInitializer();
//
//            String fileName = "Housemaids Payroll of " + DateUtil.formatSimpleMonth(rule.getPayrollMonth()) + " NOT_FINAL.xlsx";
//
//            return PayrollGenerationLibrary.generateHousemaidPayrollFile(fileName, result, totalAnsari);
//
//        } catch (Exception e) {
//            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating housemaids detailed payroll file!", false);
//        } finally {
//            HousemaidPayrollPaymentServiceV2.clearHousemaidPayrollInitializer();
//        }
//        return null;
//    }

//    public Attachment generateOfficeStaffDetailedPayrollFile(List<OfficeStaff> activeStaffs, List<OfficeStaff> terminatedStaffs, List<OfficeStaff> excludedStaffs, MonthlyPaymentRule rule) {
//        int counter = 0;
//
//        LocalDate payrollStart = new LocalDate(officeStaffPayrollPaymentServiceV2.getPayrollStartLockDate(rule));
//        LocalDate payrollEnd = new LocalDate(rule.getLockDate());
//        LocalDate payrollMonth = new LocalDate(rule.getPayrollMonth());
//        List<OfficeStaffPayrollBean> activeStaffsResult = new ArrayList<>();
//        List<OfficeStaffPayrollBean> terminatedStaffsResult = new ArrayList<>();
//        List<OfficeStaffPayrollBean> excludedStaffsResult = new ArrayList<>();
//
//        // Active Office Staffs
//        for (OfficeStaff staff : activeStaffs) {
//            OfficeStaffPayrollBean bean = this.officeStaffPayrollPaymentServiceV2.runTransactions(staff, payrollStart, payrollEnd, payrollMonth, false);
//            bean = OfficeStaffPayrollAuditService.setOfficeStaffInfo(bean, staff, rule);
//            //index
//            bean.setIndexNum(++counter);
//            activeStaffsResult.add(bean);
//        }
//
//        // Terminated Office Staffs
//        counter = 0;
//        for (OfficeStaff staff : terminatedStaffs) {
//            OfficeStaffPayrollBean bean = this.officeStaffPayrollPaymentServiceV2.runTransactions(staff, payrollStart, payrollEnd, payrollMonth, false);
//            bean = OfficeStaffPayrollAuditService.setOfficeStaffInfo(bean, staff, rule);
//            //index
//            bean.setIndexNum(++counter);
//            terminatedStaffsResult.add(bean);
//        }
//
//        // Excluded Office Staffs
//        counter = 0;
//        for (OfficeStaff staff : excludedStaffs) {
//            OfficeStaffPayrollBean bean = this.officeStaffPayrollPaymentServiceV2.runTransactions(staff, payrollStart, payrollEnd, payrollMonth, false);
//            bean = OfficeStaffPayrollAuditService.setOfficeStaffInfo(bean, staff, rule);
//            //index
//            bean.setIndexNum(++counter);
//            excludedStaffsResult.add(bean);
//        }
//
//        String fileName = activeStaffs.get(0).getEmployeeType() + " Payroll of " + DateUtil.formatSimpleMonth(rule.getPayrollMonth()) + ".xlsx";
//
//        //Jirra ACC-479
//        try {
//            return PayrollGenerationLibrary.generateOfficeStaffPayrollFile(fileName, activeStaffsResult, terminatedStaffsResult, excludedStaffsResult, activeStaffs.get(0).getEmployeeType().toString());
//        } catch (Exception e) {
//            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating office staff detailed payroll file!", false);
//        }
//        return null;
//    }

    public Attachment generateHousemaidFinalDetailedPayrollFile(List<HousemaidPayrollLog> logs, MonthlyPaymentRule rule, Map<Long, List<HousemaidPayrollLog>> maidVisaLogsForPreviousMonthsMap, List<HousemaidPayrollLog> excludedHousemaidPayrollLogs, PayrollAccountantTodoType todoType) {

        //save the bean info of excluded maids
        try {
            int counter = 0;
            for (HousemaidPayrollLog log : excludedHousemaidPayrollLogs) {
                HousemaidPayrollBean bean = log.getHousemaidPayrollBean();
                if(bean.getId() != null)
                    bean = Setup.getRepository(HousemaidPayrollBeanRepository.class).findOne(bean.getId());
                bean.setIndexNum(String.valueOf(++counter));
                bean.setSn("H-" + (counter));

                //fill prorated fields from the Payroll log
                bean.setAccommodationSalary(log.getAccommodationSalary());
                bean.setFullSalary(log.getBasicSalary());
                bean.setMohreSalary(log.getPrimarySalary());
                bean.setGroupOneDays(log.getGroupOneDays());
                bean.setGroupTwoDays(log.getGroupTwoDays());
                bean.setGroupThreeDays(log.getGroupThreeDays());
                bean.setEarningInGroupOne(log.getTotalProRatedSalary());
                bean.setEarningInGroupTwo(log.getMohreProRatedSalary());
                HousemaidBeanInfo housemaidBeanInfo = new HousemaidBeanInfo(log, bean);
                Setup.getRepository(HousemaidBeanInfoRepository.class).save(housemaidBeanInfo);
            }
        }catch (Exception ex){
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while save the bean info of excluded maids!", false);
        }

        Map<Long, List<HousemaidPayrollLog>> previousLogsMap;
        if(maidVisaLogsForPreviousMonthsMap != null)
            previousLogsMap = new HashMap<>(maidVisaLogsForPreviousMonthsMap);
        else
            previousLogsMap = new HashMap<>();

        try {
            int counter = 0;
            Double totalAnsari = 0d;
            List<HousemaidPayrollBean> result = new ArrayList<>();
            for(HousemaidPayrollLog log: logs) {
                HousemaidPayrollBean bean = log.getHousemaidPayrollBean();
                if(bean.getId() != null)
                    bean = Setup.getRepository(HousemaidPayrollBeanRepository.class).findOne(bean.getId());
                bean.setIndexNum(String.valueOf(++counter));
                bean.setSn("H-" + (counter));

                //fill prorated fields from the Payroll log
                bean.setAccommodationSalary(log.getAccommodationSalary());
                bean.setFullSalary(log.getBasicSalary());
                bean.setMohreSalary(log.getPrimarySalary());
                bean.setGroupOneDays(log.getGroupOneDays());
                bean.setGroupTwoDays(log.getGroupTwoDays());
                bean.setGroupThreeDays(log.getGroupThreeDays());
                bean.setEarningInGroupOne(log.getTotalProRatedSalary());
                bean.setEarningInGroupTwo(log.getMohreProRatedSalary());
                HousemaidBeanInfo oldBeanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).findTopByHousemaidPayrollLog(log);
                if(oldBeanInfo != null){
                    oldBeanInfo.setTotatIcome(bean.getTotatIcome());
                    oldBeanInfo.setTotalBalance(bean.getTotalBalance());
                    oldBeanInfo.setManagerAddition(bean.getManagerAddition());
                    oldBeanInfo.setBalance(bean.getBalance());
                    Setup.getRepository(HousemaidBeanInfoRepository.class).save(oldBeanInfo);
                }else if (log.getId() != null){
                    HousemaidBeanInfo housemaidBeanInfo = new HousemaidBeanInfo(log, bean);
                    Setup.getRepository(HousemaidBeanInfoRepository.class).save(housemaidBeanInfo);
                }

                // set balance to current balance + the previous months in case maid visa and got payments for old months
                double previousMonthsHousemaidSalary = 0.0;
                if (HousemaidType.MAID_VISA.equals(log.getHousemaid().getHousemaidType())) {
                    Long housemaidId = log.getHousemaid().getId();
                    List<HousemaidPayrollLog> previousMonthsLogs = previousLogsMap.get(housemaidId);

                    if (previousMonthsLogs != null) {
                        for (HousemaidPayrollLog previousLog : previousMonthsLogs)
                            previousMonthsHousemaidSalary += previousLog.getTotalSalary();

                        previousLogsMap.remove(housemaidId);
                        double totatIcome = bean.getTotatIcome();
                        double balance = bean.getBalance();
                        double totalBalance = bean.getTotalBalance();
                        bean.setPreviouslyUnpaidSalaries(previousMonthsHousemaidSalary);
                        bean.setTotatIcome(totatIcome + previousMonthsHousemaidSalary);
                        bean.setBalance(balance + previousMonthsHousemaidSalary);
                        bean.setTotalBalance(totalBalance + previousMonthsHousemaidSalary);
                    }
                }

                totalAnsari += bean.getTotalBalance();
                result.add(bean);
            }

            for (Map.Entry<Long, List<HousemaidPayrollLog>> entry : previousLogsMap.entrySet()) {
                List<HousemaidPayrollLog> previousLogs = entry.getValue();
                Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(previousLogs.get(0).getHousemaid().getId());

                Double housemaidSalary = 0.0;
                for (HousemaidPayrollLog log : previousLogs)
                    housemaidSalary += log.getTotalSalary();

                //all are the same needed date
                HousemaidPayrollLog log = previousLogs.get(0);

                HousemaidBeanInfo housemaidBeanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).findTopByHousemaidPayrollLog(log);
                if(housemaidBeanInfo == null) {
                    housemaidBeanInfo = housemaidPayrollPaymentServiceV2.createBeanInfoDetails(housemaid, log);
                    housemaidBeanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).save(housemaidBeanInfo);
                }
                HousemaidPayrollBean bean = new HousemaidPayrollBean(housemaidBeanInfo, false);
                bean.setIndexNum(String.valueOf(++counter));
                bean.setSn("H-" + (counter));
                bean.setPreviouslyUnpaidSalaries(housemaidSalary);
                bean.setTotatIcome(housemaidSalary);
                bean.setBalance(housemaidSalary);
                bean.setTotalBalance(housemaidSalary);
//                bean.setHousemaid(log.getHousemaid());
//                bean.setHousemaidName(log.getHousemaid().getName());

                totalAnsari += bean.getTotalBalance();
                result.add(bean);

            }

            String fileName = "Housemaids Payroll of " + DateUtil.formatSimpleMonth(rule.getPayrollMonth())  + " - " + (todoType.equals(PayrollAccountantTodoType.WPS) ? "WPS transfer" : StringHelper.enumToCapitalizedFirstLetter(todoType.toString()))
                    + (rule.getPayrollType() == null ? "" : " - " + rule.getPayrollType().getLabel())
                    + " .xlsx";

            return PayrollGenerationLibrary.generateHousemaidPayrollFile(fileName, result, totalAnsari);

        } catch (Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating housemaids final detailed payroll file!", false);
        }
        return null;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public PayrollAccountantTodo generateHousemaidFinalDetailedPayrollFileNew(List<HousemaidPayrollLog> logs, PayrollAccountantTodo todo, MonthlyPaymentRule rule, Map<Long, List<HousemaidPayrollLog>> maidVisaLogsForPreviousMonthsMap, PayrollAccountantTodoType todoType, boolean finalFile) {
        todo = Setup.getRepository(PayrollAccountantTodoRepository.class).getById(todo.getId());
        Attachment housemaidsDetailedFile = Setup.getApplicationContext().getBean(AuditFilesService.class).generateHousemaidFinalDetailedPayrollFileNew(logs, rule, maidVisaLogsForPreviousMonthsMap, todoType, finalFile);

        if(housemaidsDetailedFile != null) {
            todo.addAttachment(housemaidsDetailedFile);
            todo = Setup.getRepository(PayrollAccountantTodoRepository.class).save(todo);
        }

        return todo;
    }

    public Attachment generateHousemaidFinalDetailedPayrollFileNew(List<HousemaidPayrollLog> logs, MonthlyPaymentRule rule, Map<Long, List<HousemaidPayrollLog>> maidVisaLogsForPreviousMonthsMap, PayrollAccountantTodoType todoType, boolean finalFile) {
//        DebugHelper.sendMail("<EMAIL>", "Start generateHousemaidFinalDetailedPayrollFileNew for #" + logs.size() + " logs && #" + maidVisaLogsForPreviousMonthsMap.size() + " maidVisaLogsForPreviousMonthsMap.");

        if(logs != null && logs.size() == 0
                && maidVisaLogsForPreviousMonthsMap != null && maidVisaLogsForPreviousMonthsMap.size() == 0)
            return null;

        Map<Long, List<HousemaidPayrollLog>> previousLogsMap;
        if(maidVisaLogsForPreviousMonthsMap != null)
            previousLogsMap = new HashMap<>(maidVisaLogsForPreviousMonthsMap);
        else
            previousLogsMap = new HashMap<>();

        try {
            int counter = 0;
            Double totalAnsari = 0d;
            List<HousemaidPayrollBean> result = new ArrayList<>();
            for(HousemaidPayrollLog log: logs) {
                HousemaidPayrollBean bean = log.getHousemaidPayrollBean();
                if(bean.getId() != null)
                    bean = Setup.getRepository(HousemaidPayrollBeanRepository.class).findOne(bean.getId());
                bean.setIndexNum(String.valueOf(++counter));
                bean.setSn("H-" + (counter));

                //fill prorated fields from the Payroll log
                bean.setAccommodationSalary(log.getAccommodationSalary());
                bean.setFullSalary(log.getBasicSalary());
                bean.setMohreSalary(log.getPrimarySalary());
                bean.setGroupOneDays(log.getGroupOneDays());
                bean.setGroupTwoDays(log.getGroupTwoDays());
                bean.setGroupThreeDays(log.getGroupThreeDays());
                bean.setGroupFourDays(log.getGroupFourDays());
                bean.setGroupFiveDays(log.getGroupFiveDays());
                bean.setGroupSixDays(log.getGroupSixDays());

                bean.setEarningInGroupOne(log.getTotalProRatedSalary());
                bean.setEarningInGroupTwo(log.getMohreProRatedSalary());
                bean.setEarningInGroupFour(log.getVacationSalary());
                bean.setEarningInGroupFive(log.getTotalLiveOutProRatedSalary());
                bean.setEarningInGroupSix(log.getMohreLiveOutProRatedSalary());


                // set balance to current balance + the previous months in case maid visa and got payments for old months
                double previousMonthsHousemaidSalary = 0.0;
                if (HousemaidType.MAID_VISA.equals(log.getHousemaid().getHousemaidType())) {
                    Long housemaidId = log.getHousemaid().getId();
                    List<HousemaidPayrollLog> previousMonthsLogs = previousLogsMap.get(housemaidId);

                    if (previousMonthsLogs != null) {
                        for (HousemaidPayrollLog previousLog : previousMonthsLogs)
                            previousMonthsHousemaidSalary += previousLog.getTotalSalary();

                        previousLogsMap.remove(housemaidId);
                        double totatIcome = bean.getTotatIcome();
                        double balance = bean.getBalance();
                        double totalBalance = bean.getTotalBalance();
                        bean.setPreviouslyUnpaidSalaries(previousMonthsHousemaidSalary);
                        bean.setTotatIcome(totatIcome + previousMonthsHousemaidSalary);
                        bean.setBalance(balance + previousMonthsHousemaidSalary);
                        bean.setTotalBalance(totalBalance + previousMonthsHousemaidSalary);
                    }
                }

                totalAnsari += bean.getTotalBalance();
                result.add(bean);
            }

            for (Map.Entry<Long, List<HousemaidPayrollLog>> entry : previousLogsMap.entrySet()) {
                List<HousemaidPayrollLog> previousLogs = entry.getValue();
                Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(previousLogs.get(0).getHousemaid().getId());

                Double housemaidSalary = 0.0;
                for (HousemaidPayrollLog log : previousLogs)
                    housemaidSalary += log.getTotalSalary();

                //all are the same needed date
                HousemaidPayrollLog log = previousLogs.get(0);

                HousemaidPayrollBean bean = log.getHousemaidPayrollBean();
                if(bean == null) {
                    HousemaidBeanInfo housemaidBeanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).findTopByHousemaidPayrollLog(log);
                    if (housemaidBeanInfo == null) {
                        housemaidBeanInfo = housemaidPayrollPaymentServiceV2.createBeanInfoDetails(housemaid, log);
                        housemaidBeanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).save(housemaidBeanInfo);
                    }
                    bean = new HousemaidPayrollBean(housemaidBeanInfo, false);
                }
                if(bean != null && bean.getId() != null)
                    bean = Setup.getRepository(HousemaidPayrollBeanRepository.class).findOne(bean.getId());
                bean.setIndexNum(String.valueOf(++counter));
                bean.setSn("H-" + (counter));
                bean.setPreviouslyUnpaidSalaries(housemaidSalary);
                bean.setTotatIcome(housemaidSalary);
                bean.setBalance(housemaidSalary);
                bean.setTotalBalance(housemaidSalary);
//                bean.setHousemaid(log.getHousemaid());
//                bean.setHousemaidName(log.getHousemaid().getName());

                totalAnsari += bean.getTotalBalance();
                result.add(bean);

            }

            String fileName = "Housemaids Payroll of " + DateUtil.formatSimpleMonth(rule.getPayrollMonth());
            fileName += finalFile ? " - " + (todoType.equals(PayrollAccountantTodoType.WPS) ? "WPS transfer" : StringHelper.enumToCapitalizedFirstLetter(todoType.toString()))
                    + (rule.getPayrollType() == null ? "" : " - " + rule.getPayrollType().getLabel())
                    + " .xlsx"
            :  " NOT_FINAL.xlsx";

            return PayrollGenerationLibrary.generateHousemaidPayrollFile(fileName, result, totalAnsari);

        } catch (Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating housemaids final detailed payroll file!", false);
        }
        return null;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public PayrollAccountantTodo generateOfficeStaffFinalDetailedPayrollFile(List<OfficeStaffPayrollLog> logs, PayrollAccountantTodo todo, List<OfficeStaffPayrollLog> terminatedStaffsLogs, List<OfficeStaffPayrollLog> excludedStaffsLogs, MonthlyPaymentRule rule, PayrollAccountantTodoType todoType, boolean finalFile) {
        todo = Setup.getRepository(PayrollAccountantTodoRepository.class).getById(todo.getId());
        Attachment staffDetailedFile = Setup.getApplicationContext().getBean(AuditFilesService.class).generateOfficeStaffFinalDetailedPayrollFile(logs, terminatedStaffsLogs, excludedStaffsLogs, rule, todoType, finalFile);

        if(staffDetailedFile != null) {
            todo.addAttachment(staffDetailedFile);
            todo = Setup.getRepository(PayrollAccountantTodoRepository.class).save(todo);
        }

        return todo;
    }

    public Attachment generateOfficeStaffFinalDetailedPayrollFile(List<OfficeStaffPayrollLog> logs, List<OfficeStaffPayrollLog> terminatedStaffsLogs, List<OfficeStaffPayrollLog> excludedStaffsLogs, MonthlyPaymentRule rule, PayrollAccountantTodoType todoType, boolean finalFile) {

        if(logs != null && logs.size() == 0
                && terminatedStaffsLogs != null && terminatedStaffsLogs.size() == 0
                && excludedStaffsLogs != null && excludedStaffsLogs.size() == 0) return null;

        // Active Office Staffs
        int counter = 0;
        List<OfficeStaffPayrollBean> activeStaffsResult = new ArrayList<>();
        for (OfficeStaffPayrollLog log : logs) {
            OfficeStaffPayrollBean bean = log.getOfficeStaffPayrollBean();
            if(bean.getId()!=null)
                bean = Setup.getRepository(OfficeStaffPayrollBeanRepository.class).findOne(bean.getId());
            bean = OfficeStaffPayrollAuditService.setOfficeStaffInfo(bean, log.getOfficeStaff(), rule);
            //index
            bean.setIndexNum(++counter);
            activeStaffsResult.add(bean);
        }

        // Terminated Office Staffs
        counter = 0;
        List<OfficeStaffPayrollBean> terminatedStaffsResult = new ArrayList<>();
        for (OfficeStaffPayrollLog log : terminatedStaffsLogs) {
            OfficeStaffPayrollBean bean = log.getOfficeStaffPayrollBean();
            if(bean.getId()!=null)
                bean = Setup.getRepository(OfficeStaffPayrollBeanRepository.class).findOne(bean.getId());
            bean = OfficeStaffPayrollAuditService.setOfficeStaffInfo(bean, log.getOfficeStaff(), rule);
            //index
            bean.setIndexNum(++counter);
            terminatedStaffsResult.add(bean);
        }

        // Excluded Office Staffs
        counter = 0;
        List<OfficeStaffPayrollBean> excludedStaffsResult = new ArrayList<>();
        for (OfficeStaffPayrollLog log : excludedStaffsLogs) {
            OfficeStaffPayrollBean bean = log.getOfficeStaffPayrollBean();
            if(bean.getId()!=null)
                bean = Setup.getRepository(OfficeStaffPayrollBeanRepository.class).findOne(bean.getId());
            bean = OfficeStaffPayrollAuditService.setOfficeStaffInfo(bean, log.getOfficeStaff(), rule);
            //index
            bean.setIndexNum(++counter);
            excludedStaffsResult.add(bean);
        }
        String fileName = finalFile ? logs.get(0).getOfficeStaff().getEmployeeType().getLabel() + " Payroll of " + DateUtil.formatSimpleMonth(rule.getPayrollMonth()) + " - " + (todoType.equals(PayrollAccountantTodoType.WPS) ? "WPS transfer" : StringHelper.enumToCapitalizedFirstLetter(todoType.toString()))
                    + (rule.getPayrollType() == null ? "" : " - " + rule.getPayrollType().getLabel())
                    + " .xlsx"
                : logs.get(0).getOfficeStaff().getEmployeeType() + " Payroll of " + DateUtil.formatSimpleMonth(rule.getPayrollMonth()) + ".xlsx";

        //Jirra ACC-479
        try {
            return PayrollGenerationLibrary.generateOfficeStaffPayrollFile(fileName, activeStaffsResult, terminatedStaffsResult, excludedStaffsResult, logs.get(0).getOfficeStaff().getEmployeeType().toString());
        } catch (Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating office staff final detailed payroll file!", false);
        }
        return null;
    }

    public Double valueOrZero(Double value) {
        return value == null ? 0d: value;
    }
}
