package com.magnamedia.service.payroll.generation.newversion;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.mail.*;
import com.magnamedia.entity.*;
import com.magnamedia.entity.OfficeStaffPayrollBean;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.helper.*;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.repository.PayrollRosterApproveRequestRepository;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.newVersion2.OfficeStaffPayrollPaymentServiceV2;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Date;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class PayrollRosterApprovalService {

    @Autowired
    private OfficeStaffRepository officeStaffRepository;

    @Autowired
    private OfficeStaffPayrollPaymentServiceV2 officeStaffPayrollPaymentServiceV2;

    @Autowired
    private PublicPageHelper publicPageHelper;

    @Autowired
    private PayrollRosterApproveRequestRepository payrollRosterApproveRequestRepository;

    @Autowired
    private MailService mailService;

    @Transactional
    public boolean sendApprovalMails(Boolean finalManagers, Boolean isEmarati) {

        try {

            java.sql.Date payrollMonth = new java.sql.Date(new LocalDate().withDayOfMonth(1).toDate().getTime());

            Map<OfficeStaff, OfficeStaff> managersTree = getManagersTree(payrollMonth);

            List<OfficeStaff> receivers = finalManagers ? getFinalManagers(managersTree) : getSubFinalManagers(managersTree);

            for (OfficeStaff officeStaff : receivers) {

                List<OfficeStaff> employees = getSubEmployees(officeStaff, managersTree, isEmarati);

                if (employees.isEmpty()) continue;

                Attachment attachment = generateOfficeStaffDetailedPayrollFile(employees, " for " + officeStaff.getShortName() + (isEmarati ? " - Emirati" : ""), null);
                if (attachment != null) {

                    PayrollRosterApproveRequest request = new PayrollRosterApproveRequest(officeStaff, payrollMonth);
                    request.setFinalManager(finalManagers);
                    request.setForEmarati(isEmarati);
                    request.addAttachment(attachment);
                    payrollRosterApproveRequestRepository.save(request);

                    String url = publicPageHelper.generatePublicURL(PublicPageHelper.FINAL_MANAGER_APPROVE, request.getId().toString() + "#Payroll_Roster_Approval", String.valueOf(officeStaff.getUser().getId()));

                    String subject = "Payroll Roster of " + DateUtil.formatSimpleMonth(payrollMonth) + (isEmarati ? " - Emirati" : "");
                    List<EmailRecipient> recipients = Recipient.parseEmailsString(officeStaff.getUser().getEmail());

                    if (officeStaff.getEmail() != null) {
                        Map<String, String> params = new HashMap<>();
                        params.put("firstLastName", officeStaff.getShortName());
                        params.put("url", url);
                        Setup.getApplicationContext().getBean(MessagingService.class)
                                .send(recipients, null, "Payroll_Send_Payroll_Roster_File", subject
                                        , params, Collections.singletonList(attachment), null);
//                        TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_Send_Payroll_Roster_File", params);
//                        templateEmail.addAttachement(attachment);
//                        mailService.sendEmail(new MailObject.builder(templateEmail, EmailReceiverType.Office_Staff)
//                                .recipients(recipients)
//                                .senderName(MessageTemplateService.getMaidsCcSenderName())
//                                .html()
//                                .secure()
//                                .build());
                    }
                }
            }
        }catch (Exception e){
            Logger.getLogger(PayrollRosterApprovalService.class.getName()).log(Level.SEVERE, "Error while send Payroll Roster Approval Requests!" + e.getMessage());
            e.printStackTrace();
            throw e;
        }
        return true;
    }

    @Transactional
    @Async
    public void sendApprovalMailToSelectedManager(PayrollRosterApproveRequest rosterApproveRequest) {

        Boolean isEmarati = rosterApproveRequest.getForEmarati();
        OfficeStaff manager = rosterApproveRequest.getManager();
        java.sql.Date payrollMonth = new java.sql.Date(new LocalDate().withDayOfMonth(1).toDate().getTime());

        Map<OfficeStaff, OfficeStaff> managersTree = getManagersTree(payrollMonth);

        java.util.Date lastUpdateFile = null;
        Attachment lastRosterPayrollFile = rosterApproveRequest.getAttachment("RosterPayrollFile");
        if (lastRosterPayrollFile != null) {
            lastUpdateFile = lastRosterPayrollFile.getLastModificationDate();
        }

        if(manager != null){

            List<OfficeStaff> employees = getSubEmployees(manager, managersTree, isEmarati);

            if (employees.isEmpty()) return;

            Attachment attachment = generateOfficeStaffDetailedPayrollFile(employees, " for " + manager.getShortName() + (isEmarati ? " - Emirati" : ""), lastUpdateFile);
            if(attachment != null) {

                rosterApproveRequest.setActionTaken(false);
                rosterApproveRequest.addAttachment(attachment);

                String url = publicPageHelper.generatePublicURL(PublicPageHelper.FINAL_MANAGER_APPROVE, rosterApproveRequest.getId().toString() + "#Payroll_Roster_Approval", String.valueOf(manager.getUser().getId()));

                String subject = (rosterApproveRequest.getRosterNotes() != null ? "Edited " : "") + "Payroll Roster of " + DateUtil.formatSimpleMonth(payrollMonth) + (isEmarati ? " - Emirati" : "");
                String templateName = rosterApproveRequest.getRosterNotes() != null ? "Payroll_Send_Payroll_Roster_File_Edited" : "Payroll_Send_Payroll_Roster_File";
                List<EmailRecipient> recipients = Recipient.parseEmailsString(manager.getUser().getEmail());

                if (manager.getEmail() != null) {
                    Map<String, String> params = new HashMap<>();
                    params.put("firstLastName",manager.getShortName());
                    params.put("url",url);
                    Setup.getApplicationContext().getBean(MessagingService.class)
                            .send(recipients, null, templateName, subject
                                    , params, Collections.singletonList(attachment), null);
//                    TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_Send_Payroll_Roster_File", params);
//                    templateEmail.addAttachement(attachment);
//                    mailService.sendEmail(new MailObject.builder(templateEmail, EmailReceiverType.Office_Staff)
//                            .recipients(recipients)
//                            .senderName(MessageTemplateService.getMaidsCcSenderName())
//                            .html()
//                            .secure()
//                            .build());
                }
                payrollRosterApproveRequestRepository.save(rosterApproveRequest);
            }
        }
    }

    public Attachment generateOfficeStaffDetailedPayrollFile(List<OfficeStaff> officeStaffs, String forWhom, java.util.Date lastRosterFileUpdated) {
        int counter = 0;

        java.sql.Date payrollMonth = new java.sql.Date(new LocalDate().withDayOfMonth(1).toDate().getTime());

        LocalDate payrollMonthDate = new LocalDate(payrollMonth);

        List<OfficeStaffPayrollBean> result = new ArrayList<>();
        for (OfficeStaff staff : officeStaffs) {
            MonthlyPaymentRule rule = new MonthlyPaymentRule();
            rule.setPayrollMonth(payrollMonth);

            PaymentRuleEmployeeType type = PaymentRuleEmployeeType.OVERSEAS;
            if(staff.getEmployeeType() == OfficeStaffType.DUBAI_STAFF_EMARATI) type = PaymentRuleEmployeeType.EMIRATI;
            else if(staff.getEmployeeType() == OfficeStaffType.DUBAI_STAFF_EXPAT) type = PaymentRuleEmployeeType.EXPATS;

            rule.setEmployeeTypeList(Arrays.asList(type));

            LocalDate payrollStart = new LocalDate(officeStaffPayrollPaymentServiceV2.getPayrollStartLockDate(rule));
            //end
            LocalDate payrollEnd = new LocalDate().plusDays(1);

            java.util.Date payrollStartForManagerNotes = officeStaffPayrollPaymentServiceV2
                    .getPayrollStartLockDateForManagerNotes(rule);
            java.util.Date payrollEndForManagerNotes = officeStaffPayrollPaymentServiceV2
                    .getPayrollEndLockDateForManagerNotes(rule);

            SelectQuery<MonthlyPaymentRule> selectQuery1 = new SelectQuery<>(MonthlyPaymentRule.class);
            selectQuery1.filterBy("lockDate", "=", new Date(System.currentTimeMillis()));
            selectQuery1.filterBy("employeeTypeList", "MEMBER OF", type);
            selectQuery1.filterBy("singleHousemaid", "=", false);
            selectQuery1.filterBy("singleOfficeStaff", "=", false);
            List<MonthlyPaymentRule> monthlyPaymentRules = selectQuery1.execute();
//                    Setup.getRepository(MonthlyPaymentRuleRepository.class).findByLockDateAndEmployeeType(new Date(System.currentTimeMillis()), type);
            for (MonthlyPaymentRule monthlyPaymentRule : monthlyPaymentRules){
                if (monthlyPaymentRule.isTargetingOfficeStaff()){
                    payrollEnd = new LocalDate();
                    break;
                }
            }
            if(staff.getStartingDate() == null || staff.getStartingDate().compareTo(payrollEnd.toDate()) > 0)
                continue;

            OfficeStaffPayrollBean bean = officeStaffPayrollPaymentServiceV2.runTransactions(staff, payrollStart, payrollEnd, payrollStartForManagerNotes, payrollEndForManagerNotes, payrollMonthDate, false);


            if (lastRosterFileUpdated != null) {
                boolean shouldBeHighlighted = officeStaffRepository.hasNoteOrLoanOrRepaymentAfterDate(staff.getId(), lastRosterFileUpdated);
                bean.setShouldBeHighlighted(shouldBeHighlighted);
            }

            //set AdditionsReasons
            SelectQuery<PayrollManagerNote> query = new SelectQuery<>(PayrollManagerNote.class);
            query.filterBy("officeStaff.id", "=", staff.getId());
            query.filterBy("noteDate", ">=", payrollStartForManagerNotes);
            query.filterBy("noteDate", "<", payrollEndForManagerNotes);
            query.filterBy("noteType", "=", PayrollManagerNote.ManagerNoteType.BONUS);
            query.filterBy("amount", ">", 0.0);
            List<PayrollManagerNote> notes = query.execute();
            String additionsAndRaisesReasons = "";
            for (PayrollManagerNote temp : notes)
                additionsAndRaisesReasons += temp.getAdditionReason() != null ? " * " + NumberFormatter.formatNumber(temp.getAmount()) + " " + temp.getAdditionReason().getName() + "\n" : "";

            //set RaisesReasons
            query = new SelectQuery<>(PayrollManagerNote.class);
            query.filterBy("officeStaff.id", "=", staff.getId());
            query.filterBy("noteDate", ">=", payrollStartForManagerNotes);
            query.filterBy("noteDate", "<", payrollEndForManagerNotes);
            query.filterBy("noteType", "=", PayrollManagerNote.ManagerNoteType.SALARY_RAISE);
            notes = query.execute();
            for (PayrollManagerNote temp : notes)
                additionsAndRaisesReasons += temp.getNoteReasone() != null ? " * " + NumberFormatter.formatNumber(temp.getAmount()) + " " + StringHelper.enumToCapitalizedFirstLetter(temp.getNoteType().name()) + "/" + temp.getNoteReasone() + "\n" : "";

            bean.setAdditionsReasons(additionsAndRaisesReasons);

            //set DeductionsReasons
            query = new SelectQuery<>(PayrollManagerNote.class);
            query.filterBy("officeStaff.id", "=", staff.getId());
            query.filterBy("noteDate", ">=", payrollStartForManagerNotes);
            query.filterBy("noteDate", "<", payrollEndForManagerNotes);
            query.filterBy("noteType", "=", PayrollManagerNote.ManagerNoteType.DEDUCTION);
            query.filterBy("amount", ">", 0.0);
            notes = query.execute();
            String deductionsAndReductionsReasons = "";
            for (PayrollManagerNote temp : notes)
                deductionsAndReductionsReasons += temp.getDeductionReason() != null ? " * " + NumberFormatter.formatNumber(temp.getAmount()) + " " + temp.getDeductionReason().getName() + "\n" : "";

            //set ReductionsReasons
            query = new SelectQuery<>(PayrollManagerNote.class);
            query.filterBy("officeStaff.id", "=", staff.getId());
            query.filterBy("noteDate", ">=", payrollStartForManagerNotes);
            query.filterBy("noteDate", "<", payrollEndForManagerNotes);
            query.filterBy("noteType", "=", PayrollManagerNote.ManagerNoteType.REDUCTION);
            notes = query.execute();
            for (PayrollManagerNote temp : notes)
                deductionsAndReductionsReasons += temp.getNoteReasone() != null ? " * " + NumberFormatter.formatNumber(temp.getAmount()) + " " + StringHelper.enumToCapitalizedFirstLetter(temp.getNoteType().name()) + "/" + temp.getNoteReasone() + "\n" : "";

            bean.setDeductionsReasons(deductionsAndReductionsReasons);

            //setLoansReasons
            if(staff.getTerminationDate() == null || staff.getFinalSettlement() == null) {
                SelectQuery<EmployeeLoan> selectQuery = new SelectQuery<>(EmployeeLoan.class);
                selectQuery.filterBy("officeStaff.id", "=", staff.getId());
                selectQuery.filterBy("loanDate", ">=", payrollStart.toDate());
                selectQuery.filterBy("loanDate", "<", payrollEnd.toDate());
                selectQuery.filterBy("isShown", "=", true);
                selectQuery.filterBy("shouldBePaid", "=", true);
                selectQuery.filterBy("amount", "is not null", null);
                List<EmployeeLoan> employeeLoansNotes = selectQuery.execute();
                String loansReasons = "";
                for (EmployeeLoan temp : employeeLoansNotes)
                    loansReasons += " * " + NumberFormatter.formatNumber(temp.getAmount()) + " " + (temp.getLoanType() != null ? StringHelper.enumToCapitalizedFirstLetter(temp.getLoanType().name()) : "") + "/" + (temp.getNotes() != null ? temp.getNotes() : "") + "\n";
                bean.setLoansReasons(loansReasons);
            }

            bean = this.setOfficeStaffInfo(bean, staff, payrollMonth);
            //index
            bean.setIndexNum(++counter);
            OfficeStaff rosterManager = getRosterManager(staff);
            String rosterManagerName;
            if(rosterManager != null && rosterManager.getName() != null) {
                rosterManagerName = rosterManager.getName();
            }else{
                rosterManagerName = "";
            }
            bean.setRosterManager(rosterManagerName);
            result.add(bean);
        }

        String fileName = "Payroll roster of " + DateUtil.formatSimpleMonth(payrollMonth) + forWhom + ".xlsx";

        //Jirra ACC-479
        try {
            return PayrollGenerationLibrary.generateOfficeStaffRosterFile(fileName, result);
        } catch (Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating office staff detailed payroll file!", false);
        }
        return null;
    }


    /**
     * Set OfficeStaff information:
     * name, team, city, local nationality, payment method, salary currency,
     * employee unique id, agent id, employee account with agent, pLcode
     * Return OfficeStaffPayrollBean
     *
     * @param bean
     * @param staff
     * @return
     */
    public OfficeStaffPayrollBean setOfficeStaffInfo(
            OfficeStaffPayrollBean bean,
            OfficeStaff staff,
            java.sql.Date  payrollMonth) {

        bean.setExcludedFromSalary(staff.getExcludedFromPayroll() != null && staff.getExcludedFromPayroll());
        //Staff name
        bean.setOfficeStaffName(staff.getName());
        bean.setTeam(staff.getTeam() != null ? staff.getTeam().getName() : "");
        bean.setDepartments((staff.getDepartments() != null && !staff.getDepartments().isEmpty()) ? staff.getDepartmentNames() : ""); //todo: check with Team leader if needed

        if(staff.getEmployeeManager() != null) {
            bean.setManager(staff.getEmployeeManager().getName());
        }

        if(staff.getCountry() != null) {
            bean.setCountry(staff.getCountry().getName());
        }
        //staff city
        if (staff.getCity() != null) {
            bean.setCity(staff.getCityName());//.getName());
        }

        //staff mail
        if (staff.getEmail() != null && !staff.getEmail().trim().equals("")) {
            bean.setOfficeStaffEMail(staff.getEmail());
        }

        //Jirra ACC-431
        if (staff.getEmployeeType() == OfficeStaffType.DUBAI_STAFF_EMARATI)
            bean.setLocal("Yes");

        //Payment Method
        if (staff.getSalaryPayementMethod() != null) {
            bean.setPaymentMethod(staff.getSalaryPayementMethod().getName());
        }
        if (staff.getSalaryCurrency() != null) {
            bean.setCurrencyUsed(staff.getSalaryCurrency().toString());
        }

        //visa info for UAE_NON_H_C ,PT_OFFICE_STAFF,STORAGE_STAFF
        NewRequest visaNewRequest = staff.getVisaNewRequest();
        if (visaNewRequest != null) {
            if (visaNewRequest.getEmployeeUniqueId() != null) {
                bean.setEmployeeUniqueId(visaNewRequest.getEmployeeUniqueId());
            }
            if (visaNewRequest.getAgentId() != null) {
                bean.setAgentId(visaNewRequest.getAgentId());
            }
            if (visaNewRequest.getEmployeeAccountWithAgent() != null) {
                bean.setEmployeeAccountWithAgent(visaNewRequest.getEmployeeAccountWithAgent());
            }
        }

        if (staff.getPnl() != null) {
            bean.setpLcode(staff.getPnl());
        }

        if (staff.getStartingDate() != null) {
            LocalDate da = new LocalDate(staff.getStartingDate());
            bean.setStartDate(DateUtil.formatFullDate(da.toDate()));

            if(staff.getStartingDate().after(payrollMonth)) {
                bean.setNewHire(true);
            }
        }
        //Jirra ACC-1066
        if (staff.getStatus() != null) {
            bean.setStatus(StringHelper.enumToCapitalizedFirstLetter(staff.getStatus().toString()));
        }

        //Jirra ACC-353
        //PAY-2
        //bean.setMohoreSalary(staff.getPrimarySalary());
        bean.setMohoreSalary(staff.getBasicSalary());

        //PAY-2
        //bean.setMonthlyCashAdvance(staff.getMonthlyLoan());
        bean.setMonthlyCashAdvance(0.0);
        bean.setPaySlipMonth(DateUtil.formatSimpleMonthYear(payrollMonth));

        bean.setJobTitle(staff.getJobTitle() != null ? staff.getJobTitle().getName() : "");
        bean.setSalaryTransferMethod(staff.getSelectedTransferDestination() != null && ReceiveMoneyMethod.BANK_TRANSFER.equals(staff.getSelectedTransferDestination().getReceiveMoneyMethod()) ? "Bank Transfer" : "Money transfer");
        bean.setNationality(staff.getNationality() != null ? staff.getNationality().getName() : "");
        return bean;
    }
    // ======================================
    public List<OfficeStaff> getAllEmployees(java.sql.Date payrollMonthDate) {
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.leftJoin("finalSettlement");

        // Active and not excluded from payroll
        SelectFilter activeAndNotExcludedFromPayroll = new SelectFilter("status", "=", OfficeStaffStatus.ACTIVE);

        // Terminated within payroll
        LocalDate payrollMonth = new LocalDate(payrollMonthDate);
        SelectFilter terminatedWithinPayrollDate = new SelectFilter("terminationDate", "IS NOT NULL", null)
                .and("terminationDate", ">=", payrollMonth.withDayOfMonth(1).toDate())
                .and("terminationDate", "<=", payrollMonth.dayOfMonth().withMaximumValue().toDate());


        // Terminated within payroll date and Final settlement is approved before lock date (New case)
        SelectFilter terminatedWithinPayrollFinalSettlementApproved = new SelectFilter("finalSettlement", "IS NOT NULL", null)
                .and("finalSettlement.approvedByCFO", "=", true)
                .and("finalSettlement.includeInPayroll", "=", true)
                .and(terminatedWithinPayrollDate);

        SelectFilter baseFilter = new SelectFilter(new SelectFilter(activeAndNotExcludedFromPayroll)
                .or(terminatedWithinPayrollFinalSettlementApproved));


        query.filterBy(baseFilter);

        return query.execute();
    }

    public Map<OfficeStaff, OfficeStaff> getManagersTree(java.sql.Date payrollMonthDate) {
        Map<OfficeStaff, OfficeStaff> managersTree = new HashMap<>();
        for(OfficeStaff officeStaff: getAllEmployees(payrollMonthDate)) {
            managersTree.put(officeStaff, officeStaff.getEmployeeManager());
        }
        return managersTree;
    }

    public List<OfficeStaff> getFinalManagers(Map<OfficeStaff, OfficeStaff> managersTree) {
        Set<OfficeStaff> allStaff = managersTree.keySet();
        return allStaff.stream().filter(staff -> managersTree.get(staff) == null || staff.getId().equals(managersTree.get(staff).getId())).collect(Collectors.toList());
    }

    List<OfficeStaff> getSubFinalManagers(Map<OfficeStaff, OfficeStaff> managersTree) {
        Set<OfficeStaff> allStaff = managersTree.keySet();
        Map<Long, Boolean> isManager = new HashMap<>();
        for(OfficeStaff officeStaff: managersTree.keySet()) {
            OfficeStaff manager = managersTree.get(officeStaff);
            if(manager != null) {
                isManager.put(manager.getId(), true);
            }
        }

        return allStaff.stream().filter(staff -> {
            OfficeStaff manager = managersTree.get(staff);
            return isManager.getOrDefault(staff.getId(), false)
                    && manager != null
                    && (managersTree.get(manager) == null || manager.getId().equals(managersTree.get(manager).getId()));
        }).collect(Collectors.toList());
    }

    public OfficeStaff getRosterManager(OfficeStaff officeStaff) {
        // example in case of: staff1 reports to staff2 reports to staff3 reports to staff4 :
        // staff1's Roster manager is staff3(Sub-Final Manager), staff2's Roster manager is staff3, staff3's Roster manager is staff4(Final Manager)

        // Get the direct manager of the current office staff
        OfficeStaff manager = officeStaff.getEmployeeManager();

        // Base case: If there is no manager, return null (no roster manager in this case)
        if (manager == null) {
            return null;
        }

        OfficeStaff managersManager = manager.getEmployeeManager();

        // If the manager reports directly to the final manager
        if (managersManager == null || manager.equals(managersManager)) {
            return manager; // The current staff reports directly to the final manager
        }

        // If the next manager in the hierarchy (manager's manager) reports directly to the final manager
        if (managersManager.getEmployeeManager() == null || managersManager.equals(managersManager.getEmployeeManager())) {
            return manager; // Return the manager before the final manager (roster manager)
        }

        // Recursive case: Keep going up the hierarchy to find the roster manager
        return getRosterManager(manager);
    }


    List<OfficeStaff> getSubEmployees(OfficeStaff manager, Map<OfficeStaff, OfficeStaff> managersTree, boolean isEmarati) {

        Map<Long, Boolean> existMap = new HashMap<>();
        for(OfficeStaff officeStaff: managersTree.keySet()) {
            existMap.put(officeStaff.getId(), true);
        }

        return manager.getAllLevelEmployeesForRoster().stream().filter(
                    staff -> existMap.getOrDefault(staff.getId(), false)
                    && (staff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI) == isEmarati)
                ).collect(Collectors.toList());

    }
}
