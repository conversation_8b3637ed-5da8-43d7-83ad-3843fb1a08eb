package com.magnamedia.service.payroll.generation.newversion;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.entity.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.OfficeStaffRepository;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Service
public class OfficeStaffPayrollAuditService {

    @Autowired
    private LockDateService lockDateService;

    @Autowired
    private OfficeStaffRepository officeStaffRepository;

    public SelectFilter getTargetListFilter(MonthlyPaymentRule monthlyPaymentRule, PaymentRuleEmployeeType employeeType) {

        SelectFilter typeFilter = new SelectFilter();

        Integer dayOfMonth = 0;
        // value of excluded Staff Due To Start Date IDs
        Set<Long> excludedStaffDueToStartDate;

        // Overseas type
        if (employeeType == PaymentRuleEmployeeType.OVERSEAS) {
            SelectFilter overseasFilter = new SelectFilter("employeeType", "=", OfficeStaffType.OVERSEAS_STAFF);
            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));
            if (monthlyPaymentRule.getCountries().size() > 0) {
                SelectFilter selfReceiver = new SelectFilter(new SelectFilter("selectedTransferDestination", "IS NULL", null)
                        .or("selectedTransferDestination.selfReceiver", "=", true));

                SelectFilter nonSelfReceiver = new SelectFilter("selectedTransferDestination", "IS NOT NULL", null)
                        .and("selectedTransferDestination.selfReceiver", "=", false);

                overseasFilter.and(new SelectFilter(selfReceiver.and("country", "IN", monthlyPaymentRule.getCountries()))
                        .or(nonSelfReceiver.and("selectedTransferDestination.country", "IN", monthlyPaymentRule.getCountries())));
            }
            typeFilter = new SelectFilter(overseasFilter);
        }
        // Expats type
        else if (employeeType == PaymentRuleEmployeeType.EXPATS) {
            SelectFilter expatsFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EXPAT);
            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));
//            if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
//                expatsFilter.and("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
//            }
            typeFilter = new SelectFilter(expatsFilter);
        }
        // Emiratis type
        else if (employeeType == PaymentRuleEmployeeType.EMIRATI) {
            SelectFilter emiratisFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EMARATI);
            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EMIRATI));
            typeFilter = new SelectFilter(emiratisFilter);
        }

        Date maxStartingDate = DateUtil.getDayEnd(DateUtil.getDayOfMonthDate(new Date(monthlyPaymentRule.getPayrollMonth().getTime()), dayOfMonth));
        excludedStaffDueToStartDate = officeStaffRepository.excludedStaff(OfficeStaffTodoType.CONFIRM_PAYROLL_QUESTIONNAIRE.toString(), DateUtil.getFirstOfMonthDate(monthlyPaymentRule.getPayrollMonth()), maxStartingDate);
        typeFilter.and("id", "not in", excludedStaffDueToStartDate);

        // Not excluded from payroll
        SelectFilter notExcludedFromPayroll = new SelectFilter("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null);

        // Active and not excluded from payroll
        SelectFilter activeAndNotExcludedFromPayroll = new SelectFilter("status", "=", OfficeStaffStatus.ACTIVE)
                .and(notExcludedFromPayroll);

        SelectFilter baseFilter = new SelectFilter(typeFilter).and(new SelectFilter(activeAndNotExcludedFromPayroll));

        return baseFilter;
    }

    public List<OfficeStaff> getTargetList(MonthlyPaymentRule monthlyPaymentRule, PaymentRuleEmployeeType employeeType) {
        if (!monthlyPaymentRule.isTargetingOfficeStaff()) return new ArrayList<>();
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.leftJoin("selectedTransferDestination");
        query.filterBy(getTargetListFilter(monthlyPaymentRule, employeeType));
        return query.execute();
    }

    public List<OfficeStaff> getTargetListForAllStaffs(MonthlyPaymentRule monthlyPaymentRule) {
        if (!monthlyPaymentRule.isTargetingOfficeStaff()) return new ArrayList<>();
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.leftJoin("selectedTransferDestination");
        query.leftJoin("finalSettlement");
        query.filterBy(getTargetListFilterForAllStaffs(monthlyPaymentRule));
        query.sortBy("name", true);
        return query.execute();
    }

    public SelectFilter getTargetListFilterForAllStaffs(MonthlyPaymentRule monthlyPaymentRule) {

        SelectFilter typeFilter = null;

        Integer dayOfMonth = null;
        Date maxStartingDate;

        SelectFilter selfReceiver = new SelectFilter(new SelectFilter("selectedTransferDestination", "IS NULL", null)
                .or("selectedTransferDestination.selfReceiver", "=", true));

        SelectFilter nonSelfReceiver = new SelectFilter("selectedTransferDestination", "IS NOT NULL", null)
                .and("selectedTransferDestination.selfReceiver", "=", false);

        // Overseas type
        if (monthlyPaymentRule.isTargetingOverseas()) {
            SelectFilter overseasFilter = new SelectFilter("employeeType", "=", OfficeStaffType.OVERSEAS_STAFF);
            if (monthlyPaymentRule.getCountries().size() > 0) {

                overseasFilter.and(new SelectFilter(selfReceiver.and("country", "IN", monthlyPaymentRule.getCountries()))
                        .or(nonSelfReceiver.and("selectedTransferDestination.country", "IN", monthlyPaymentRule.getCountries())));
            }
            typeFilter= new SelectFilter(overseasFilter);

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));
        }
        // Expats type
        if (monthlyPaymentRule.isTargetingExpats()) {
            SelectFilter expatsFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EXPAT);
            if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
                expatsFilter.and("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
            }
            typeFilter= typeFilter == null ? new SelectFilter(expatsFilter) : typeFilter.or(expatsFilter);

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));
        }
        // Emiratis type
        if (monthlyPaymentRule.isTargetingEmiratis()) {
            SelectFilter emiratisFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EMARATI);

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EMIRATI));

            typeFilter= typeFilter == null ? new SelectFilter(emiratisFilter) : typeFilter.or(emiratisFilter);
        }

        maxStartingDate = DateUtil.getDayEnd(DateUtil.getDayEnd(DateUtil.getDayOfMonthDate(new Date(monthlyPaymentRule.getPayrollMonth().getTime()), dayOfMonth)));
        typeFilter.and("startingDate", "<=", maxStartingDate);

        // Active and not excluded from payroll
        SelectFilter active = new SelectFilter("status", "=", OfficeStaffStatus.ACTIVE);

        // Terminated within payroll
        LocalDate payrollMonth = new LocalDate(monthlyPaymentRule.getPayrollMonth());
        SelectFilter terminatedWithinPayrollDate = new SelectFilter("terminationDate", "IS NOT NULL", null)
                .and("terminationDate", ">=", payrollMonth.withDayOfMonth(1).toDate())
                .and("terminationDate", "<=", payrollMonth.dayOfMonth().withMaximumValue().toDate());



        // Terminated within payroll date and Final settlement is approved before lock date (New case)
        SelectFilter terminatedWithinPayrollFinalSettlementApproved = new SelectFilter("finalSettlement", "IS NOT NULL", null)
                .and(terminatedWithinPayrollDate);

        SelectFilter baseFilter = new SelectFilter(typeFilter).and(new SelectFilter(active).or(terminatedWithinPayrollFinalSettlementApproved));

        return baseFilter;
    }


    public SelectFilter geTerminatedListFilter(MonthlyPaymentRule monthlyPaymentRule, PaymentRuleEmployeeType employeeType) {

        SelectFilter typeFilter = new SelectFilter();
        Integer dayOfMonth = 0;
        Set<Long> excludedStaffDueToStartDate;

        // Overseas type
        if (employeeType == PaymentRuleEmployeeType.OVERSEAS) {
            SelectFilter overseasFilter = new SelectFilter("employeeType", "=", OfficeStaffType.OVERSEAS_STAFF);
            if (monthlyPaymentRule.getCountries().size() > 0) {
                SelectFilter selfReceiver = new SelectFilter(new SelectFilter("selectedTransferDestination", "IS NULL", null)
                        .or("selectedTransferDestination.selfReceiver", "=", true));

                SelectFilter nonSelfReceiver = new SelectFilter("selectedTransferDestination", "IS NOT NULL", null)
                        .and("selectedTransferDestination.selfReceiver", "=", false);

                overseasFilter.and(new SelectFilter(selfReceiver.and("country", "IN", monthlyPaymentRule.getCountries()))
                        .or(nonSelfReceiver.and("selectedTransferDestination.country", "IN", monthlyPaymentRule.getCountries())));
            }

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));

            typeFilter = new SelectFilter(overseasFilter);
        }
        // Expats type
        else if (employeeType == PaymentRuleEmployeeType.EXPATS) {
            SelectFilter expatsFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EXPAT);
//            if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
//                expatsFilter.and("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
//            }

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));

            typeFilter = new SelectFilter(expatsFilter);
        }
        // Emiratis type
        else if (employeeType == PaymentRuleEmployeeType.EMIRATI) {
            SelectFilter emiratisFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EMARATI);

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EMIRATI));

            typeFilter = new SelectFilter(emiratisFilter);
        }

        Date maxStartingDate = DateUtil.getDayEnd(DateUtil.getDayOfMonthDate(new Date(monthlyPaymentRule.getPayrollMonth().getTime()), dayOfMonth));
        excludedStaffDueToStartDate = officeStaffRepository.excludedStaff(OfficeStaffTodoType.CONFIRM_PAYROLL_QUESTIONNAIRE.toString(), DateUtil.getFirstOfMonthDate(monthlyPaymentRule.getPayrollMonth()), maxStartingDate);
        typeFilter.and("id", "not in", excludedStaffDueToStartDate);

        // Not excluded from payroll
        SelectFilter notExcludedFromPayroll = new SelectFilter("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null);

        // Terminated within payroll
        LocalDate payrollMonth = new LocalDate(monthlyPaymentRule.getPayrollMonth());
        SelectFilter terminatedWithinPayrollDate = new SelectFilter("terminationDate", "IS NOT NULL", null)
                .and("terminationDate", ">=", payrollMonth.withDayOfMonth(1).toDate())
                .and("terminationDate", "<=", payrollMonth.dayOfMonth().withMaximumValue().toDate());


        // Terminated within payroll date and not excluded form payroll (Old case)
        SelectFilter terminatedWithinPayrollNotExcluded = new SelectFilter("status", "=", OfficeStaffStatus.TERMINATED)
                .and("finalSettlement", "IS NULL", null)
                .and(terminatedWithinPayrollDate)
                .and(notExcludedFromPayroll);

        // Terminated within payroll date and Final settlement is approved before lock date (New case)
        SelectFilter terminatedWithinPayrollFinalSettlementApproved = new SelectFilter("finalSettlement", "IS NOT NULL", null)
                .and("finalSettlement.approvedByCFO", "=", true)
                .and("finalSettlement.includeInPayroll", "=", true)
                .and(terminatedWithinPayrollDate);

        SelectFilter baseFilter = new SelectFilter(typeFilter).and(new SelectFilter(terminatedWithinPayrollNotExcluded)
                .or(terminatedWithinPayrollFinalSettlementApproved));

        return baseFilter;
    }

    public SelectFilter geTerminatedListFilterForDetailedFile(MonthlyPaymentRule monthlyPaymentRule, PaymentRuleEmployeeType employeeType) {

        SelectFilter typeFilter = new SelectFilter();

        Date maxStartingDateForOfficeStaff;
        Integer dayOfMonth = null;

        // Overseas type
        if (employeeType == PaymentRuleEmployeeType.OVERSEAS) {
            SelectFilter overseasFilter = new SelectFilter("employeeType", "=", OfficeStaffType.OVERSEAS_STAFF);
            if (monthlyPaymentRule.getCountries().size() > 0) {
                SelectFilter selfReceiver = new SelectFilter(new SelectFilter("selectedTransferDestination", "IS NULL", null)
                        .or("selectedTransferDestination.selfReceiver", "=", true));

                SelectFilter nonSelfReceiver = new SelectFilter("selectedTransferDestination", "IS NOT NULL", null)
                        .and("selectedTransferDestination.selfReceiver", "=", false);

                overseasFilter.and(new SelectFilter(selfReceiver.and("country", "IN", monthlyPaymentRule.getCountries()))
                        .or(nonSelfReceiver.and("selectedTransferDestination.country", "IN", monthlyPaymentRule.getCountries())));
            }

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));

            typeFilter = new SelectFilter(overseasFilter);
        }
        // Expats type
        else if (employeeType == PaymentRuleEmployeeType.EXPATS) {
            SelectFilter expatsFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EXPAT);
//            if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
//                expatsFilter.and("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
//            }

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));

            typeFilter = new SelectFilter(expatsFilter);
        }
        // Emiratis type
        else if (employeeType == PaymentRuleEmployeeType.EMIRATI) {
            SelectFilter emiratisFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EMARATI);

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EMIRATI));

            typeFilter = new SelectFilter(emiratisFilter);
        }

        maxStartingDateForOfficeStaff = DateUtil.getDayEnd(DateUtil.getDayOfMonthDate(new Date(monthlyPaymentRule.getPayrollMonth().getTime()), dayOfMonth));
        typeFilter.and("startingDate", "<=", maxStartingDateForOfficeStaff);

        // Terminated within payroll
        LocalDate payrollMonth = new LocalDate(monthlyPaymentRule.getPayrollMonth());
        SelectFilter terminatedWithinPayrollDate = new SelectFilter("terminationDate", "IS NOT NULL", null)
                .and("terminationDate", ">=", payrollMonth.withDayOfMonth(1).toDate())
                .and("terminationDate", "<=", payrollMonth.dayOfMonth().withMaximumValue().toDate());

        SelectFilter baseFilter = new SelectFilter(typeFilter).and(terminatedWithinPayrollDate);

        return baseFilter;
    }

    public SelectFilter geExcludedListFilter(MonthlyPaymentRule monthlyPaymentRule, PaymentRuleEmployeeType employeeType) {

        SelectFilter typeFilter = new SelectFilter();

        Integer dayOfMonth = null;
        Set<Long> excludedStaffDueToStartDate;
        OfficeStaffType officeStaffType = null;

        SelectFilter officeStaffExcludedFilter;

        // Overseas type
        if (employeeType == PaymentRuleEmployeeType.OVERSEAS) {
            SelectFilter overseasFilter = new SelectFilter("employeeType", "=", OfficeStaffType.OVERSEAS_STAFF);
            if (monthlyPaymentRule.getCountries().size() > 0) {
                SelectFilter selfReceiver = new SelectFilter(new SelectFilter("selectedTransferDestination", "IS NULL", null)
                        .or("selectedTransferDestination.selfReceiver", "=", true));

                SelectFilter nonSelfReceiver = new SelectFilter("selectedTransferDestination", "IS NOT NULL", null)
                        .and("selectedTransferDestination.selfReceiver", "=", false);

                overseasFilter.and(new SelectFilter(selfReceiver.and("country", "IN", monthlyPaymentRule.getCountries()))
                        .or(nonSelfReceiver.and("selectedTransferDestination.country", "IN", monthlyPaymentRule.getCountries())));
            }
            typeFilter = new SelectFilter(overseasFilter);


            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));
            officeStaffType = OfficeStaffType.OVERSEAS_STAFF;
        }
        // Expats type
        else if (employeeType == PaymentRuleEmployeeType.EXPATS) {
            SelectFilter expatsFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EXPAT);
//            if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
//                expatsFilter.and("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
//            }
            typeFilter = new SelectFilter(expatsFilter);

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS));
            officeStaffType = OfficeStaffType.DUBAI_STAFF_EXPAT;
        }

        // Emiratis type
        else if (employeeType == PaymentRuleEmployeeType.EMIRATI) {
            SelectFilter emiratisFilter = new SelectFilter("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EMARATI);
            typeFilter = new SelectFilter(emiratisFilter);

            dayOfMonth = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EMIRATI));
            officeStaffType = OfficeStaffType.DUBAI_STAFF_EMARATI;
        }

        Date maxStartingDate = DateUtil.getDayEnd(DateUtil.getDayOfMonthDate(new Date(monthlyPaymentRule.getPayrollMonth().getTime()), dayOfMonth));
        excludedStaffDueToStartDate = officeStaffRepository.excludedStaffButNeedToGenerate(OfficeStaffTodoType.CONFIRM_PAYROLL_QUESTIONNAIRE.toString(), DateUtil.getFirstOfMonthDate(monthlyPaymentRule.getPayrollMonth()), maxStartingDate);
        officeStaffExcludedFilter = new SelectFilter("id", "in", excludedStaffDueToStartDate).and("employeeType", "=", officeStaffType);

        // excluded from payroll
        SelectFilter excludedFromPayroll = new SelectFilter("excludedFromPayroll", "=", true);

        // Active and not excluded from payroll and not terminated
        SelectFilter activeAndExcludedFromPayroll = new SelectFilter("status", "=", OfficeStaffStatus.ACTIVE)
                .and(new SelectFilter(officeStaffExcludedFilter));

        SelectFilter baseFilter = new SelectFilter(typeFilter).and(new SelectFilter(activeAndExcludedFromPayroll));

        return baseFilter;
    }

    public List<OfficeStaff> getTerminatedList(MonthlyPaymentRule monthlyPaymentRule, PaymentRuleEmployeeType employeeType) {
        if (!monthlyPaymentRule.isTargetingOfficeStaff()) return new ArrayList<>();
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.leftJoin("finalSettlement");
        query.leftJoin("selectedTransferDestination");
        query.filterBy(geTerminatedListFilter(monthlyPaymentRule, employeeType));
        return query.execute();
    }

    public List<OfficeStaff> getTerminatedListForDetailedFile(MonthlyPaymentRule monthlyPaymentRule, PaymentRuleEmployeeType employeeType) {
        if (!monthlyPaymentRule.isTargetingOfficeStaff()) return new ArrayList<>();
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.leftJoin("finalSettlement");
        query.leftJoin("selectedTransferDestination");
        query.filterBy(geTerminatedListFilterForDetailedFile(monthlyPaymentRule, employeeType));
        return query.execute();
    }

    public List<OfficeStaff> getExcludedList(MonthlyPaymentRule monthlyPaymentRule, PaymentRuleEmployeeType employeeType) {
        if (!monthlyPaymentRule.isTargetingOfficeStaff()) return new ArrayList<>();
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.leftJoin("selectedTransferDestination");
        query.filterBy(geExcludedListFilter(monthlyPaymentRule, employeeType));
        return query.execute();
    }

    /**
     * Set OfficeStaff information:
     * name, team, city, local nationality, payment method, salary currency,
     * employee unique id, agent id, employee account with agent, pLcode
     * Return OfficeStaffPayrollBean
     *
     * @param bean
     * @param staff
     * @return
     */
    public static OfficeStaffPayrollBean setOfficeStaffInfo(
            OfficeStaffPayrollBean bean,
            OfficeStaff staff,
            MonthlyPaymentRule rule) {

        //Staff name
        bean.setOfficeStaffName(staff.getName());
        bean.setTeam(staff.getTeam() != null ? staff.getTeam().getName() : "");
        bean.setDepartments((staff.getDepartments() != null && !staff.getDepartments().isEmpty()) ? staff.getDepartmentNames() : ""); //todo: check with Team leader if needed

        //staff city
        if (staff.getCity() != null) {
            bean.setCity(staff.getCityName());//.getName());
        }

        //staff mail
        if (staff.getEmail() != null && !staff.getEmail().trim().equals("")) {
            bean.setOfficeStaffEMail(staff.getEmail());
        }

        //Jirra ACC-431
        if (staff.getEmployeeType() == OfficeStaffType.DUBAI_STAFF_EMARATI)
            bean.setLocal("Yes");

        //Payment Method
        if (staff.getSalaryPayementMethod() != null) {
            bean.setPaymentMethod(staff.getSalaryPayementMethod().getName());
        }
        if (staff.getSalaryCurrency() != null) {
            bean.setCurrencyUsed(staff.getSalaryCurrency().toString());
        }

        //visa info for UAE_NON_H_C ,PT_OFFICE_STAFF,STORAGE_STAFF
        NewRequest visaNewRequest = staff.getVisaNewRequest();
        if (visaNewRequest != null) {
            if (visaNewRequest.getEmployeeUniqueId() != null) {
                bean.setEmployeeUniqueId(visaNewRequest.getEmployeeUniqueId());
            }
            if (visaNewRequest.getAgentId() != null) {
                bean.setAgentId(visaNewRequest.getAgentId());
            }
            if (visaNewRequest.getEmployeeAccountWithAgent() != null) {
                bean.setEmployeeAccountWithAgent(visaNewRequest.getEmployeeAccountWithAgent());
            }
        }

        if (staff.getPnl() != null) {
            bean.setpLcode(staff.getPnl());
        }

        if (staff.getStartingDate() != null) {
            LocalDate da = new LocalDate(staff.getStartingDate());
            bean.setStartDate(DateUtil.formatFullDate(da.toDate()));
        }

        //Jirra ACC-1066
        if (staff.getStatus() != null) {
            bean.setStatus(staff.getStatus().toString());
        }

        //Jirra ACC-353
        //PAY-2
        //bean.setMohoreSalary(staff.getPrimarySalary());
        bean.setMohoreSalary(staff.getBasicSalary());

        //PAY-2
        //bean.setMonthlyCashAdvance(staff.getMonthlyLoan());
        bean.setMonthlyCashAdvance(0.0);
        bean.setPaySlipMonth(DateUtil.formatSimpleMonthYear(rule.getPayrollMonth()));
        //...PAY-907...//
        bean.setMoneyReceiverName(staff.getMoneyReceiverName());
        return bean;
    }

}
