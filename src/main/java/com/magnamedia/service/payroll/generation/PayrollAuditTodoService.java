package com.magnamedia.service.payroll.generation;

import com.magnamedia.controller.PayrollAuditTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Shortener;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.mail.TemplateEmail;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.payrollAudit.PayrollAuditMissingField;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.PayrollAuditTodoTaskNameType;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.helper.PublicPageHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.module.type.PaymentRulePaymentMethod;
import com.magnamedia.module.type.PayrollType;
import com.magnamedia.repository.*;
import com.magnamedia.salarycalculation.version2NewSecondPhase.Z_DeductionCapTransaction;
import com.magnamedia.service.Auditor.Housemaids.HousemaidCheckListService;
import com.magnamedia.service.Auditor.Housemaids.HousemaidsExceptions;
import com.magnamedia.service.Auditor.Housemaids.HousemaidsMissingFields;
import com.magnamedia.service.Auditor.OfficeStaffs.OfficeStaffsExceptions;
import com.magnamedia.service.PayrollNotificationsService;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.OfficeStaffPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newversion.AuditFilesService;
import com.magnamedia.service.payroll.generation.newversion.HousemaidPayrollAuditService;
import com.magnamedia.service.payroll.generation.newversion.OfficeStaffPayrollAuditService;
import com.magnamedia.service.payroll.generation.newversion.PayrollGenerationHelperService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Date;
import java.util.*;
import java.util.stream.Collectors;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 6/21/2020
 */
@Service
public class PayrollAuditTodoService {

    @Autowired
    private PayrollAuditTodoService selfReference;

    @Autowired
    private PayrollAuditTodoRepository payrollAuditTodoRepository;

    @Autowired
    private HousemaidsExceptions housemaidsExceptions;

    @Autowired
    private HousemaidPayrollAuditService housemaidPayrollAuditService;

    @Autowired
    private HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2;

    @Autowired
    private OfficeStaffsExceptions officeStaffsExceptions;

    @Autowired
    private OfficeStaffPayrollAuditService officeStaffPayrollAuditService;

    @Autowired
    private OfficeStaffPayrollPaymentServiceV2 officeStaffPayrollPaymentServiceV2;

    @Autowired
    private PayrollNotificationsService notificationsService;

    @Autowired
    private PayrollAuditHousemaidExceptionRepository housemaidExceptionRepository;

    @Autowired
    private PayrollAuditOfficeStaffExceptionRepository officeStaffExceptionRepository;

    @Autowired
    private MonthlyPaymentRuleRepository monthlyPaymentRuleRepository;

    @Autowired
    private PublicPageHelper publicPageHelper;

    @Autowired
    private Shortener shortener;

    @Autowired
    private CcMaidSwitchedToMvRepository ccMaidSwitchedToMvRepository;

    @Transactional
    public void createPayrollAuditTodoBasedOnPaymentRule(MonthlyPaymentRule rule) {

        //create audit to-do fo each employee type in the rule
        for (PaymentRuleEmployeeType employeeType : rule.getEmployeeTypeList()) {
            selfReference.createPayrollAuditTodo(rule, employeeType);
        }
    }

    public void createPayrollAuditTodo(MonthlyPaymentRule rule, PaymentRuleEmployeeType employeeType) {

//        DebugHelper.sendMail(
//                "<EMAIL>", "trying to  generate Audit Todo for " + employeeType + "'");

        //this is in case we run the job again
        PayrollAuditTodo auditTodo = payrollAuditTodoRepository.findTopByMonthlyPaymentRuleAndTaskName(rule, employeeType.toString());
        if(auditTodo == null) {
            //if not null then there is already another to-do for Housemaid/Expat and the payrollType is Primary
            if (employeeType == PaymentRuleEmployeeType.HOUSEMAIDS || employeeType == PaymentRuleEmployeeType.EXPATS) {
                PayrollAuditTodo anotherTodo = payrollAuditTodoRepository.getAnotherCreatedTodo(rule.getPayrollMonth(), employeeType.toString(), rule.getLockDate());
                if (anotherTodo != null) {
//                    DebugHelper.sendMail(
//                            "<EMAIL>", "anotherTodo != null. will not generate Audit Todo for " + employeeType + "'");
                    return;
                }
            }
        }

        List<Housemaid> auditHousemaidList = null;
        List<Housemaid> mustCreateLogHousemaidList = null;
        List<OfficeStaff> auditOfficeStaffList = null;
        List<OfficeStaff> mustCreateOfficeStaffList = null;

        //check if target list is empty then DO NOT GENERATE AUDIT TO-DO
        try {
            if (employeeType == PaymentRuleEmployeeType.HOUSEMAIDS) {
                auditHousemaidList = housemaidPayrollAuditService.getTargetList(rule);
                mustCreateLogHousemaidList = housemaidPayrollAuditService.getTargetListForAllHousemaids(rule);
//                DebugHelper.sendMail(
//                        "<EMAIL>", "Audit Todo for '" + employeeType + "' and the count of employees is: " + auditHousemaidList.size());
                if (auditHousemaidList == null || auditHousemaidList.size() == 0)
                    return;
            } else if (Arrays.asList(PaymentRuleEmployeeType.EXPATS, PaymentRuleEmployeeType.OVERSEAS, PaymentRuleEmployeeType.EMIRATI).
                    contains(employeeType)) {
                auditOfficeStaffList = officeStaffPayrollAuditService.getTargetList(rule, employeeType);
                mustCreateOfficeStaffList = officeStaffPayrollAuditService.getTargetListForAllStaffs(rule);
//                DebugHelper.sendMail(
//                        "<EMAIL>", "Audit Todo for '" + employeeType + "' and the count of employees is: " + auditOfficeStaffList.size());
                if (auditOfficeStaffList == null || auditOfficeStaffList.size() == 0)
                    return;
            }
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail(
                    "<EMAIL>", ex,
                    "Exception occurred while trying to get the target list", false);
        }

        //this is in case we run the job again (auditTodo will not be null
        if(auditTodo == null)
            auditTodo = selfReference.generateAuditTodo(rule, employeeType);

        //generate all exception && generate Payroll logs
        try {
            if (employeeType == PaymentRuleEmployeeType.HOUSEMAIDS) {

                //delete created exceptions if created before and finishedProcessing is still false (this case happens in case a bug)
                if(auditTodo.getFinishedProcessing()  == null || !auditTodo.getFinishedProcessing()) {
                    List<PayrollAuditHousemaidException> exceptions = housemaidExceptionRepository.findByAuditTodo(auditTodo);
                    for (PayrollAuditHousemaidException exception : exceptions) {
                        housemaidExceptionRepository.delete(exception);
                        housemaidExceptionRepository.flush();
                    }
                    housemaidsExceptions.generateHousemaidExceptions(auditTodo, auditHousemaidList);
                }

                //generate Payroll logs asynchronously
                housemaidPayrollPaymentServiceV2.generatePayrollLogsBasedOnAllAsync(mustCreateLogHousemaidList, rule, auditTodo, null, false, true );
            } else if (Arrays.asList(PaymentRuleEmployeeType.EXPATS, PaymentRuleEmployeeType.OVERSEAS, PaymentRuleEmployeeType.EMIRATI).
                    contains(employeeType)) {

                //delete created exceptions if created before and finishedProcessing is still false (this case happens in case a bug)
                if(auditTodo.getFinishedProcessing()  == null || !auditTodo.getFinishedProcessing()) {
                    List<PayrollAuditOfficeStaffException> exceptions = officeStaffExceptionRepository.findByAuditTodo(auditTodo);
                    for (PayrollAuditOfficeStaffException exception : exceptions) {
                        officeStaffExceptionRepository.delete(exception);
                        officeStaffExceptionRepository.flush();
                    }
                    officeStaffsExceptions.generateOfficeStaffExceptions(auditTodo, auditOfficeStaffList);
                }

                //generate Payroll logs asynchronously
                officeStaffPayrollPaymentServiceV2.generatePayrollLogsBasedOnAllAsync(mustCreateOfficeStaffList, rule, auditTodo, null, false);
            }
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail(
                    "<EMAIL>", ex,
                    "Exception occurred while createPayrollAuditTodo for rule #" + rule.getId(), false);
            throw ex;
        }

        Setup.getApplicationContext().getBean(MessagingService.class).notifyAuditors(auditTodo);
    }

    // PAY-1712 Payroll Revamp
    public void createPayrollAuditTodoRevamp(MonthlyPaymentRule rule, PaymentRuleEmployeeType employeeType) {

        if(rule.getExclusionsAuditAfterPayroll() != null && !rule.getExclusionsAuditAfterPayroll()) {

            PayrollAuditTodoTaskNameType auditTodoType = PayrollAuditTodoTaskNameType.getAuditTodoTypeRevamp(employeeType);
            PayrollAuditTodo auditTodo = payrollAuditTodoRepository.findTopByMonthlyPaymentRuleAndTaskName(rule, auditTodoType.toString());

            createPayrollAuditTodo(rule, employeeType); // todo: check later in part#2

        }else {

            List<Housemaid> mustCreateLogHousemaidList = null;
            List<OfficeStaff> mustCreateOfficeStaffList = null;

            //check if target list is empty then DO NOT GENERATE AUDIT TO-DO
            try {
                if (employeeType == PaymentRuleEmployeeType.HOUSEMAIDS) {
                    mustCreateLogHousemaidList = housemaidPayrollAuditService.getTargetListForAllHousemaids(rule);
                    if (mustCreateLogHousemaidList == null || mustCreateLogHousemaidList.size() == 0)
                        return;
                } else if (Arrays.asList(PaymentRuleEmployeeType.EXPATS, PaymentRuleEmployeeType.OVERSEAS, PaymentRuleEmployeeType.EMIRATI).
                        contains(employeeType)) {
                    mustCreateOfficeStaffList = officeStaffPayrollAuditService.getTargetListForAllStaffs(rule);
                    if (mustCreateOfficeStaffList == null || mustCreateOfficeStaffList.size() == 0)
                        return;
                }
            } catch (Exception ex) {
                DebugHelper.sendExceptionMail(
                        "<EMAIL>", ex,
                        "Exception occurred while trying to get the target list", false);
            }

            //generate all exception && generate Payroll logs
            try {
                if (employeeType == PaymentRuleEmployeeType.HOUSEMAIDS) {

                    //generate Payroll logs asynchronously
                    housemaidPayrollPaymentServiceV2.generatePayrollLogsBasedOnAllAsync(mustCreateLogHousemaidList, rule, null, null, false, true);
                } else if (Arrays.asList(PaymentRuleEmployeeType.EXPATS, PaymentRuleEmployeeType.OVERSEAS, PaymentRuleEmployeeType.EMIRATI).
                        contains(employeeType)) {

                    //generate Payroll logs asynchronously
                    officeStaffPayrollPaymentServiceV2.generatePayrollLogsBasedOnAllAsync(mustCreateOfficeStaffList, rule, null, null, false);
                    rule.setFinishedGeneratingStaffsLogs(true);
                    rule = monthlyPaymentRuleRepository.save(rule);
                }
            } catch (Exception ex) {
                DebugHelper.sendExceptionMail(
                        "<EMAIL>", ex,
                        "Exception occurred while createPayrollAuditTodo for rule #" + rule.getId(), false);
                throw ex;
            }

            // todo: to be modified later
            Setup.getApplicationContext().getBean(MessagingService.class).notifyAuditors(null);
        }
    }

    public Boolean createExcludedFromProfileManuallyTodo(Long ruleId) {
        MonthlyPaymentRule rule = Setup.getRepository(MonthlyPaymentRuleRepository.class).findOne(ruleId);
        try {
//            DebugHelper.sendMail(
//                    "<EMAIL>", "trying to generate manually excluded maids from their profile todo for rule #" + rule.getId());

            //this is in case we run the job again
            PayrollAuditTodo auditTodo = payrollAuditTodoRepository.findTopByMonthlyPaymentRuleAndTaskName(rule, PayrollAuditTodoTaskNameType.HOUSEMAID_MANUALLY_EXCLUDED_FROM_PROFILE.toString());
            if (auditTodo == null) {
                List<Housemaid> ccExcludedMaids = housemaidPayrollAuditService.getManuallyExcludedTargetListForHousemaidType(rule, true);
                List<Housemaid> mvExcludedMaids = housemaidPayrollAuditService.getManuallyExcludedTargetListForHousemaidType(rule, false);

                //no need to create todo or do anything
                if (ccExcludedMaids.size() == 0 && mvExcludedMaids.size() == 0)
                    return true;

                //send emails to cc and mv managers
                sendManuallyExcludedMaidsEmails(rule, ccExcludedMaids, mvExcludedMaids);

                //create excluded maids todo
                auditTodo = new PayrollAuditTodo();
                auditTodo.setTaskName(PayrollAuditTodoTaskNameType.HOUSEMAID_MANUALLY_EXCLUDED_FROM_PROFILE.toString());
                auditTodo.setMonthlyPaymentRule(rule);
                auditTodo.setPayrollMonth(rule.getPayrollMonth());
                auditTodo.setLabel("List of Maids Excluded Manually from Payroll");
                auditTodo = payrollAuditTodoRepository.save(auditTodo);
//            notifications/Service.notifyAuditors(auditTodo);
            }
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Exception While createExcludedFromProfileManuallyTodo for Rule #" + ruleId, false);
            return false;
        }
        return true;
    }

    public void sendManuallyExcludedMaidsEmails(MonthlyPaymentRule rule, List<Housemaid> ccExcludedMaids, List<Housemaid> mvExcludedMaids) {
        //send emails to CC managers
        if (ccExcludedMaids.size() > 0) {
            String ccManagerId = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CC_MANAGER_USER_ID);
            if (ccManagerId != null && !"".equals(ccManagerId)) {
                String ccManagerRecipientsCopy = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CC_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_RECIPIENTS);
                List<EmailRecipient> ccRecipients = null;
                if (ccManagerRecipientsCopy != null && !"".equals(ccManagerRecipientsCopy))
                    ccRecipients = Recipient.parseEmailsString(ccManagerRecipientsCopy);

                String subject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CC_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_TITLE);
                subject = subject.replace("@payroll_month@", DateUtil.formatMonth(rule.getPayrollMonth()));
                String url = shortener.shorten(PublicPageHelper.getModuleBaseUrl() + PublicPageHelper.CC_EXCLUDED_MAIDS + "/" + rule.getId());
                User ccManager = Setup.getRepository(UserRepository.class).findOne(Long.parseLong(ccManagerId));
                List<EmailRecipient> recipients = Recipient.parseEmailsString(ccManager.getEmail());
                Map<String, String> params = new HashMap<>();
                params.put("payroll_month", DateUtil.formatMonth(rule.getPayrollMonth()));
                params.put("lock_time_at_last_audit_day", DateUtil.formatDateSlashed(DateUtil.addDays(rule.getPaymentDate(), -1)));
                params.put("url", url);
                Setup.getApplicationContext().getBean(MessagingService.class)
                        .send(recipients, ccRecipients, "Payroll_CC_Maids_Excluded_Manually", subject
                                , params, null, null);
//                TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_CC_Maids_Excluded_Manually", params);
//                Setup.getMailService().sendEmail(recipients, templateEmail, null);
            }
        }

        //send emails to MV managers
        if (mvExcludedMaids.size() > 0) {
            String mvManagerId = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_MV_MANAGER_USER_ID);
            if (mvManagerId != null && !"".equals(mvManagerId)) {
                String mvManagersRecipientsCopy = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_MV_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_RECIPIENTS);
                List<EmailRecipient> ccRecipients = null;
                if (mvManagersRecipientsCopy != null && !"".equals(mvManagersRecipientsCopy))
                    ccRecipients = Recipient.parseEmailsString(mvManagersRecipientsCopy);

                String subject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_MV_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_TITLE);
                subject = subject.replace("@payroll_month@", DateUtil.formatMonth(rule.getPayrollMonth()));
                String url = shortener.shorten(PublicPageHelper.getModuleBaseUrl() + PublicPageHelper.MV_EXCLUDED_MAIDS + "/" + rule.getId());
                User mvManager = Setup.getRepository(UserRepository.class).findOne(Long.parseLong(mvManagerId));
                List<EmailRecipient> recipients = Recipient.parseEmailsString(mvManager.getEmail());
                Map<String, String> params = new HashMap<>();
                params.put("payroll_month", DateUtil.formatMonth(rule.getPayrollMonth()));
                params.put("lock_time_at_last_audit_day", DateUtil.formatDateSlashed(DateUtil.addDays(rule.getPaymentDate(), -1)));
                params.put("url", url);
                Setup.getApplicationContext().getBean(MessagingService.class)
                        .send(recipients, ccRecipients, "Payroll_MV_Maids_Excluded_Manually", subject
                                , params, null, null);
//                TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_MV_Maids_Excluded_Manually", params);
//                Setup.getMailService().sendEmail(recipients, templateEmail, null);
            }
        }

        return;
    }
    public PayrollAuditTodo generateAuditTodo (MonthlyPaymentRule rule, PaymentRuleEmployeeType employeeType) {
        PayrollAuditTodo auditTodo = new PayrollAuditTodo();
        // get the label of the To-do
        String label = "Audit " + employeeType.getLabel() + " for " + DateUtil.formatSimpleMonthYear(rule.getPayrollMonth());
        List<String> otherSpecs = new ArrayList<>();

        //check Housemaids
        if (employeeType.equals(PaymentRuleEmployeeType.HOUSEMAIDS) && rule.getHousemaidTypeList().size() == 1)
            otherSpecs.add(rule.getHousemaidTypeList().get(0).getLabel());

//        //check MOL
//        if ((employeeType.equals(PaymentRuleEmployeeType.EXPATS) || employeeType.equals(PaymentRuleEmployeeType.HOUSEMAIDS))
//                && rule.getMolType() != AbstractPaymentRule.MolType.WITH_AND_WITHOUT)
//            otherSpecs.add(rule.getMolType().getLabel());

        //check PayrollType
        if (rule.getPayrollType() != PayrollType.PRIMARY)
            otherSpecs.add(rule.getPayrollType().getLabel());

        if (otherSpecs.size() > 0) {
            label = label.concat(" (");

            for (int i = 0; i < otherSpecs.size(); i++) {
                label = label.concat(otherSpecs.get(i));
                if (i != otherSpecs.size() - 1)
                    label = label.concat(", ");
            }
            label = label.concat(")");
        }


        //now start creating the to-do
        auditTodo.setTaskName(PayrollAuditTodoTaskNameType.valueOf(employeeType.getValue()).toString());
        auditTodo.setLabel(label);
        auditTodo.setMonthlyPaymentRule(rule);
        auditTodo.setPayrollMonth(rule.getPayrollMonth());

        auditTodo = payrollAuditTodoRepository.save(auditTodo);
        return auditTodo;
    }

    @Transactional
    public void createDefaultAuditSetupRecord() {
        if (Setup.getRepository(AuditSetupRepository.class).count() > 0) return;

        AuditSetup auditSetup = new AuditSetup();
        auditSetup.setAdditionMarginPercentage(20);
        auditSetup.setBonusMarginPercentage(20);
        auditSetup.setRepeatedAdditionAndBonusLimit(3);
        auditSetup.setTerminationCompensationLimit(1);
        auditSetup.setLoanAmountLimit(1);

        Setup.getRepository(AuditSetupRepository.class).save(auditSetup);
    }

    /**
     * create Excluded Maid Visa Audit To-do in case there is excluded MV and we are on payment date or after
     * @param monthlyPaymentRule
     * @return
     */
    @Transactional
    public boolean createExcludedMVAuditTodo (MonthlyPaymentRule monthlyPaymentRule) {

//        DebugHelper.sendMail("<EMAIL>", "start createExcludedMVAuditTodo : for rule#" + monthlyPaymentRule.getId() + ", and the payrollType is: " + monthlyPaymentRule.getPayrollType());

        try {
            List<PayrollAuditTodo> excludedMVAuditTodos = payrollAuditTodoRepository.getByMonthlyRuleAndTaskName(monthlyPaymentRule, "EXCLUDED_MAID_VISA");

            //already created but the to-do is closed
            if(excludedMVAuditTodos != null && excludedMVAuditTodos.size() > 0 && excludedMVAuditTodos.get(0).getCompleted())
                return false;

            //already created before
            if (excludedMVAuditTodos != null && excludedMVAuditTodos.size() > 0)
                return true;

            //if LOCAL_TRANSFER don't create and don't skip anything
            if(PaymentRulePaymentMethod.LOCAL_TRANSFER.equals(monthlyPaymentRule.getPaymentMethod()))
                return false;

            if ( monthlyPaymentRule.isTargetingMaidVisa()
                    && monthlyPaymentRule.getPaymentDate().compareTo(new java.sql.Date(System.currentTimeMillis())) <= 0) {

                //get Excluded MV to create a new Audit To-do for them
                List<ExcludedMVInfo> excludedMVInfoList = HousemaidPayrollPaymentServiceV2.getExcludedMVInfoList(monthlyPaymentRule);

                if(excludedMVInfoList != null && excludedMVInfoList.size() > 0) {
                    Setup.getRepository(ExcludedMVInfoRepository.class).save(excludedMVInfoList);
//                    DebugHelper.sendMail("<EMAIL>", "createExcludedMVAuditTodo : #" + monthlyPaymentRule.getId());
                    PayrollAuditTodo excludedMVAuditTodo = new PayrollAuditTodo();
                    excludedMVAuditTodo.setMonthlyPaymentRule(monthlyPaymentRule);
                    excludedMVAuditTodo.setLabel("Check Excluded Maids With Payments Less Than Salary + Fees");
                    excludedMVAuditTodo.setTaskName(PayrollAuditTodoTaskNameType.valueOf("EXCLUDED_MAID_VISA").toString());
                    excludedMVAuditTodo.setPayrollMonth(monthlyPaymentRule.getPayrollMonth());
                    excludedMVAuditTodo.setExcludedMVInfos(excludedMVInfoList);

                    excludedMVAuditTodo.getRulesForExcludedMV().addAll(Setup.getRepository(MonthlyPaymentRuleRepository.class).findByPayrollMonthAndLockDateAndFinishedFalse(monthlyPaymentRule.getPayrollMonth(),
                            monthlyPaymentRule.getLockDate()));

                    payrollAuditTodoRepository.save(excludedMVAuditTodo);

                    //notify the auditors
                    Setup.getApplicationContext().getBean(MessagingService.class).notifyAuditors(excludedMVAuditTodo);

                    return true;
                }
            }
        }catch (Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e, " createExcludedMVAuditTodo: ", false);
        }
        return false;
    }

    @Transactional
    public boolean createCcSwitchingToMvTodo(Date payrollMonth) {
        try {
            //this is in case we run the job again
            PayrollAuditTodo auditTodo = payrollAuditTodoRepository.findTopByPayrollMonthAndTaskName(payrollMonth, PayrollAuditTodoTaskNameType.CC_SWITCHING_TO_MV.toString());
            if (auditTodo == null) {

                //this is a special case for the deployment month only to consider starting from the first of previous month not from the audit closing date
                String firstDeploymentDateSt = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_CC_SWITCHING_TO_MV_DEPLOYMENT_PAYROLL_MONTH);
                Date firstDeploymentDate = firstDeploymentDateSt != null && !firstDeploymentDateSt.isEmpty() ? DateUtil.parseDateDashedSql(firstDeploymentDateSt) : null;

                HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2 = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class);
                java.util.Date from = firstDeploymentDate != null && firstDeploymentDate.equals(payrollMonth) ? firstDeploymentDate : housemaidPayrollPaymentServiceV2.getAuditTodoClosingDate(DateUtil.addMonthsSql(payrollMonth, -1));
                java.util.Date to = housemaidPayrollPaymentServiceV2.getAuditTodoClosingDate(payrollMonth);
                List<CcMaidSwitchedToMv> ccSwitchedToMvMaids = housemaidPayrollAuditService.getCCSwitchedToMvMaidsTargetList(from, to);

                //no need to create to-do or do anything
                if (ccSwitchedToMvMaids.size() == 0)
                    return true;

                PayrollGenerationHelperService generationHelperService = Setup.getApplicationContext().getBean(PayrollGenerationHelperService.class);

                HousemaidPayrollPaymentServiceV2 payrollPaymentServiceV2 = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class);
                java.sql.Date previousPayrollMonth = new java.sql.Date(DateUtil.addMonths(payrollMonth, -1).getTime());
                LocalDate previousPayrollStart = new LocalDate(payrollPaymentServiceV2.getPayrollStartLockDateOfPayrollMonth(previousPayrollMonth));

                LocalDate startLockDate = new LocalDate(payrollPaymentServiceV2.getPayrollStartLockDateOfPayrollMonth(payrollMonth).getTime());
                LocalDate endLockDate = new LocalDate(payrollPaymentServiceV2.getPayrollEndPaymentDateOfPayrollMonth(payrollMonth).getTime());

                // Iterate over each CcMaidSwitchedToMv record. Perform calculations.
                for (CcMaidSwitchedToMv maidSwitchedToMv : ccSwitchedToMvMaids) {
                    doMaidSwitchedToMvCalculations(maidSwitchedToMv, payrollMonth, generationHelperService, startLockDate, endLockDate, previousPayrollStart);
                }
                AccountantToDoService accountantToDoService = Setup.getApplicationContext().getBean(AccountantToDoService.class);
                MonthlyPaymentRule monthlyPaymentRule = accountantToDoService.createMonthlyRuleForSpecialUse(new LocalDate(payrollMonth));
                monthlyPaymentRule = monthlyPaymentRuleRepository.save(monthlyPaymentRule);

                //create CC switching to MV maids to-do
                auditTodo = new PayrollAuditTodo();
                auditTodo.setMonthlyPaymentRule(monthlyPaymentRule);
                auditTodo.setTaskName(PayrollAuditTodoTaskNameType.CC_SWITCHING_TO_MV.toString());
                auditTodo.setPayrollMonth(payrollMonth);
                auditTodo.setLabel("CC switching to MV for " + DateUtil.formatFullMonthFullYear(payrollMonth));
                auditTodo = payrollAuditTodoRepository.save(auditTodo);
                Setup.getApplicationContext().getBean(MessagingService.class).notifyAuditors(auditTodo);
            }
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Exception While createCcSwitchingToMvTodo for Payroll Month " + payrollMonth, false);
            Logger.getLogger(PayrollAuditTodoService.class.getName()).log(Level.SEVERE, "Exception While createCcSwitchingToMvTodo for Payroll Month " + payrollMonth);
            throw ex;
        }
        return true;
    }

    public void doMaidSwitchedToMvCalculations(CcMaidSwitchedToMv maidSwitchedToMv, Date payrollMonth, PayrollGenerationHelperService generationHelperService, LocalDate startLockDate, LocalDate endLockDate, LocalDate previousPayrollStart) {

        maidSwitchedToMv.setOldContract();
        double amountToBeTransferred = maidSwitchedToMv.getHousemaid().getLastCCSalary() == null ? 0.0 : maidSwitchedToMv.getHousemaid().getLastCCSalary();
        double allDeductionsAmount = generationHelperService
                .getSumOfDeductionsWithAllTypeForCurrentMonthByHousemaid(maidSwitchedToMv.getHousemaid(), payrollMonth);
        double  allAdditionsAmount = generationHelperService.getSumOfAdditionsForCurrentMonthByHousemaid(maidSwitchedToMv.getHousemaid(), startLockDate, endLockDate)
                + generationHelperService.getUnpaidImportantNotesByHousemaid(maidSwitchedToMv.getHousemaid(), startLockDate, endLockDate, false);

        Boolean firstSalary = maidSwitchedToMv.getHousemaid().isFirstSalary(payrollMonth);
        Z_DeductionCapTransaction z_deductionCapTransaction = new Z_DeductionCapTransaction();
        HousemaidPayrollBean bean = new HousemaidPayrollBean();
        bean.setTotalDeduction(allDeductionsAmount);
        bean.setTotatIcome(amountToBeTransferred + allAdditionsAmount);
        double repaymentValue = z_deductionCapTransaction.calculate(maidSwitchedToMv.getHousemaid(), startLockDate, endLockDate, previousPayrollStart,
                payrollMonth, false, true, firstSalary, bean);

        double finalAmountToBeTransferred = amountToBeTransferred + allAdditionsAmount - allDeductionsAmount - repaymentValue;

        maidSwitchedToMv.setAmountToBeTransferred(amountToBeTransferred);
        maidSwitchedToMv.setAdditionsAmount(allAdditionsAmount);
        maidSwitchedToMv.setDeductionsAmount(allDeductionsAmount);
        maidSwitchedToMv.setFinalAmountToBeTransferred(finalAmountToBeTransferred);
        maidSwitchedToMv.setRepaymentAmount(repaymentValue);
        maidSwitchedToMv.setAlreadyCalculated(true);
        maidSwitchedToMv.setRepaymentAmount(repaymentValue);


        maidSwitchedToMv = ccMaidSwitchedToMvRepository.save(maidSwitchedToMv);
    }


    // USED AS BGT
    public void processHousemaidPayrollAuditTodo(Long payrollAuditTodoId) {
        PayrollAuditTodo payrollAuditTodo = payrollAuditTodoRepository.findOne(payrollAuditTodoId);
        if (payrollAuditTodo == null) {
            return;
        }
        MonthlyPaymentRule monthlyPaymentRule = monthlyPaymentRuleRepository.findOne(payrollAuditTodo.getMonthlyPaymentRule().getId());
        //getting the second housemaid rule in this lock date if exists
        MonthlyPaymentRule secondRule = null;

        SelectQuery<MonthlyPaymentRule> selectQuery = new SelectQuery<>(MonthlyPaymentRule.class);
        selectQuery.filterBy("lockDate", "=", payrollAuditTodo.getLockDate());
        selectQuery.filterBy("employeeTypeList", "MEMBER OF", PaymentRuleEmployeeType.HOUSEMAIDS);
        selectQuery.filterBy("singleHousemaid", "=", false);
        selectQuery.filterBy("singleOfficeStaff", "=", false);
        List<MonthlyPaymentRule> rules = selectQuery.execute();
        for (MonthlyPaymentRule rule : rules)
            if (!rule.getId().equals(monthlyPaymentRule.getId()))
                secondRule = rule;

        Logger.getLogger(PayrollAuditTodoService.class.getName()).log(Level.SEVERE, "monthlyPaymentRule #" + monthlyPaymentRule.getId());
        Logger.getLogger(PayrollAuditTodoService.class.getName()).log(Level.SEVERE, "secondRule #" + (secondRule != null ? secondRule.getId() : "null"));

        List<Housemaid> targetList = housemaidPayrollAuditService.getTargetListForAllHousemaids(payrollAuditTodo.getMonthlyPaymentRule());

        List<PayrollAuditMissingField> missingFields = Setup.getApplicationContext().getBean(HousemaidsMissingFields.class).getHousemaidMissingFields(targetList);
        if (missingFields != null && missingFields.size() > 0) {
            Set<Housemaid> excludedMaidsDueToMissingFields = new HashSet<>();
            for (PayrollAuditMissingField missingField : missingFields) {
                excludedMaidsDueToMissingFields.add(Setup.getRepository(HousemaidRepository.class).getOne(missingField.getHousemaidId()));
            }

            //delete generate audit log in case we are in secondary payroll and the maid is excluded due to her missing details
            //in order to prevent duplicated logs
            if (monthlyPaymentRule.isSecondaryMonthlyRule()){
                List<MonthlyPaymentRule> listOfRelatedRules = monthlyPaymentRuleRepository.findByPayrollMonthAndEmployeeTypeAndPayrollTypeAndLockDate(monthlyPaymentRule.getPayrollMonth(), PaymentRuleEmployeeType.HOUSEMAIDS, monthlyPaymentRule.getPayrollType(), monthlyPaymentRule.getLockDate());
                Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).clearDataForMaids(new ArrayList<>(excludedMaidsDueToMissingFields), monthlyPaymentRule, payrollAuditTodo, null, listOfRelatedRules);
            }

            monthlyPaymentRule.setExcludedMaidsDueToMissingFields(excludedMaidsDueToMissingFields);
            monthlyPaymentRule = monthlyPaymentRuleRepository.save(monthlyPaymentRule);
            if (secondRule != null && secondRule.getId() != null) {
                secondRule.setExcludedMaidsDueToMissingFields(excludedMaidsDueToMissingFields);
                secondRule = monthlyPaymentRuleRepository.save(secondRule);
            }
        }

        List<Housemaid> excludedMaidsHasNoMedicalCertificate = Setup.getApplicationContext().getBean(PayrollAuditTodoController.class).
                getExcludedMaidsDueToNotHasMedicalCertificate(targetList);
        if (excludedMaidsHasNoMedicalCertificate != null) {
            Set<Housemaid> excludedMaidsDueDontHaveMedicalCertificate = new HashSet<>(excludedMaidsHasNoMedicalCertificate);

            monthlyPaymentRule.setExcludedDueToMedicalTest(excludedMaidsDueDontHaveMedicalCertificate);
            monthlyPaymentRule = monthlyPaymentRuleRepository.save(monthlyPaymentRule);
            if (secondRule != null && secondRule.getId() != null) {
                secondRule.setExcludedDueToMedicalTest(excludedMaidsDueDontHaveMedicalCertificate);
                secondRule = monthlyPaymentRuleRepository.save(secondRule);
            }
        }

//        List<Housemaid> excludedEVisaIssuedMaids = Setup.getApplicationContext().getBean(PayrollAuditTodoController.class)
//                .getExcludedMaidsDueToEVisaIssued(targetList);
//        if (excludedEVisaIssuedMaids != null) {
//            Set<Housemaid> excludedMaidsDueToEVisaIssued = new HashSet<>(excludedEVisaIssuedMaids);
//
////            monthlyPaymentRule.setExcludedMaidDueToEVisaIssue(excludedMaidsDueToEVisaIssued);
//            monthlyPaymentRule = monthlyPaymentRuleRepository.save(monthlyPaymentRule);
//            if (secondRule != null && secondRule.getId() != null) {
////                secondRule.setExcludedMaidDueToEVisaIssue(excludedMaidsDueToEVisaIssued);
//                secondRule = monthlyPaymentRuleRepository.save(secondRule);
//            }
//        }

        int overLapDays = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_EXCLUDING_OVERLAP_MAIDS_DAYS_COUNT));
        Set<Housemaid> overLapMaids = Setup.getApplicationContext().getBean(HousemaidCheckListService.class).getOverlapMaidsForMoreThan(targetList, overLapDays);
        if (overLapMaids != null && overLapMaids.size() > 0) {

            //delete generate audit log in case we are in secondary payroll and the maid is excluded due to overlap for more than X days
            //in order to prevent duplicated logs
            if (monthlyPaymentRule.isSecondaryMonthlyRule()) {
                List<MonthlyPaymentRule> listOfRelatedRules = monthlyPaymentRuleRepository.findByPayrollMonthAndEmployeeTypeAndPayrollTypeAndLockDate(monthlyPaymentRule.getPayrollMonth(), PaymentRuleEmployeeType.HOUSEMAIDS, monthlyPaymentRule.getPayrollType(), monthlyPaymentRule.getLockDate());
                Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).clearDataForMaids(new ArrayList<>(overLapMaids), monthlyPaymentRule, payrollAuditTodo, null, listOfRelatedRules);
            }

            monthlyPaymentRule.setExcludedMaidsDueToOverLap(overLapMaids);
            monthlyPaymentRule = monthlyPaymentRuleRepository.save(monthlyPaymentRule);
            if (secondRule != null && secondRule.getId() != null) {
                secondRule.setExcludedMaidsDueToOverLap(overLapMaids);
                secondRule = monthlyPaymentRuleRepository.save(secondRule);
            }
        }

        if (!createExcludedMVAuditTodo(monthlyPaymentRule)) {

            // check if all monthlyPaymentRule's related todos are completed, then set the monthlyPaymentRule as finished auditing
            Setup.getApplicationContext().getBean(PayrollAuditTodoController.class).checkMonthlyRuleAuditTodos(monthlyPaymentRule);
        }
    }

    public void sendInitialPayrollFilesRevamp(Long monthlyPaymentRuleId){
        MonthlyPaymentRule rule = monthlyPaymentRuleRepository.findOne(monthlyPaymentRuleId);
        if (rule == null) {
            return;
        }

        List<Attachment> attachments = new ArrayList<>();
        attachments.addAll(Setup.getApplicationContext().getBean(AuditFilesService.class).generatePayrollPrePaymentFilesRevamp(rule));
        attachments.addAll(Setup.getApplicationContext().getBean(AuditFilesService.class).generatePayrollAuditDetailFileRevamp(rule));
        attachments.addAll(Setup.getApplicationContext().getBean(AuditFilesService.class).generatePayrollExceptionsReportFileRevamp(rule));

        Map<String, String> paramValues = new HashMap<>();
        paramValues.put("payroll_month", DateUtil.formatFullMonthFullYear(rule.getPayrollMonth()));

        String templateName = "Payroll_Send_Initial_Payroll_Files_Template";
        String templateSubject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PAYROLL_SEND_INITIAL_PAYROLL_FILES_TEMPLATE_SUBJECT);

        if (rule.getEmployeeTypeList().size() == 1) {
            templateSubject = templateSubject.replace("@employee_type1@ - @employee_type2@",
                    rule.getEmployeeTypeList().get(0).getLabel());
        } else if (rule.getEmployeeTypeList().size() > 1) {
            templateSubject = templateSubject.replace("@employee_type1@ - @employee_type2@",
                    rule.getEmployeeTypeList().get(0).getLabel() +
                            " - " + rule.getEmployeeTypeList().get(1).getLabel());
        }

        templateSubject = templateSubject
                .replace("@payment_method@", rule.getPaymentMethod().getLabel())
                .replace("@payroll_type@", rule.getPayrollType().getLabel())
                .replace("@payroll_month@", DateUtil.formatFullMonthFullYear(rule.getPayrollMonth()));

        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.INITIAL_PAYROLL_FILES_RECIPIENT_PARAMETER));
        Setup.getApplicationContext().getBean(MessagingService.class)
                .send(recipients, null, templateName, templateSubject
                        , paramValues, attachments, null);
    }


}
