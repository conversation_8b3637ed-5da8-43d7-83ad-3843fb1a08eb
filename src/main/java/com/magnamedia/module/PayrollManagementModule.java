package com.magnamedia.module;

import com.magnamedia.core.RunModule;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.MagnamediaModule;
import com.magnamedia.core.entity.JobDefinition;
import com.magnamedia.core.entity.Parameter;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.mail.GmailInbox;
import com.magnamedia.extra.BasicSalaryBreakDown;
import com.magnamedia.extra.SalaryLibrary;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.scheduledjobs.*;
import com.magnamedia.service.EmailTemplateService;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.mailprocessor.ProcessingMaidsRejectedSalaries;
import com.magnamedia.service.mailprocessor.SyrianExchangeRate;
import com.magnamedia.service.payroll.generation.PayrollAuditTodoService;
import com.magnamedia.service.payroll.generation.newversion.MonthlyPaymentRuleService;

import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Jabr <<EMAIL>>
 * Created on Jun 23, 2017
 */
@MagnamediaModule
public class PayrollManagementModule implements RunModule {

    /**
     * PAYROLL MANAGEMENT MODULE PARAMETERS
     */
    public static final String URL_MODULE_STAFF_MGMT = "/staffmgmt";
    //Jirra ACC-431
    public static final String PARAMETER_lOCAL_STAFF_NATIONALITY_TAG = "LOCAL_NATIONALITY_TAG";
    public static final String PARAMETER_PAYROLL_TRUSTEE_EMAIL = "payroll_trustee_email";
    public static final String PARAMETER_ADEEB_EMAIL = "Adeeb's Email";
    public static final String PARAMETER_ERROR_EMAIL = "Errors Email";
    public static final String PARAMETER_PAYROLL_EMAILS = "Payroll Emails";
    public static final String PARAMETER_PAYROLL_GENERATION_EMAILS = "Payroll generation Emails";
    public static final String PARAMETER_PAYROLL_SENDING_TO_FACEBOOK_EMAILS = "Payroll sending to facebook Emails";
    public static final String PARAMETER_PAYROLL_EXCEPTIONS_EMAILS = "Payroll Exceptions Emails";
    public static final String PARAMETER_PAYROLL_JOBS_START = "Payroll Jobs Start";
    public static final String PARAMETER_PAYROLL_JOBS_END = "Payroll Jobs End";
    public static final String PARAMETER_LOCK_PAYROLL_ENABLED = "lock payroll enabled";
    public static final String PARAMETER_LOCK_PAYROLL_START = "lock payroll start day";
    public static final String PARAMETER_LOCK_PAYROLL_END = "lock payroll end day";
    public static final String PARAMETER_EMPLOYEES_WITH_NO_EID_EMAILS = "EMPLOYEES WITH NO EID EMAILS ";
    public static final String PARAMETER_MV_INCLUDED_BY_AUDITOR_EMAILS = "PARAMETER_MV_INCLUDED_BY_AUDITOR_EMAILS";
    public static final String PARAMETER_MV_INCLUDED_BY_AUDITOR_EMAIL_SUBJECT = "PARAMETER_MV_INCLUDED_BY_AUDITOR_EMAIL_SUBJECT";
    public static final String PARAMETER_MV_EXCLUDED_AUDIT_TODO_EMAIL_SUBJECT = "PARAMETER_MV_EXCLUDED_AUDIT_TODO_EMAIL_SUBJECT";
    public static final String PARAMETER_CHECKLIST_EMAILS = "Checklist Emails";
    public static final String DDS_CANCELLATION_SCHEDULED_JOB_START_DAY = "dds_cancellation_scheduled_job_start_day";
    public static final String PARAMETER_CHECKLIST_EXCEPTIONS_EMAILS = "Checklist Exceptions Emails";
    public static final String PARAMETER_PAYSLIPS_ARE_BEING_GENRATED = "Are paySlips being generated";
    public static final String PARAMETER_PAYSLIPS_MESSENGER_JOB_IS_RUNNING = "PAYSLIPS_MESSENGER_JOB_IS_RUNNING";
    public static final String PARAMETER_OFFICESTAFF_WEEKLY_PAYROLL_REPORT_EMAIL = "OfficeStaff Weekly Payroll Report Email";
    //Jira PAY-390
    public static final String PARAMETER_PAYROLL_AUDITORS_RECIPIENTS_OF_PAYROLL_FILES_AFTER_CFO_APPROVAL = "PARAMETER_PAYROLL_AUDITORS_RECIPIENTS_OF_PAYROLL_FILES_AFTER_CFO_APPROVAL";
    public static final String PARAMETER_PAYROLL_AUDITORS_SUBJECT_OF_PAYROLL_FILES_AFTER_CFO_APPROVAL = "PARAMETER_PAYROLL_AUDITORS_SUBJECT_OF_PAYROLL_FILES_AFTER_CFO_APPROVAL";
    //Jirra ACC-1227
    public static final String PARAMETER_WORKING_PUBLIC_HOLIDAYS_LIMIT = "Maximum working public holidays";// referenced also in complaints
    public static final String PARAMETER_NO_KIDS_DEDUCTION_ENABLED = "PARAMETER_NO_KIDS_DEDUCTION_ENABLED";
    public static final String PARAMETER_NO_KIDS_DEDUCTION_RESULT_EMAILS = "NO_KIDS_DEDUCTION_RESULT_EMAILS";
    public static final String PARAM_ATTENDANCE_DEDUCTION_AMOUNT_FILIPINO = "attendance_deduction_amount_filipino";
    public static final String PARAM_ATTENDANCE_DEDUCTION_AMOUNT_OTHER = "attendance_deduction_amount_other";
    public static final String PARAMETER_HOUSEMAID_VACATION_ALLOWANCE_EMAIL = "Housemaid Vacation Allowances Email";
    public static final String PARAMETER_OFFICESTAFF_VACATION_ALLOWANCE_EMAIL = "OfficeStaff Vacation Allowances Email";
    public static final String PARAMETER_BANK_TRANSFER_REPORT_RECEIVER_EMAIL = "BANK_TRANSFER_REPORT_RECEIVER_EMAIL";
    public static final String PARAMETER_AUDITOR_ACTIONS_ON_PAYROLL_MANAGER_NOTE_JOB_CONF = "parameter_auditor_actions_on_payroll_manager_note_job_conf";    // PAY-384
    /**
     * PAY-475 related Sales Param
     */
    public static final String PARAMETER_SALES_APPS_GENERIC_JOB_CONFIG = "apps_generic_job_config";
    /**
     * PAYROLL MANAGEMENT MODULE OFFICE STAFF SALARY COMPONENTS PARAMETERS PAY-2
     */
    public static final String PARAMETER_EXPAT_BASIC_SALARY_PERCENTAGE = "EXPAT_BASIC_SALARY_PERCENTAGE";
    public static final String PARAMETER_EXPAT_HOUSING_PERCENTAGE = "EXPAT_HOUSING_PERCENTAGE";
    public static final String PARAMETER_EXPAT_TRANSPORTATION_PERCENTAGE = "EXPAT_TRANSPORTATION_PERCENTAGE";
    /**
     * PAYROLL MANAGEMENT MODULE JAZZ HR PARAMETERS
     */
    public static final String PARAMETER_JAZZ_HR_API_KEY = "JAZZ_HR_API_KEY";
    public static final String PARAMETER_JAZZ_HR_FETCH_HIRES_API = "JAZZ_HR_FETCH_HIRES_API";
    public static final String PARAMETER_JAZZ_HR_FETCH_APPLICANT_API = "JAZZ_HR_FETCH_APPLICANT_API";
    public static final String PARAMETER_MIN_HIRE_DATE_TO_CREATE_HIRE_EMPLOYEE_TODO = "MIN_HIRE_DATE_TO_CREATE_HIRE_EMPLOYEE_TODO";
    /**
     * PAYROLL MANAGEMENT MODULE EMAIL SUBJECT AND BODY FOR SENDING QUESTIONNAIRES TO EMPLOYEE (CANDIDATE) PARAMETERS PAY-2
     */
    public static final String NEW_STAFF_QUESTIONNAIRE_EMAIL_SUBJECT = "NEW_STAFF_QUESTIONNAIRE_EMAIL_SUBJECT";
    public static final String NEW_STAFF_QUESTIONNAIRE_EMAIL_BODY = "NEW_STAFF_QUESTIONNAIRE_EMAIL_BODY";
    public static final String PARAMETER_NEW_STAFF_QUESTIONNAIRE_CC_EMAIL_RECEIVER = "PARAMETER_NEW_STAFF_QUESTIONNAIRE_CC_EMAIL_RECEIVER";
    //the API key to get IBAN info
    public static final String PARAMETER_IBAN_CHECK_API_KEY = "IBAN_VALIDATION_API_KEY";
    //Change test email here to test sending mails
    public static final String PARAMETER_TEST_EMAIL_RECEIVER = "PAYROLL_TEST_EMAIL_RECEIVER";
    public static final String PARAMETER_ANSARI_CHANGE_BANK_DETAILS_EMAIL = "ANSARI_CHANGE_BANK_DETAILS_EMAIL";
    public static final String PARAMETER_ANSARI_PREMIUM_SERVICES_EMAIL = "ANSARI_PREMIUM_SERVICES_EMAIL";
    public static final String PARAMETER_SYRIA_TRANSFERS_EXCHANGE_RATE_EMAIL_CC_RECEIVERS = "SYRIA_TRANSFERS_EXCHANGE_RATE_EMAIL_CC_RECEIVERS";
    public static final String PARAMETER_LAST_RUN_DATE_OF_SYRIA_TRANSFERS_JOB = "LAST_RUN_DATE_OF_SYRIA_TRANSFERS_JOB";
    public static final String PARAMETER_FINAL_SETTLEMENT_DOCUMENTS_NAMES_FOR_EXPATS = "PARAMETER_FINAL_SETTLEMENT_DOCUMENTS_NAMES_FOR_EXPATS";
    public static final String PARAMETER_FINAL_SETTLEMENT_DOCUMENTS_NAMES_FOR_EMIRATES = "PARAMETER_FINAL_SETTLEMENT_DOCUMENTS_NAMES_FOR_EMIRATES";

    public static final String UPDATE_INFO_QUESTIONNAIRE_EMAIL_SUBJECT="UPDATE_INFO_QUESTIONNAIRE_EMAIL_SUBJECT";
    /**
     * PAYROLL MANAGEMENT MODULE FINAL SETTLEMENT PARAMETERS
     */
    public static final String PARAMETER_CFO_APPROVAL_REMINDER = "CFO_APPROVAL_REMINDER";
    public static final String PARAMETER_CFO_EMAIL = "CFO_EMAIL";

    public static final String CFO_FINAL_SETTLEMENT = "CFO_FINAL_SETTLEMENT";

    public static final String CFO_FINAL_SETTLEMENT_USER_ID = "CFO_FINAL_SETTLEMENT_USER_ID";

    public static final String PARAMETER_CFO_ID = "CFO_ID";
    public static final String PARAMETER_REMIND_HANDLER_TO_REVOKE_ACCESSES = "REMIND_HANDLER_TO_REVOKE_ACCESSES";
    /**
     * jira PAY-379
     */
    public static final String PARAMETER_CEO_ID = "CEO_ID";
    //jira PAY-426
    public static final String PARAMETER_DEDUCTION_CALCULATION_IS_ACTIVE = "PARAMETER_DEDUCTION_CALCULATION_IS_ACTIVE";

    //jira PAY-503
    public static final String PARAMETER_PAYROLL_USD_TO_AED_EXCHANGE_RATE = "PARAMETER_PAYROLL_USD_TO_AED_EXCHANGE_RATE";
    /**
     * PAYROLL MANAGEMENT MODULE BUSINESS RULES PARAMETERS
     */
    // jira acc-1651
    public static final String PARAMETER_LOAN_HOMELESS_MAID = "loan for homeless maid";
    public static String PARAMETER_HOUSEMAID_DEDCUCTION_NOTIFICATION_MSG = "housemaid_dedcuction_notification_msg";
    public static String PARAMETER_HOUSEMAID_DEDCUCTION_NOTIFICATION_SMS = "housemaid_dedcuction_notification_sms";
    // Jirra Acc-1350
    public static final String PARAMETER_Full_TIME_MAIDS_AT_CONVERTED_TO_HOUSE_MAID_CASH_ADVANCE_AMOUNT = "full_time_maid_at_converted_to_house_maid_cash_advance_amount";
    /**
     * PAYROLL MANAGEMENT MODULE START DATE POLICY PARAMETERS
     */
    public static final String PARAMETER_START_DATE_N_WEEKS_POLICY_ENABLED = "START_DATE_N_WEEKS_POLICY_ENABLED";
    public static final String PARAMETER_START_DATE_N_WEEKS_POLICY_DURATION = "START_DATE_N_WEEKS_POLICY_DURATION";

    public static final String PARAMETER_START_DATE_N_DAYS_LIVE_OUT_POLICY_DURATION = "START_DATE_N_DAYS_LIVE_OUT_POLICY_DURATION";

    public static final String PARAMETER_START_DATE_N_WEEKS_POLICY_CUTOFF_DATE = "START_DATE_N_WEEKS_POLICY_CUTOFF_DATE";
    public static final String PARAMETER_START_DATE_N_WEEKS_POLICY_EMAILS = "START_DATE_N_WEEKS_POLICY_EMAILS";

    /**
     * PAYROLL MANAGEMENT MODULE REPAYMENT PARAMETERS
     */
    public static final String PARAMETER_REPAYMENT_SUMMARY_EMAIL = "REPAYMENT_SUMMARY_EMAIL";
    public static final String DEFAULT_MONTHLY_REPAYMENT = "Default Monthly Repayment";
    public static final String DEFAULT_MONTHLY_REPAYMENT_AFRICANS = "Default Monthly Repayment Africans";
    public static final String DEFAULT_MONTHLY_REPAYMENT_ETHIOPIAN = "Default Monthly Repayment ethiopian";
    public static final String DEFAULT_MONTHLY_REPAYMENT_KENYANS = "Default Monthly Repayment Kenyans";
    public static final String DEFAULT_MONTHLY_REPAYMENT_PHILIPPINES = "Default Monthly Repayment Philippines";
    /**
     * PAYROLL MANAGEMENT MODULE SALARY COMPONENTS PARAMETERS
     */
    //Freedom Cutoff
    public static final String SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_ETHIOPIAN_NO_ARABIC = "SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_ETHIOPIAN_NO_ARABIC";
    public static final String SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_ETHIOPIAN_ARABIC_SPEAKER = "SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_ETHIOPIAN_ARABIC_SPEAKER";
    public static final String SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_NON_ETHIOPIAN = "SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_NON_ETHIOPIAN";
    public static final String SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_KENYAN = "SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_KENYAN";
    //Jirra ACC-597
    public static final String SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_CAMEROONIAN = "SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_CAMEROONIAN";

    //Freedom regardless of Cutoff
    public static final String SALARY_COMPONENTS_FREEDOM_KENYAN = "SALARY_COMPONENTS_FREEDOM_KENYAN";
    public static final String SALARY_COMPONENTS_FREEDOM_SRILANKAN_NO_ARABIC = "SALARY_COMPONENTS_FREEDOM_SRILANKAN_NO_ARABIC";
    public static final String SALARY_COMPONENTS_FREEDOM_SRILANKAN_ARABIC_SPEAKER = "SALARY_COMPONENTS_FREEDOM_SRILANKAN_ARABIC_SPEAKER";
    public static final String SALARY_COMPONENTS_FREEDOM_INDIAN = "SALARY_COMPONENTS_FREEDOM_INDIAN";
    public static final String SALARY_COMPONENTS_FREEDOM_ETHIOPIAN_NO_ARABIC = "SALARY_COMPONENTS_FREEDOM_ETHIOPIAN_NO_ARABIC";
    public static final String SALARY_COMPONENTS_FREEDOM_ETHIOPIAN_ARABIC_SPEAKER = "SALARY_COMPONENTS_FREEDOM_ETHIOPIAN_ARABIC_SPEAKER";
    public static final String SALARY_COMPONENTS_FREEDOM_NON_ETHIOPIAN = "SALARY_COMPONENTS_FREEDOM_NON_ETHIOPIAN";
    //Jirra ACC-597
    public static final String SALARY_COMPONENTS_FREEDOM_CAMEROONIAN = "SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_CAMEROONIAN";

    //Agency
    public static final String SALARY_COMPONENTS_AGENCY = "SALARY_COMPONENTS_AGENCY";

    //ACC-275
    //Visa
    public static final String SALARY_COMPONENTS_VISA = "SALARY_COMPONENTS_VISA";

    //Clean Exit
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_ETHIOPIAN_NO_ARABIC = "SALARY_COMPONENTS_CLEAN_EXIT_ETHIOPIAN_NO_ARABIC";
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_ETHIOPIAN_ARABIC_SPEAKER = "SALARY_COMPONENTS_CLEAN_EXIT_ETHIOPIAN_ARABIC_SPEAKER";
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_INDONESIAN = "SALARY_COMPONENTS_CLEAN_EXIT_INDONESIAN";
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_INDIAN = "SALARY_COMPONENTS_CLEAN_EXIT_INDIAN";
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_KENYAN = "SALARY_COMPONENTS_CLEAN_EXIT_KENYAN";
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_SRILANKAN_NO_ARABIC = "SALARY_COMPONENTS_CLEAN_EXIT_SRILANKAN_NO_ARABIC";
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_SRILANKAN_ARABIC_SPEAKER = "SALARY_COMPONENTS_CLEAN_EXIT_SRILANKAN_ARABIC_SPEAKER";
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_FILIPINO = "SALARY_COMPONENTS_CLEAN_EXIT_FILIPINO";
    /**
     * PAYROLL MANAGEMENT MODULE DEDUCTION LIMIT PARAMETERS
     */
    public static final String PARAMETER_DEDUCTION_LIMIT = "Deduction Limit";
    public static final String PARAMETER_FILIPINOES_DEDUCTION_LIMIT = "Filipinoes Deduction Limit";
    public static final String PARAMETER_ETHIOPIAN_DEDUCTION_LIMIT = "Ethiopian Deduction Limit";
    public static final String PARAMETER_MAIDSAT_DEDUCTION_LIMIT = "Maids.at Deduction Limit";
    public static final String PARAMETER_AFRICANS_DEDUCTION_LIMIT = "Africans deduction Limit";
    /**
     * PAYROLL MANAGEMENT MODULE HOUSEMAID RULES PARAMETERS
     */
    public static String PARAMETER_HOUSEMAID_RULES_JOB_EMAILS = "HOUSEMAID_RULES_JOB_EMAILS";
    public static String PARAMETER_HOUSEMAID_RULES_JOB_ENABLED = "HOUSEMAID_RULES_JOB_ENABLED";
    /**
     * PAY-497 Limit SMS reminders sent in payroll
     */
    public static String PARAMETER_PAYROLL_REMINDERS_LIMIT = "PARAMETER_PAYROLL_REMINDERS_LIMIT";
    /**
     * PAY-996 Employee Access Update - Add TalentLMS Access Automatically to user's Access
     */
    public static String PARAMETER_PAYROLL_AUTOMATICALLY_EMPLOYEE_ACCESSES = "PARAMETER_PAYROLL_AUTOMATICALLY_EMPLOYEE_ACCESSES";

    /**
     * YAYA-BOT API PARAMETERS
     */
    public static final String YAYA_CHAT_BOT_API = "YAYA_CHAT_BOT_API";
    public static final String YAYA_CHAT_BOT_SENDER_API = "YAYA_CHAT_BOT_SENDER_API";
    public static final String YAYA_CHAT_BOT_PaySlipsSender_Sender_API = "YAYA_CHAT_BOT_PaySlipsSender_Sender_API";
    public static final String YAYA_CHAT_BOT_PaySlips_Photoes_Server = "YAYA_CHAT_BOT_PaySlips_Fhotoes_Server";
    public static final String YAYA_CHAT_BOT_NOT_CONNECTED_MAIDS_API = "YAYA_CHAT_BOT_NOT_CONNECTED_MAIDS_API";
    public static final String YAYA_CHAT_BOT_SALARY_DEDUCTIONS_API = "YAYA_CHAT_BOT_SALARY_DEDUCTIONS_API";
    public static final String YAYA_USER = "YAYA_USER";
    public static final String YAYA_PASSWORD = "YAYA_PASSWORD";
    public static final String MAIDS_AE_Payslips_Sender_API = "MAIDS_AE_Payslips_Sender_API";
    /**
     * SALES-FORCE API PARAMETERS
     */
    public static final String SALESFORCE_APIS_BASE_URL = "SALESFORCE_APIS_BASE_URL";
    public static final String SALESFORCE_APIS_CLEANERS_SALARIES = "SALESFORCE_APIS_CLEANERS_SALARIES";
    // ACC-486 Qatar cleaners payroll API
    public static final String SALESFORCE_APIS_QATAR_CLEANERS = "SALESFORCE_APIS_QATAR_CLEANERS";
    /**
     * ACCOUNTANT TODOS PARAMETERS
     */
    public static final String PARAMETER_MONEY_EXCHANGE_NAME = "MONEY_EXCHANGE_NAME";

    public static final String PAYROLL_PAYMENT_TODO_NOTIFICATION_EMAIL = "payroll_payment_todo_notification_email";
    /**
     * PAYROLL AUDIT HOUSEMAID Limits parameters
     */
    public static final String PARAMETER_HOUSEMAID_SALARY_RAISE_LIMIT = "PARAMETER_HOUSEMAID_SALARY_RAISE_LIMIT";
    public static final String PARAMETER_HOUSEMAID_LOAN_REPAYMENT_SKIPPED_LIMIT = "PARAMETER_HOUSEMAID_LOAN_REPAYMENT_SKIPPED_LIMIT";
    public static final String PARAMETER_HOUSEMAID_REPETITIVE_ADDITION_LIMIT = "PARAMETER_HOUSEMAID_REPETITIVE_ADDITION_LIMIT";
    public static final String PARAMETER_HOUSEMAID_SICK_LEAVE_DAYS_LIMIT = "PARAMETER_HOUSEMAID_SICK_LEAVE_DAYS_LIMIT";
    public static final String PARAMETER_HOUSEMAID_NOT_WITH_CLIENT_MONTHS_LIMIT = "PARAMETER_HOUSEMAID_NOT_WITH_CLIENT_MONTHS_LIMIT";
    public static final String PARAMETER_HOUSEMAID_FILIPINO_RECEIVED_PAYMENT_LIMIT = "PARAMETER_HOUSEMAID_FILIPINO_RECEIVED_PAYMENT_LIMIT";
    public static final String PARAMETER_HOUSEMAID_ETHIOPIAN_RECEIVED_PAYMENT_LIMIT = "PARAMETER_HOUSEMAID_ETHIOPIAN_RECEIVED_PAYMENT_LIMIT";
    public static final String PARAMETER_HOUSEMAID_OTHER_NATIONALITY_RECEIVED_PAYMENT_LIMIT = "PARAMETER_HOUSEMAID_OTHER_NATIONALITY_RECEIVED_PAYMENT_LIMIT";
    public static final String PARAMETER_HOUSEMAID_FILIPINO_AIRFARE_TICKET_LIMIT = "PARAMETER_HOUSEMAID_FILIPINO_AIRFARE_TICKET_LIMIT";
    public static final String PARAMETER_HOUSEMAID_OTHER_NATIONALITY_AIRFARE_TICKET_LIMIT = "PARAMETER_HOUSEMAID_OTHER_NATIONALITY_AIRFARE_TICKET_LIMIT";

    /**
     * PAYROLL AUDIT Report Email parameters
     */

    public static final String PARAMETER_PAYROLL_AUDIT_REPORT_EMAIL_SUBJECT = "PARAMETER_PAYROLL_AUDIT_REPORT_EMAIL_SUBJECT";
    public static final String PARAMETER_PAYROLL_AUDIT_REPORT_EMAIL_RECIPIENTS = "PARAMETER_PAYROLL_AUDIT_REPORT_EMAIL_RECIPIENTS";

    /**
     * Payroll Exceptions Report parameters
     */
    public static final String PARAMETER_HOUSEMAID_FILIPINO_SALARY_LIMIT = "PARAMETER_HOUSEMAID_FILIPINO_SALARY_LIMIT";
    public static final String PARAMETER_HOUSEMAID_ETHIOPIAN_SALARY_LIMIT = "PARAMETER_HOUSEMAID_ETHIOPIAN_SALARY_LIMIT";
    public static final String PARAMETER_HOUSEMAID_INDIAN_SALARY_LIMIT = "PARAMETER_HOUSEMAID_INDIAN_SALARY_LIMIT";
    public static final String PARAMETER_HOUSEMAID_RENEWED_INDIAN_SALARY_LIMIT = "PARAMETER_HOUSEMAID_RENEWED_INDIAN_SALARY_LIMIT";
    public static final String PARAMETER_HOUSEMAID_INDONESIAN_SALARY_LIMIT = "PARAMETER_HOUSEMAID_INDONESIAN_SALARY_LIMIT";
    public static final String PARAMETER_HOUSEMAID_RENEWED_INDONESIAN_SALARY_LIMIT = "PARAMETER_HOUSEMAID_RENEWED_INDONESIAN_SALARY_LIMIT";
    public static final String PARAMETER_HOUSEMAID_AFRICAN_SALARY_LIMIT = "PARAMETER_HOUSEMAID_AFRICAN_SALARY_LIMIT";
    public static final String PARAMETER_HOUSEMAID_OTHER_NATIONALITY_SALARY_LIMIT = "PARAMETER_HOUSEMAID_OTHER_NATIONALITY_SALARY_LIMIT";

    /**
     * PAYROLL MONEY CENTERS PARAMETERS
     */
    public static final String PARAMETER_PHILIPPINE_MONEY_TRANSFER_CENTER = "PHILIPPINE_MONEY_TRANSFER_CENTER";
    public static final String PARAMETER_LEBANON_MONEY_TRANSFER_CENTER = "LEBANON_MONEY_TRANSFER_CENTER";
    /**
     * ATM CARD VIDEOS PARAMETERS
     */
    public static final String PARAMETER_ACTIVATE_ATM_CARD = "ACTIVATE_ATM_CARD";
    public static final String PARAMETER_CHECK_ATM_CARD_BALANCE = "CHECK_ATM_CARD_BALANCE";
    public static final String PARAMETER_USE_ATM_CARD_FROM_OUTSIDE = "USE_ATM_CARD_FROM_OUTSIDE";
    public static final String PARAMETER_SEND_MONEY_TO_FAMILY_WITH_ATM_CARD = "SEND_MONEY_TO_FAMILY_WITH_ATM_CARD";
    public static final String PARAMETER_SEND_MONEY_TO_FAMILY_WITHOUT_ATM_CARD = "SEND_MONEY_TO_FAMILY_WITHOUT_ATM_CARD";
    public static final String PARAMETER_COLLECT_ATM_CARD = "COLLECT_ATM_CARD";
    public static final String PARAMETER_ANSARI_MAIN_BRANCH_ADDRESS = "ANSARI_MAIN_BRANCH_ADDRESS";
    public static final String PARAMETER_ANSARI_LOCATION = "ANSARI_LOCATION";
    public static final String PARAMETER_MUSHRIF_MALL_AUH_LOCATION = "PARAMETER_MUSHRIF_MALL_AUH_LOCATION";
    public static final String PARAMETER_ANSARI_BRANCH = "ANSARI_BRANCH";
    /**
     * PAYROLL CORE EMAIL SENDER
     */
    public static final String PARAMETER_PAYROLL_CORE_EMAIL_ADDRESS = "PAYROLL_CORE_EMAIL_ADDRESS";
    public static final String PARAMETER_PAYROLL_CORE_EMAIL_PASSWORD = "PAYROLL_CORE_EMAIL_PASSWORD";

    public static final String PARAMETER_PAYROLL_PAYMENT_APPROVE_RECEIVERS = "PAYROLL_PAYMENT_APPROVE_RECEIVERS";
    public static final String PARAMETER_PAYROLL_PAYMENT_APPROVE_USER_ID_RECEIVERS = "PARAMETER_PAYROLL_PAYMENT_APPROVE_USER_ID_RECEIVERS";
    public static final String PARAMETER_PAYROLL_PAYMENT_APPROVE_RECEIVERS_FOR_BANK_TRANSFER = "PARAMETER_PAYROLL_PAYMENT_APPROVE_RECEIVERS_FOR_BANK_TRANSFER";
    public static final String PARAMETER_PAYROLL_PAYMENT_APPROVE_USER_ID_RECEIVERS_FOR_BANK_TRANSFER = "PARAMETER_PAYROLL_PAYMENT_APPROVE_USER_ID_RECEIVERS_FOR_BANK_TRANSFER";
    public static final String PARAMETER_PAYROLL_PAYMENT_APPROVE_SUBJECT = "PARAMETER_PAYROLL_PAYMENT_APPROVE_SUBJECT";
    public static final String PARAMETER_PAYROLL_PAYMENT_APPROVE_SUBJECT_FOR_BANK_TRANSFER = "PARAMETER_PAYROLL_PAYMENT_APPROVE_SUBJECT_FOR_BANK_TRANSFER";

    /**
     * RM-248 EXCLUDE MAID VISA MAIDS WITH LESS THAN salary + 578
     */
    public static final String PARAMETER_ADDITIONAL_TO_MAID_VISA_PAYMENT = "PARAMETER_ADDITIONAL_TO_MAID_VISA_PAYMENT";
    public static final String PARAMETER_MAIDS_INCLUDED_IN_PAYROLL = "MAIDS_INCLUDED_IN_PAYROLL";

    //PAY-489 VAT PERCENTAGE PARAMETER from Sales
    public static final String PARAMETER_SALES_VAT_PERCENT = "VAT_PERCENT";

    public static final String PARAMETER_AUDIT_TODO_REMINDER_CUTOFF = "PARAMETER_AUDIT_TODO_REMINDER_CUTOFF";
    public static final String PARAMETER_AUDIT_TODO_REMINDER_RECIPIENT_EMAILS = "PARAMETER_AUDIT_TODO_REMINDER_RECIPIENT_EMAILS";
    public static final String PARAMETER_AUDIT_TODO_REMINDER_CC_EMAILS = "PARAMETER_AUDIT_TODO_REMINDER_CC_EMAILS";
    public static final String PARAMETER_ACCOUNTANT_TODO_REMINDER_RECIPIENT_EMAILS = "PARAMETER_ACCOUNTANT_TODO_REMINDER_RECIPIENT_EMAILS";
    public static final String PARAMETER_ACCOUNTANT_TODO_REMINDER_CC_EMAILS = "PARAMETER_ACCOUNTANT_TODO_REMINDER_CC_EMAILS";

    public static final String PARAMETER_QUESTION_ABOUT_SALARY_EMAIL_TITLE = "PARAMETER_QUESTION_ABOUT_SALARY_EMAIL_TITLE";
    public static final String PARAMETER_REPLAY_QUESTION_ABOUT_SALARY_EMAIL_TITLE = "PARAMETER_REPLAY_QUESTION_ABOUT_SALARY_EMAIL_TITLE";
    public static final String PARAMETER_CC_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_TITLE = "PARAMETER_CC_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_TITLE";
    public static final String PARAMETER_CC_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_RECIPIENTS = "PARAMETER_CC_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_RECIPIENTS";
    public static final String PARAMETER_MV_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_TITLE = "PARAMETER_MV_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_TITLE";
    public static final String PARAMETER_MV_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_RECIPIENTS = "PARAMETER_MV_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_RECIPIENTS";

    //PAY-535
    public static final String PARAMETER_AUTHORIZE_PAYROLL_TRANSFER_RECEIVERS_EMAIL = "PARAMETER_AUTHORIZE_PAYROLL_TRANSFER_RECEIVERS_EMAIL";
    public static final String PARAMETER_AUTHORIZE_PAYROLL_TRANSFER_CC_EMAIL = "PARAMETER_AUTHORIZE_PAYROLL_TRANSFER_CC_EMAIL";
    public static final String PARAMETER_PAYROLL_GENERATION_START_RECEIVERS_EMAIL = "PARAMETER_PAYROLL_GENERATION_START_RECEIVERS_EMAIL";
    public static final String PARAMETER_PAYROLL_GENERATION_START_CC_EMAIL = "PARAMETER_PAYROLL_GENERATION_START_CC_EMAIL";
    public static final String PARAMETER_PAYROLL_GENERATION_FINISHED_RECEIVERS_EMAIL = "PARAMETER_PAYROLL_GENERATION_FINISHED_RECEIVERS_EMAIL";
    public static final String PARAMETER_PAYROLL_GENERATION_FINISHED_CC_EMAIL = "PARAMETER_PAYROLL_GENERATION_FINISHED_CC_EMAIL";

    //PAY-619
    public static final String PARAMETER_RENEWED_PHILIPPINES_HOUSEMAID_IN_ACCOMMODATION_SALARY = "renewal_filipino_basic_for_accommodation";

    //CC-App
    public static final String Check_Atm_Website = "Check_Atm_Website";

    //PAY-526 CC MANAGER && MV MANAGER
    public static final String PARAMETER_CC_MANAGER_USER_ID = "PARAMETER_CC_MANAGER_USER_ID";
    public static final String PARAMETER_MV_MANAGER_USER_ID = "PARAMETER_MV_MANAGER_USER_ID";
    public static final String PARAMETER_EXCLUDE_MAID_EMAIL_TITLE = "PARAMETER_EXCLUDE_MAID_EMAIL_TITLE";
    //PAY-636
    public static final String PARAMETER_PAY_FIX_CONTRACT_RECIPIENT = "PARAMETER_PAY_FIX_CONTRACT_RECIPIENT";
    public static final String PARAMETER_DUPLICATE_IBAN_DETECTION_RECIPIENT = "PARAMETER_DUPLICATE_IBAN_DETECTION_RECIPIENT";

    public static final String PARAMETER_PAYROLL_AFTER_NEW_SALARY_EXCEPTION_RECIPIENT = "PARAMETER_PAYROLL_AFTER_NEW_SALARY_EXCEPTION_RECIPIENT";

    //PAY-1733: For Payroll_Send_Initial_Payroll_Files_Template
    public static final String INITIAL_PAYROLL_FILES_RECIPIENT_PARAMETER = "INITIAL_PAYROLL_FILES_RECIPIENT_PARAMETER";
    public static final String PAYROLL_SEND_INITIAL_PAYROLL_FILES_TEMPLATE_SUBJECT = "PAYROLL_SEND_INITIAL_PAYROLL_FILES_TEMPLATE_SUBJECT";
    //PAY-1733: For Pending_Payroll_Roster_Approval_Alert_Template
    public static final String PENDING_PAYROLL_ROSTER_APPROVAL_REMINDER_TEMPLATE_SUBJECT = "PENDING_PAYROLL_ROSTER_APPROVAL_REMINDER_TEMPLATE_SUBJECT";
    public static final String PENDING_PAYROLL_ROSTER_APPROVAL_REMINDER_TEMPLATE_RECIPIENT = "PENDING_PAYROLL_ROSTER_APPROVAL_REMINDER_TEMPLATE_RECIPIENT";
    //PAY-1733: For Payroll_Mol_List_Upload_Failure_Alert_Template
    public static final String PAYROLL_MOL_LIST_UPLOAD_FAILURE_ALERT_TEMPLATE_SUBJECT = "PAYROLL_MOL_LIST_UPLOAD_FAILURE_ALERT_TEMPLATE_SUBJECT";
    public static final String PAYROLL_MOL_LIST_UPLOAD_FAILURE_ALERT_TEMPLATE_RECIPIENT = "PAYROLL_MOL_LIST_UPLOAD_FAILURE_ALERT_TEMPLATE_RECIPIENT";
    //PAY-1733: For Payroll_Hazardous_Conditions_Alert_Template
    public static final String PAYROLL_HAZARDOUS_CONDITIONS_ALERT_TEMPLATE_SUBJECT = "PAYROLL_HAZARDOUS_CONDITIONS_ALERT_TEMPLATE_SUBJECT";
    public static final String PAYROLL_HAZARDOUS_CONDITIONS_ALERT_TEMPLATE_RECIPIENT = "PAYROLL_HAZARDOUS_CONDITIONS_ALERT_TEMPLATE_RECIPIENT";
    //PAY-1733: For CheckEmployeeListBeforePaymentDateJob Logic
    public static final String HOURS_BEFORE_PAYMENT_DATE_TO_CHECK_THE_EMPLOYEE_LIST_PARAM = "HOURS_BEFORE_PAYMENT_DATE_TO_CHECK_THE_EMPLOYEE_LIST_PARAM";
    public static final String MAX_EMPLOYEE_LIST_AGE_IN_HOUR_PARAM = "MAX_EMPLOYEE_LIST_AGE_IN_HOUR_PARAM";

    //PAY-1733: For Checking Accounting conditions step
    public static final String PAYMENT_PROCESS_PERCENTAGE_PARAM = "PAYMENT_PROCESS_PERCENTAGE_PARAM";

    //PAY-1733: For Checking CC_MAID_SALARY_COMPARISON hazardous condition
    public static final String CC_TOTAL_NET_SALARY_DIFFERENCE_FROM_PREVIOUS_MONTH_PARAM = "CC_TOTAL_NET_SALARY_DIFFERENCE_FROM_PREVIOUS_MONTH_PARAM";

    //PAY-1733: For Checking LOAN_REPAYMENT_BALANCE_COMPARISON hazardous condition
    public static final String TOTAL_LOAN_REPAYMENT_PERCENTAGE_FROM_TOTAL_LOAN_BALANCE_PARAM = "TOTAL_LOAN_REPAYMENT_PERCENTAGE_FROM_TOTAL_LOAN_BALANCE_PARAM";

    //PAY-1733: For Checking MV_MAID_SALARY_CLIENT_PAYMENT hazardous condition
    public static final String MV_MAIDS_SALARY_PERCENTAGE_FROM_TOTAL_MV_PAYMENT_PARAM = "MV_MAIDS_SALARY_PERCENTAGE_FROM_TOTAL_MV_PAYMENT_PARAM";


    //PAY-1733: For Checking GRP_EARNINGS_COMPARISON hazardous condition
    public static final String GROUP_2_OR_6_EARNING_PERCENTAGE_FROM_GROUP_1_OR_5_EARNING_PARAM = "GROUP_2_OR_6_EARNING_PERCENTAGE_FROM_GROUP_1_OR_5_EARNING_PARAM";

    //PAY-1733: For Checking CC_MAID_CLIENT_COUNT hazardous condition
    public static final String RATIO_CC_MAIDS_WITHOUT_CLIENT_PARAM = "RATIO_CC_MAIDS_WITHOUT_CLIENT_PARAM";

    //PAY-1734
    public static final String TIME_FRAME_MONTHS_PARAM = "TIME_FRAME_MONTHS_PARAM";
    public static final String CC_NEW_MULTIPLIER_PARAM = "CC_NEW_MULTIPLIER_PARAM";
    public static final String CC_RENEWAL_MULTIPLIER_PARAM = "CC_RENEWAL_MULTIPLIER_PARAM";
    public static final String MV_MULTIPLIER_PARAM = "MV_MULTIPLIER_PARAM";
    public static final String TIME_INTERVAL_PARAM = "TIME_INTERVAL_PARAM";
    public static final String TOTAL_RAFFLE_PRIZE_LIMIT_PARAM = "TOTAL_RAFFLE_PRIZE_LIMIT_PARAM";
    public static final String OVERSEAS_EMPLOYEE_MAX_SAME_RECIPIENT_PARAM = "OVERSEAS_EMPLOYEE_MAX_SAME_RECIPIENT_PARAM";
    public static final String STATUSES_ELIGIBLE_FOR_PAYROLL_PARAM = "STATUSES_ELIGIBLE_FOR_PAYROLL_PARAM";


    public static final String PAYROLL_EMPLOYEE_LIST_RBA_CHECK_TEMPLATE_SUBJECT = "PAYROLL_EMPLOYEE_LIST_RBA_CHECK_TEMPLATE_SUBJECT";
    public static final String PAYROLL_EMPLOYEE_LIST_RBA_CHECK_TEMPLATE_RECIPIENT = "PAYROLL_EMPLOYEE_LIST_RBA_CHECK_TEMPLATE_RECIPIENT";


    //PAY-818 set the validation date
    public static final String PARAMETER_HOUSEMAID_PRIMARY_PAYMENT_DATE_VALIDATION_DAY = "PARAMETER_HOUSEMAID_PRIMARY_PAYMENT_DATE_VALIDATION_DAY";

    //PAY-663
    public static final String PARAMETER_EMPLOYEES_REQUESTED_FOR_TERMINATION_EMAIL_SUBJECT = "PARAMETER_EMPLOYEES_REQUESTED_FOR_TERMINATION_EMAIL_SUBJECT";
    public static final String PARAMETER_EMPLOYEES_REQUESTED_FOR_TERMINATION_EMAIL_RECEIVERS = "PARAMETER_EMPLOYEES_REQUESTED_FOR_TERMINATION_EMAIL_RECEIVERS";

    //PAY-671 unpaid days (group 3)
    public static final String PARAMETER_HOUSEMAID_FORGIVENESS_DAYS_TO_LOOK_BACK = "PARAMETER_HOUSEMAID_FORGIVENESS_DAYS_TO_LOOK_BACK";

    public static final String PARAMETER_MAX_ADDITION_VALUE_TO_MAID_AFTER_SWITCH_TO_MV = "PARAMETER_MAX_ADDITION_VALUE_TO_MAID_AFTER_SWITCH_TO_MV";
    /**
     * PAYROLL MANAGEMENT MODULE PICK-LISTS
     */
    public static final String PICKLIST_WARNING_LETTER_TYPE = "WarningLetterType";
    public static final String PICKLIST_MANAGER_NOTE_DEDUCTION_REASONS_CODE = "DeductionReasons";
    public static final String PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE = "AnnualVacationType";
    public static final String PICKLIST_PROSPECTTYPE = "ProspectType";
    public static final String PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE = "AdditionReasons";
    public static final String PICKLIST_OFFICESTAFF_SALARY_PAYMENT_METHOD_CODE = "SalaryPaymentMethod";
    public static final String PICKLIST_OFFICESTAFF_SALARY_TRANSFER_DESTINATION_CODE = "SalaryTransferDestination";
    public static final String PICKLIST_HOUSEMIAD_PAYROLL_TYPE = "PAYROLL_TYPE";
    public static final String PICKLIST_JOB_TITLE="JOB_TITLE";
    public static final String PICKLIST_UNPAID_DAYS_FORGIVENESS_TYPES = "UNPAID_DAYS_FORGIVENESS_TYPES";
    public static final String PICKLIST_ITEM_MAID_WAS_WITH_CLIENT = "Maid was with client (Full)";
    public static final String PICKLIST_ITEM_MAID_WAS_IN_ACCOMMODATION = "Maid was in accommodation (Basic)";
    public static final String PICKLIST_HOUSEMAID_PURPOSE_FOR_FORGIVENESS_DEDUCTION = "HousemaidPurposesForForgivenessDeduction";
    public static final String PICKLIST_ITEM_MAID_WAS_WITH_CLIENT_PURPOSE = "Maid was with client purpose";
    public static final String PICKLIST_ITEM_MAID_WAS_IN_ACCOMMODATION_PURPOSE = "Maid was in accommodation purpose";
    /**
     * PAYROLL MANAGEMENT MODULE PICK-LIST-ITEMS
     */
    public static final String PICKLIST_ITEM_COVER_DEDUCTION_LIMIT_ADDITION_CODE = "cover_deduction_limit";
    public static final String PICKLIST_ITEM_COVER_NEGATIVE_SALARY_ADDITION_CODE = "cover_negative_salary";
    public static final String MAID_VISA_PEOSPECT_TYPE = "maidvisa.ae prospect";
    public static final String PICKLIST_ITEM_MANAGER_NOTE_REFUND_DEDUCTION_ADDITION_CODE = "refund";
    public static final String PICKLIST_ITEM_MANAGER_NOTE_AIRFARE_TICKET_ADDITION_CODE = "airfare_ticket";
    public static final String PICKLIST_ITEM_MANAGER_NOTE_LOW_EXCHANGE_RATE_COMPENSATION_ADDITION_CODE = "low_exchange_rate_compensation";
    //Jirra ACC-738
    public static final String PICKLIST_ITEM_NATIONALITY_FILIPINO_1 = "philippines";
    public static final String PICKLIST_ITEM_NATIONALITY_FILIPINO_2 = "filipino";

    // misc
    public static final String PARAMETER_FAILED_CREATE_MAID_FINAL_SETTLEMENT_EMAIL_RECIPIENTS = "failed_create_maid_final_settlement_email_recipients";
    //Jirra PAY-611 Repeat Eid Deduction Amount
    public static final String PARAMETER_PAY_REPEAT_EID_DEDUCTION_AMOUNT = "PARAMETER_PAY_REPEAT_EID_DEDUCTION_AMOUNT";

    //PAY-597 Payday celebration Email
    public static final String PARAMETER_PAYDAY_CELEBRATION_EMAIL_SUBJECT = "PARAMETER_PAYDAY_CELEBRATION_EMAIL_SUBJECT";

    //PAY-835 Payday celebration Email
    public static final String PARAMETER_SYRIAN_LOW_EXCHANGE_RATE_PERCENTAGE = "PARAMETER_SYRIAN_LOW_EXCHANGE_RATE_PERCENTAGE";
    //PAY-830
    public static final String PARAMETER_ROSTER_REQUESTS_ARE_APPROVED_EMAIL_SUBJECT = "PARAMETER_ROSTER_REQUESTS_ARE_APPROVED_EMAIL_SUBJECT";
    public static final String PARAMETER_ROSTER_REQUESTS_ARE_APPROVED_EMAIL_RECEIVERS = "PARAMETER_ROSTER_REQUESTS_ARE_APPROVED_EMAIL_RECEIVERS";

    //PAY-1299
    public static final String EMIRATES_ROSTER_APPROVAL_EMAIL_SUBJECT = "EMIRATES_ROSTER_APPROVAL_EMAIL_SUBJECT";
    public static final String EMIRATES_ROSTER_APPROVAL_EMAIL_RECEIVERS = "EMIRATES_ROSTER_APPROVAL_EMAIL_RECEIVERS";

    //PAY-1024 excluding overlap maids
    public static final String PARAMETER_EXCLUDING_OVERLAP_MAIDS_DAYS_COUNT = "PARAMETER_EXCLUDING_OVERLAP_MAIDS_DAYS_COUNT";

    //PAY-1037
    public static final String PARAMETER_STARTING_YEAR_FOR_MAID_IN_PAYROLL_EXCEPTION_REPORT = "PARAMETER_THE_YEAR_WHEN_START_VALIDATING_PAYROLL";
    public static final String PARAMETER_ADDITION_LIMIT_FOR_MAID_IN_PAYROLL_EXCEPTION_REPORT = "PARAMETER_ADDITION_LIMIT_FOR_MAID_IN_PAYROLL_EXCEPTION_REPORT";
    //PAY-1095 PAY-1014 Pay - switching CC to MV to-do
    public static final String PARAMETER_PAYROLL_CC_SWITCHING_TO_MV_DEPLOYMENT_PAYROLL_MONTH = "PARAMETER_PAYROLL_CC_SWITCHING_TO_MV_DEPLOYMENT_PAYROLL_MONTH";

    //PAY-884
    public static final String PARAMETER_BIRTHDAY_REMINDER_JAD_EMAIL = "PARAMETER_BIRTHDAY_REMINDER_JAD_EMAIL";

    // PAY-1167
    public static final String PARAMETER_RECRUITMENT_MANAGER_EMAIL = "PARAMETER_RECRUITMENT_MANAGER_EMAIL";

    public static  final String PARAMETER_PAY_LOAN_REPAYMENT_APPROVAL_EMAIL_RECIPIENTS = "PARAMETER_PAY_LOAN_REPAYMENT_APPROVAL_EMAIL_RECIPIENTS";
    public static final String PARAMETER_PAY_LOAN_REPAYMENT_APPROVAL_USER_ID_RECIPIENTS = "PARAMETER_PAY_LOAN_REPAYMENT_APPROVAL_USER_ID_RECIPIENTS";
    public static final String PARAMETER_PAYROLL_ROSTER_FILE_COLUMN_WIDTHS = "PARAMETER_PAYROLL_ROSTER_FILE_COLUMN_WIDTHS";

    public static final String PARAMETER_PAYROLL_NUMBER_OF_DAYS_TO_CREATE_ATTENDANCE_LOG =  "PARAMETER_PAYROLL_NUMBER_OF_DAYS_TO_CREATE_ATTENDANCE_LOG";

    /**
     * Modules Code
     */
    public static final String ACCOUNTING = "accounting";

    // Pay-855 Final Settlement
    public static final String PAYROLL_OFFICESTAFF_FINAL_SETTLEMENT = "PAYROLL_OFFICESTAFF_FINAL_SETTLEMENT";
    public static final String PAYROLL_OFFICESTAFF_RESIGNATION_LETTER = "PAYROLL_OFFICESTAFF_RESIGNATION_LETTER";

    //PAY-1232
    public static final String PARAMETER_PAYROLL_REMINDER_EMAIL_RECIPIENTS = "PARAMETER_PAYROLL_REMINDER_EMAIL_RECIPIENTS";
    public static final String PARAMETER_PAYROLL_PRIMARY_REMINDER_CC_RECIPIENTS = "PARAMETER_PAYROLL_PRIMARY_REMINDER_CC_RECIPIENTS";
    public static final String PARAMETER_PAYROLL_SECONDARY_REMINDER_CC_RECIPIENTS = "PARAMETER_PAYROLL_SECONDARY_REMINDER_CC_RECIPIENTS";
    public static final String PARAMETER_PRIMARY_PAYROLL_REMINDER_SUBJECT = "PARAMETER_PRIMARY_PAYROLL_REMINDER_SUBJECT";
    public static final String PARAMETER_SECONDARY_PAYROLL_REMINDER_SUBJECT = "PARAMETER_SECONDARY_PAYROLL_REMINDER_SUBJECT";
    public static final String PARAMETER_MAID_WHATSAPP_HELPLINE = "PARAMETER_MAID_WHATSAPP_HELPLINE";
    public static final String PARAMETER_CHUNK_SIZE_FOR_BGT = "PARAMETER_CHUNK_SIZE_FOR_BGT";
    public static final String PARAMETER_MARK_PAYROLL_UNPAID_EMAIL_RECIPIENTS = "PARAMETER_MARK_PAYROLL_UNPAID_EMAIL_RECIPIENTS";
    public static final String PARAMETER_MARK_PAYROLL_UNPAID_USER_ID_RECIPIENTS = "PARAMETER_MARK_PAYROLL_UNPAID_USER_ID_RECIPIENTS";
    public static final String PARAMETER_MARK_PAYROLL_UNPAID_EMAIL_SUBJECT = "PARAMETER_MARK_PAYROLL_UNPAID_EMAIL_SUBJECT";


    // PAY-1429
    public static final String PARAMETER_PAYROLL_ANSARI_MAIDS_REJECTED_SALARIES_EMAIL = "PARAMETER_PAYROLL_ANSARI_MAIDS_REJECTED_SALARIES_EMAIL";
    public static final String PARAMETER_PAYROLL_MAIDS_REJECTED_SALARIES_EMAIL_RECIPIENTS= "PARAMETER_PAYROLL_MAIDS_REJECTED_SALARIES_EMAIL_RECIPIENTS";

    public static final String PARAMETER_PAYROLL_MAIDS_REJECTED_SALARIES_CC_EMAIL_RECIPIENTS = "PARAMETER_PAYROLL_MAIDS_REJECTED_SALARIES_CC_EMAIL_RECIPIENTS";

    public static final String PARAMETER_PAYROLL_SALARY_RULES_REPORT_RECIPIENTS = "PARAMETER_PAYROLL_SALARY_RULES_REPORT_RECIPIENTS";
    public static final String PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EMIRATI = "PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EMIRATI";
    public static final String PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS = "PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS";
    public static final String PARAMETER_PAYROLL_EMARATI_PENSION_RECIPIENTS = "PARAMETER_PAYROLL_EMARATI_PENSION_RECIPIENTS";
    public static final String PARAMETER_PAYROLL_INVALID_PENSION_DEDUCTION_RECIPIENTS = "PARAMETER_PAYROLL_INVALID_PENSION_DEDUCTION_RECIPIENTS";
    public static final String PARAMETER_PAYROLL_DIFFERENCE_BETWEEN_PENSION_AMOUNT_AND_CONTRIBUTION_RECIPIENTS = "PARAMETER_PAYROLL_DIFFERENCE_BETWEEN_PENSION_AMOUNT_AND_CONTRIBUTION_RECIPIENTS";
    public static final String PARAMETER_PAYROLL_PENSION_DEDUCTION_PERCENTAGE_ON_OR_BEFORE_31_10_2023 = "PARAMETER_PAYROLL_PENSION_DEDUCTION_PERCENTAGE_ON_OR_BEFORE_31_10_2023";
    public static final String PARAMETER_PAYROLL_PENSION_DEDUCTION_PERCENTAGE_AFTER_31_10_2023 = "PARAMETER_PAYROLL_PENSION_DEDUCTION_PERCENTAGE_AFTER_31_10_2023";
    public static final String PARAMETER_PAYROLL_PENSION_AUTHORITY_PERCENTAGE_ON_OR_BEFORE_31_10_2023 = "PARAMETER_PAYROLL_PENSION_AUTHORITY_PERCENTAGE_ON_OR_BEFORE_31_10_2023";
    public static final String PARAMETER_PAYROLL_PENSION_AUTHORITY_PERCENTAGE_AFTER_31_10_2023 = "PARAMETER_PAYROLL_PENSION_AUTHORITY_PERCENTAGE_AFTER_31_10_2023";

    public static final String PARAMETER_NEGATIVE_SALARY_COMPONENT_RECIPIENTS = "PARAMETER_NEGATIVE_SALARY_COMPONENT_RECIPIENTS";

    public static final String PARAMETER_PAYROLL_ASSIGNED_OFFICE_WORK_ADDITIONS_EMAIL_RECIPIENTS = "PARAMETER_PAYROLL_ASSIGNED_OFFICE_WORK_ADDITIONS_EMAIL_RECIPIENTS";

    public static final String PARAMETER_PAYROLL_GENERATION_CHUNK_SIZE = "PARAMETER_PAYROLL_GENERATION_CHUNK_SIZE";
    /**
     * Payroll Zoho Recruit APIs
     * */

    public static final String PARAMETER_ZOHO_HR_BASE_API = "ZOHO_HR_BASE_API";


    //PAY-1859
    public static final String PARAMETER_PAYROLL_ANSARI_CHARGES_RATE = "PARAMETER_PAYROLL_ANSARI_CHARGES_RATE";

    public static final String PARAMETER_PAYROLL_EXCEPTION_NOTIFICATION_EMAILS = "PARAMETER_PAYROLL_EXCEPTION_NOTIFICATION_EMAILS";
    public static final String PARAMETER_PAYROLL_SEND_PAYSLIP_CHUNK_SIZE = "PARAMETER_PAYROLL_SEND_PAYSLIP_CHUNK_SIZE";
    public static final String PARAMETER_PAYROLL_PROCESS_OFFICE_STAFF_PAYROLL_CHUNK_SIZE = "PARAMETER_PAYROLL_PROCESS_OFFICE_STAFF_PAYROLL_CHUNK_SIZE";
    public static final String PARAMETER_PAYROLL_PROCESS_HOUSEMAID_PAYROLL_CHUNK_SIZE = "PARAMETER_PAYROLL_PROCESS_HOUSEMAID_PAYROLL_CHUNK_SIZE";

    public static final String PARAMETER_PAYROLL_MULTIPLE_PROFILES_EMAIL_RECIPIENTS = "PARAMETER_PAYROLL_MULTIPLE_PROFILES_EMAIL_RECIPIENTS";

    public static final String PARAMETER_PAYROLL_NO_SHOW_THRESHOLD = "PARAMETER_PAYROLL_NO_SHOW_THRESHOLD";

    public static final String PARAMETER_ACCESS_LINK_USER_ID_RECIPIENTS = "PARAMETER_ACCESS_LINK_USER_ID_RECIPIENTS";

    public static final String PARAMETER_PAYROLL_STAFF_UPDATE_OF_BANK_INFO_HISTORY_POSITION_NAME = "PARAMETER_PAYROLL_STAFF_UPDATE_OF_BANK_INFO_HISTORY_POSITION_NAME";

    public static final String PICKLIST_OFFICE_STAFF_DEPARTMENT = "office_staff_Department";

    //TODO migrate parameters form accounting module 76
    @Override
    public List<Parameter> getCustomParameters() {
        return Arrays.asList(
                //Jirra ACC-431
                new Parameter(
                        PARAMETER_lOCAL_STAFF_NATIONALITY_TAG,
                        "Local Nationality Tag",
                        "local_nationality",
                        "Local Nationality Tag (used for Pick List Items)"),
                new Parameter(
                        PARAMETER_PAYROLL_TRUSTEE_EMAIL,
                        PARAMETER_PAYROLL_TRUSTEE_EMAIL,
                        "<EMAIL>",
                        "Payroll Trustee emails to use it instead of Trustee Position"),
                new Parameter(
                        PARAMETER_ADEEB_EMAIL,
                        PARAMETER_ADEEB_EMAIL,
                        "<EMAIL>",
                        "Email To send final payroll."),
                new Parameter(
                        PARAMETER_ERROR_EMAIL,
                        PARAMETER_ERROR_EMAIL,
                        "<EMAIL>",
                        "Email To send general exceptions."),
                new Parameter(
                        PARAMETER_PAYROLL_EMAILS,
                        PARAMETER_PAYROLL_EMAILS,
                        "<EMAIL>",
                        "Email To send Payroll file."),
                new Parameter(
                        PARAMETER_PAYROLL_GENERATION_EMAILS,
                        PARAMETER_PAYROLL_GENERATION_EMAILS,
                        "<EMAIL>;<EMAIL>",
                        "Email To send Information when the housemaid payslips is being generated."),
                new Parameter(
                        PARAMETER_PAYROLL_EXCEPTIONS_EMAILS,
                        PARAMETER_PAYROLL_EXCEPTIONS_EMAILS,
                        "<EMAIL>",
                        "Email To send payroll generation exceptions."),
                new Parameter(PARAMETER_PAYROLL_JOBS_START,
                        PARAMETER_PAYROLL_JOBS_START,
                        "26",
                        "The start date to call Repayments and Deductions Limit jobs."),
                new Parameter(PARAMETER_PAYROLL_JOBS_END,
                        PARAMETER_PAYROLL_JOBS_END,
                        "5",
                        "The end date to call Repayments and Deductions Limit jobs."),
                new Parameter(
                        PARAMETER_LOCK_PAYROLL_ENABLED,
                        PARAMETER_LOCK_PAYROLL_ENABLED,
                        "1",
                        "Parameter to indicate if housemaid payroll profile updating is locked or not."),
                new Parameter(
                        PARAMETER_LOCK_PAYROLL_START,
                        PARAMETER_LOCK_PAYROLL_START,
                        "27",
                        "Start date of housemaid payroll profile updating is enabled."),
                new Parameter(
                        PARAMETER_LOCK_PAYROLL_END,
                        PARAMETER_LOCK_PAYROLL_END,
                        "2",
                        "End date of housemaid payroll profile updating is enabled."),
                new Parameter(
                        PARAMETER_EMPLOYEES_WITH_NO_EID_EMAILS,
                        PARAMETER_EMPLOYEES_WITH_NO_EID_EMAILS,
                        "<EMAIL>",
                        "Email To send Payroll file."),
                new Parameter(
                        PARAMETER_MV_INCLUDED_BY_AUDITOR_EMAILS,
                        PARAMETER_MV_INCLUDED_BY_AUDITOR_EMAILS,
                        "<EMAIL>",
                        "Email To send the MV maids that auditor included in payroll"),
                new Parameter(
                        PARAMETER_MV_INCLUDED_BY_AUDITOR_EMAIL_SUBJECT,
                        PARAMETER_MV_INCLUDED_BY_AUDITOR_EMAIL_SUBJECT,
                        "MV maids that auditors included in payroll",
                        "the subject of the INCLUDED MV Maids by Auditor report "),
                new Parameter(
                        PARAMETER_MV_EXCLUDED_AUDIT_TODO_EMAIL_SUBJECT,
                        PARAMETER_MV_EXCLUDED_AUDIT_TODO_EMAIL_SUBJECT,
                        "Urgent/ Check Excluded MV Maids and Take Action",
                        "the subject of the EXCLUDED MV Maids Audit Todo Notification Email"),
                new Parameter(
                        PARAMETER_CHECKLIST_EMAILS,
                        PARAMETER_CHECKLIST_EMAILS,
                        "<EMAIL>",
                        "Email To send Checklist file."),
                new Parameter(DDS_CANCELLATION_SCHEDULED_JOB_START_DAY,
                        "DDS Cancellation Scheduled Job Start Day",
                        "4",
                        "Start the Job Before N Day From the End of the Month"),
                new Parameter(
                        PARAMETER_CHECKLIST_EXCEPTIONS_EMAILS,
                        PARAMETER_CHECKLIST_EXCEPTIONS_EMAILS,
                        "<EMAIL>",
                        "Email To send Checklist file generation exceptions."),
                new Parameter(YAYA_CHAT_BOT_API,
                        "Yaya chat bot api",
                        "https://www.maid-ae.net/api/test/",
                        "Yaya Bot API Base URL."),
                new Parameter(YAYA_CHAT_BOT_SENDER_API,
                        "Yaya chat bot sender api",
                        "yayasender",
                        "Yaya Bot Sender API Base URL."),
                new Parameter(YAYA_CHAT_BOT_SALARY_DEDUCTIONS_API,
                        "Yaya chat bot salary deductions api",
                        "https://teljoy.io/test/modules/yayabot/web-app/#!/my-salary/deductions?psid=",
                        "Yaya Bot Salary Deduction API Base URL."),
                new Parameter(YAYA_CHAT_BOT_NOT_CONNECTED_MAIDS_API,
                        "Yaya chat bot api for not connected maids",
                        "getconnectedmaids",
                        "Yaya Bot Connected Maids API."),
                new Parameter(YAYA_CHAT_BOT_PaySlipsSender_Sender_API,
                        "Yaya chat bot pay slips sender api",
                        "payslipssender",
                        "Yaya Bot Payslips Sender API."),
                new Parameter(YAYA_CHAT_BOT_PaySlips_Photoes_Server,
                        "Yaya chat bot pay slips photoes server",
                        "http://localhost:8080",
                        "Yaya Bot API Base URL."),
                new Parameter(YAYA_USER,
                        "Yaya chat bot user name",
                        "chatbots",
                        "Yaya Bot User Name."),
                new Parameter(YAYA_PASSWORD,
                        "Yaya chat bot password",
                        "API#Magna$Bot",
                        "Yaya Bot Password."),
                new Parameter(MAIDS_AE_Payslips_Sender_API,
                        MAIDS_AE_Payslips_Sender_API,
                        "maidsae_payslipssender",
                        "Yaya bot API to send AE maids payslips."),
                new Parameter(SALESFORCE_APIS_BASE_URL,
                        "Salesforce apis base url",
                        "https://sandbox-maidsae.cs87.force.com/",
                        "Salesforce apis base url"),
                new Parameter(SALESFORCE_APIS_CLEANERS_SALARIES,
                        "Salesforce apis cleaners salaries",
                        "cleaners/services/apexrest/cleanersalariesws",
                        "Salesforce apis cleaners salaries"),
                new Parameter(SALESFORCE_APIS_QATAR_CLEANERS,
                        "Salesforce apis qatar cleaners",
                        "cleaners/services/apexrest/cleanerspayrollws",
                        "Salesforce apis qatar cleaners"),
                new Parameter(
                        PARAMETER_PAYSLIPS_ARE_BEING_GENRATED,
                        PARAMETER_PAYSLIPS_ARE_BEING_GENRATED,
                        "0",
                        "Parameter to indicate if Payslips generating job is running or not."),
                new Parameter(
                        PARAMETER_PAYROLL_SENDING_TO_FACEBOOK_EMAILS,
                        PARAMETER_PAYROLL_SENDING_TO_FACEBOOK_EMAILS,
                        "<EMAIL>",
                        "Email To send Information when the housemaid payslips is being sending."),
                new Parameter(
                        PARAMETER_PAYSLIPS_MESSENGER_JOB_IS_RUNNING,
                        PARAMETER_PAYSLIPS_MESSENGER_JOB_IS_RUNNING,
                        "0",
                        "Parameter to indicate if Payslips sending job is running or not."),
                new Parameter(
                        PARAMETER_WORKING_PUBLIC_HOLIDAYS_LIMIT,
                        PARAMETER_WORKING_PUBLIC_HOLIDAYS_LIMIT,
                        "25",
                        "working public holidays limit"),
                new Parameter(PARAMETER_OFFICESTAFF_WEEKLY_PAYROLL_REPORT_EMAIL,
                        PARAMETER_OFFICESTAFF_WEEKLY_PAYROLL_REPORT_EMAIL,
                        Setup.isTestMode() ? "" : "",
                        "Email to send report of OfficeStaff Weekly Termination Addition"),
                new Parameter(PARAMETER_NO_KIDS_DEDUCTION_ENABLED,
                        "No Kids Deduction Enabled",
                        "1",
                        "Parameter to enable or disable No kids deductions."),
                new Parameter(
                        PARAMETER_NO_KIDS_DEDUCTION_RESULT_EMAILS,
                        "No kids deduction result emails",
                        "<EMAIL>",
                        "Email to send the result of no kids deductions jobs."),
                //Jiira ACC-738
                new Parameter(PARAM_ATTENDANCE_DEDUCTION_AMOUNT_FILIPINO,
                        PARAM_ATTENDANCE_DEDUCTION_AMOUNT_FILIPINO,
                        "100",
                        "Deduction amount for Filipino maid absent."),
                new Parameter(PARAM_ATTENDANCE_DEDUCTION_AMOUNT_OTHER,
                        PARAM_ATTENDANCE_DEDUCTION_AMOUNT_OTHER,
                        "50",
                        "Deduction amount for maid absent."),
                new Parameter(PARAMETER_HOUSEMAID_VACATION_ALLOWANCE_EMAIL,
                        PARAMETER_HOUSEMAID_VACATION_ALLOWANCE_EMAIL,
                        Setup.isTestMode() ? "" : "",
                        "Email to send Housemaid Allowanace."),
                new Parameter(PARAMETER_OFFICESTAFF_VACATION_ALLOWANCE_EMAIL,
                        PARAMETER_HOUSEMAID_VACATION_ALLOWANCE_EMAIL,
                        Setup.isTestMode() ? "" : "",
                        "Email to send OfficeStaff Allowanace."),
                new Parameter(PARAMETER_BANK_TRANSFER_REPORT_RECEIVER_EMAIL,
                        PARAMETER_BANK_TRANSFER_REPORT_RECEIVER_EMAIL,
                        "",
                        "Email to send Bank Transfer Report."),
                /**
                 * BUSINESS RULES PARAMETERS
                 */
                // jira acc-1651
                new Parameter(PARAMETER_LOAN_HOMELESS_MAID,
                        "loan for homeless maid",
                        "3000",
                        "loan for homeless maid upon a decision by Recruitment manager"),
                new Parameter(
                        PARAMETER_HOUSEMAID_DEDCUCTION_NOTIFICATION_MSG,
                        PARAMETER_HOUSEMAID_DEDCUCTION_NOTIFICATION_MSG,
                        "Dear @maid_name@. You've received a deduction of @amount@ AED. Press @link@ to view your deduction and the reason.",
                        "Body of Notification Email on Housmaid Deduction."),
                new Parameter(
                        PARAMETER_HOUSEMAID_DEDCUCTION_NOTIFICATION_SMS,
                        PARAMETER_HOUSEMAID_DEDCUCTION_NOTIFICATION_SMS,
                        "Dear @maid_name@. You got a deduction of @amount@ because @reason_of_deduction@",
                        "Notification SMS on Housmaid Deduction."),
                new Parameter(
                        PARAMETER_Full_TIME_MAIDS_AT_CONVERTED_TO_HOUSE_MAID_CASH_ADVANCE_AMOUNT,
                        PARAMETER_Full_TIME_MAIDS_AT_CONVERTED_TO_HOUSE_MAID_CASH_ADVANCE_AMOUNT,
                        "3000",
                        "Amount of cash advance when MaidsAt converted to housemaid"),
                /**
                 * START DATE POLICY PARAMETERS
                 */
                new Parameter(
                        PARAMETER_START_DATE_N_WEEKS_POLICY_ENABLED,
                        "Start Date N Weeks: Policy Enabled",
                        "1",
                        "Parameter to indicate if the job of housemaid start date correction is enabled or not."),
                new Parameter(
                        PARAMETER_START_DATE_N_WEEKS_POLICY_DURATION,
                        "Start Date N Weeks: Policy Duration (In Days)",
                        "21",
                        "Period of the job of housemaid start date correction."),
                new Parameter(PARAMETER_START_DATE_N_DAYS_LIVE_OUT_POLICY_DURATION,
                        PARAMETER_START_DATE_N_DAYS_LIVE_OUT_POLICY_DURATION,
                        "7",
                        PARAMETER_START_DATE_N_DAYS_LIVE_OUT_POLICY_DURATION),
                new Parameter(
                        PARAMETER_START_DATE_N_WEEKS_POLICY_CUTOFF_DATE,
                        "Start Date N Weeks: Policy Cutoff Date",
                        "2018-07-15",
                        "Cutoff date of the job of housemaid start date correction."),
                new Parameter(
                        PARAMETER_START_DATE_N_WEEKS_POLICY_EMAILS,
                        "Start Date N Weeks: Policy Emails",
                        "<EMAIL>;<EMAIL>",
                        "Emails of housemaid start date correction."),
                /**
                 * REPAYMENT PARAMETERS
                 */
                new Parameter(
                        PARAMETER_REPAYMENT_SUMMARY_EMAIL,
                        PARAMETER_REPAYMENT_SUMMARY_EMAIL,
                        "<EMAIL>",
                        "Email To send summary of genratred repayments for both housemaids and OfficeStaffs."),
                new Parameter(DEFAULT_MONTHLY_REPAYMENT_ETHIOPIAN,
                        DEFAULT_MONTHLY_REPAYMENT_ETHIOPIAN,
                        "200",
                        "Default amount of Ethiopian maids monthly repayment."),
                new Parameter(DEFAULT_MONTHLY_REPAYMENT_KENYANS,
                        DEFAULT_MONTHLY_REPAYMENT_KENYANS,
                        "200",
                        "Default amount of Kenyan maids monthly repayment."),
                new Parameter(DEFAULT_MONTHLY_REPAYMENT_PHILIPPINES,
                        DEFAULT_MONTHLY_REPAYMENT_PHILIPPINES,
                        "500",
                        "Default amount of Philippines maids monthly repayment."),
                new Parameter(DEFAULT_MONTHLY_REPAYMENT_AFRICANS,
                        DEFAULT_MONTHLY_REPAYMENT_AFRICANS,
                        "200",
                        "Default amount of African maids monthly repayment."),
                new Parameter(DEFAULT_MONTHLY_REPAYMENT,
                        DEFAULT_MONTHLY_REPAYMENT,
                        "500",
                        "Default amount of maids monthly repayment."),
                /**
                 * SALARY COMPONENTS PARAMETERS
                 */
                new Parameter(
                        SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_ETHIOPIAN_NO_ARABIC,
                        "Salary Components - Freedom Maids - Cutoff Applied - Nationality: Ethiopian - Doesn't speak arabic",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(350d, 0d, 0d, 0d, 0d)),
                        "Salary Components - Freedom Maids - Cutoff Applied - Nationality: Ethiopian - Doesn't speak arabic"),
                new Parameter(
                        SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_ETHIOPIAN_ARABIC_SPEAKER,
                        "Salary Components - Freedom Maids - Cutoff Applied - Nationality: Ethiopian -Speaks Arabic",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(450d, 0d, 0d, 0d, 0d)),
                        "Salary Components - Freedom Maids - Cutoff Applied - Nationality: Ethiopian -Speaks Arabic"),
                new Parameter(
                        SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_NON_ETHIOPIAN,
                        "Salary Components - Freedom Maids - Cutoff Applied - Nationality: Anything other than Ethiopian",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(750d, 0d, 0d, 0d, 0d)),
                        "Salary Components - Freedom Maids - Cutoff Applied - Nationality: Anything other than Ethiopian"),
                new Parameter(
                        SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_KENYAN,
                        "Salary Components - Freedom Maids - Cutoff Applied - Nationality: Kenyan",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(450d, 0d, 0d, 0d, 0d)),
                        "Salary Components - Freedom Maids - Cutoff Applied - Nationality: Kenyan"),
                //Jirra ACC-597
                new Parameter(
                        SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_CAMEROONIAN,
                        "Salary Components - Freedom Maids - Cutoff Applied - Nationality: Cameroonian",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(546d, 164d, 290d, 0d, 0d)),
                        "Salary Components - Freedom Maids - Cutoff Applied - Nationality: Cameroonian"),

                //Salaries, Freedom No Cutoff
                new Parameter(
                        SALARY_COMPONENTS_FREEDOM_NON_ETHIOPIAN,
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Anything other than Ethiopian",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(846d, 364d, 290d, 0d, 0d)),
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Anything other than Ethiopian"),
                new Parameter(
                        SALARY_COMPONENTS_FREEDOM_ETHIOPIAN_NO_ARABIC,
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Ethiopian - Doesn't speak arabic",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(346d, 64d, 290d, 0d, 0d)),
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Ethiopian - Doesn't speak arabic"),
                new Parameter(
                        SALARY_COMPONENTS_FREEDOM_ETHIOPIAN_ARABIC_SPEAKER,
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Ethiopian - Speaks Arabic",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(546d, 64d, 290d, 0d, 0d)),
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Ethiopian - Speaks Arabic"),
                new Parameter(
                        SALARY_COMPONENTS_FREEDOM_KENYAN,
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Kenyan",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(546d, 164d, 290d, 0d, 0d)),
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Kenyan"),
                new Parameter(
                        SALARY_COMPONENTS_FREEDOM_INDIAN,
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Indian",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(646d, 264d, 290d, 0d, 0d)),
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Indian"),
                new Parameter(
                        SALARY_COMPONENTS_FREEDOM_SRILANKAN_ARABIC_SPEAKER,
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Srilankan - Speaks Arabic",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(546d, 264d, 290d, 0d, 0d)),
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Srilankan - Speaks Arabic"),
                new Parameter(
                        SALARY_COMPONENTS_FREEDOM_SRILANKAN_NO_ARABIC,
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Srilankan - Doesn't speak arabic",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(546d, 164d, 290d, 0d, 0d)),
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Srilankan - Doesn't speak arabic"),
                //Jirra ACC-597
                new Parameter(
                        SALARY_COMPONENTS_FREEDOM_CAMEROONIAN,
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Cameroonian",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(546d, 164d, 290d, 0d, 0d)),
                        "Salary Components - Freedom Maids - Cutoff Not Applied - Nationality: Cameroonian"),
                //Salaries, Agency
                new Parameter(
                        SALARY_COMPONENTS_AGENCY,
                        "Salary Components - Agency Maids",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(1500d, 0d, 0d, 0d, 0d)),
                        "Salary Components - Agency Maids"),

                //Salaries, Visa
                new Parameter(
                        SALARY_COMPONENTS_VISA,
                        "Salary Components - Visa Maids",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(400d, 0d, 0d, 600d, 0d)),
                        "Salary Components - Visa Maids"),
                //Salaries, Clean Exit
                new Parameter(
                        SALARY_COMPONENTS_CLEAN_EXIT_ETHIOPIAN_NO_ARABIC,
                        "Salary Components - Clean Exit Maids - Nationality: Ethiopian - Doesn't speak arabic",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(346d, 64d, 290d, 0d, 0d)),
                        "Salary Components - Clean Exit Maids - Nationality: Ethiopian - Doesn't speak arabic"),
                new Parameter(
                        SALARY_COMPONENTS_CLEAN_EXIT_ETHIOPIAN_ARABIC_SPEAKER,
                        "Salary Components - Clean Exit Maids - Nationality: Ethiopian - Speaks arabic",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(546d, 64d, 290d, 0d, 0d)),
                        "Salary Components - Clean Exit Maids - Nationality: Ethiopian - Speaks arabic"),
                new Parameter(
                        SALARY_COMPONENTS_CLEAN_EXIT_INDONESIAN,
                        "Salary Components - Clean Exit Maids - Nationality: Indonesian",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(910d, 200d, 290d, 0d, 0d)),
                        "Salary Components - Clean Exit Maids - Nationality: Indonesian"),
                new Parameter(
                        SALARY_COMPONENTS_CLEAN_EXIT_INDIAN,
                        "Salary Components - Clean Exit Maids - Nationality: Indian",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(646d, 264d, 290d, 0d, 0d)),
                        "Salary Components - Clean Exit Maids - Nationality: Indian"),
                new Parameter(
                        SALARY_COMPONENTS_CLEAN_EXIT_KENYAN,
                        "Salary Components - Clean Exit Maids - Nationality: Kenyan",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(546d, 164d, 290d, 0d, 0d)),
                        "Salary Components - Clean Exit Maids - Nationality: Kenyan"),
                new Parameter(
                        SALARY_COMPONENTS_CLEAN_EXIT_SRILANKAN_NO_ARABIC,
                        "Salary Components - Clean Exit Maids - Nationality: Srilankan - Doesn't speak arabic",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(546d, 164d, 290d, 0d, 0d)),
                        "Salary Components - Clean Exit Maids - Nationality: Srilankan - Doesn't speak arabic"),
                new Parameter(
                        SALARY_COMPONENTS_CLEAN_EXIT_SRILANKAN_ARABIC_SPEAKER,
                        "Salary Components - Clean Exit Maids - Nationality: Srilankan - Speaks arabic",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(546d, 264d, 290d, 0d, 0d)),
                        "Salary Components - Clean Exit Maids - Nationality: Srilankan - Speaks arabic"),
                new Parameter(
                        SALARY_COMPONENTS_CLEAN_EXIT_FILIPINO,
                        "Salary Components - Clean Exit Maids - Nationality: Filipino",
                        SalaryLibrary.encodeSalary(new BasicSalaryBreakDown(846d, 334d, 420d, 0d, 0d)),
                        "Salary Components - Clean Exit Maids - Nationality: Filipino"),
                /**
                 *  DEDUCTION LIMIT PARAMETERS
                 */
                new Parameter(PARAMETER_DEDUCTION_LIMIT,
                        PARAMETER_DEDUCTION_LIMIT,
                        "250",
                        "Default amount of maids deductions limit."),
                new Parameter(PARAMETER_FILIPINOES_DEDUCTION_LIMIT,
                        PARAMETER_FILIPINOES_DEDUCTION_LIMIT,
                        "700",
                        "Default amount of maids deductions limit."),
                new Parameter(PARAMETER_ETHIOPIAN_DEDUCTION_LIMIT,
                        PARAMETER_ETHIOPIAN_DEDUCTION_LIMIT,
                        "250",
                        "Default amount of ethiopians maids deductions limit."),
                //
                new Parameter(PARAMETER_MAIDSAT_DEDUCTION_LIMIT,
                        PARAMETER_MAIDSAT_DEDUCTION_LIMIT,
                        "700",
                        "Default amount of maids.at deductions limit."),
                new Parameter(PARAMETER_AFRICANS_DEDUCTION_LIMIT,
                        PARAMETER_AFRICANS_DEDUCTION_LIMIT,
                        "250",
                        "African maids deduction limit"),
                new Parameter(
                        PARAMETER_HOUSEMAID_RULES_JOB_EMAILS,
                        "Housemaid Rules Job - Emails",
                        "<EMAIL>",
                        "Emails to send exceptions are accruing during Housemaid Rules Job running."),
                new Parameter(
                        PARAMETER_HOUSEMAID_RULES_JOB_ENABLED,
                        "Housemaid Rules Job - Is Enabled?",
                        String.valueOf(Boolean.FALSE),
                        "A flag to indicate if the Housemaid Rules Job Is Enabled ot not."),
                /**
                 * PAY-497 Limit SMS reminders sent in payroll
                 */
                new Parameter(
                        PARAMETER_PAYROLL_REMINDERS_LIMIT,
                        PARAMETER_PAYROLL_REMINDERS_LIMIT,
                        "3",
                        "Limit Payroll Reminders"),
                /**
                 *  OFFICE STAFF SALARY COMPONENTS PARAMETERS
                 */
                new Parameter(PARAMETER_EXPAT_BASIC_SALARY_PERCENTAGE,
                        PARAMETER_EXPAT_BASIC_SALARY_PERCENTAGE,
                        "60",
                        "Basic salary percentage of office staff expat salary"),
                new Parameter(PARAMETER_EXPAT_HOUSING_PERCENTAGE,
                        PARAMETER_EXPAT_HOUSING_PERCENTAGE,
                        "35",
                        "Housing percentage of office staff expat salary"),
                //
                new Parameter(PARAMETER_EXPAT_TRANSPORTATION_PERCENTAGE,
                        PARAMETER_EXPAT_TRANSPORTATION_PERCENTAGE,
                        "5",
                        "Transportation percentage of office staff expat salary"),
                /**
                 *  JAZZ HR PARAMETERS
                 */
                new Parameter(PARAMETER_JAZZ_HR_API_KEY,
                        PARAMETER_JAZZ_HR_API_KEY,
                        "iLxzeOv4ujozxOy4a1OOQdWCmeQsJ2i4",
                        "JazzHR API Key"),
                new Parameter(PARAMETER_JAZZ_HR_FETCH_HIRES_API,
                        PARAMETER_JAZZ_HR_FETCH_HIRES_API,
                        "https://api.resumatorapi.com/v1/hires/page/",
                        "JazzHR Fetch Hired Employees API"),
                new Parameter(PARAMETER_JAZZ_HR_FETCH_APPLICANT_API,
                        PARAMETER_JAZZ_HR_FETCH_APPLICANT_API,
                        "https://api.resumatorapi.com/v1/applicants/",
                        "JazzHR Fetch Specific Applicant API"),
                new Parameter(PARAMETER_MIN_HIRE_DATE_TO_CREATE_HIRE_EMPLOYEE_TODO,
                        PARAMETER_MIN_HIRE_DATE_TO_CREATE_HIRE_EMPLOYEE_TODO,
                        "0",
                        "Create hire employee todo for employees who are hired before x days from today"),
                /**
                 * PAYROLL MANAGEMENT MODULE EMAIL SUBJECT AND BODY FOR SENDING QUESTIONNAIRES TO EMPLOYEE (CANDIDATE) PARAMETERS PAY-2
                 */
                new Parameter(NEW_STAFF_QUESTIONNAIRE_EMAIL_SUBJECT,
                        NEW_STAFF_QUESTIONNAIRE_EMAIL_SUBJECT,
                        "New Hire Questionnaire",
                        "the email's subject that will be send to new staff to fill a questionnaire with his/her information"),
                new Parameter(NEW_STAFF_QUESTIONNAIRE_EMAIL_BODY,
                        NEW_STAFF_QUESTIONNAIRE_EMAIL_BODY,
                        "Greetings from maids.cc. Please fill in the below questionnaires to complete your payroll profile. You should fill in all fields.",
                        "the email's body that will be send to new staff to fill a questionnaire with his/her information"),
                new Parameter(PARAMETER_NEW_STAFF_QUESTIONNAIRE_CC_EMAIL_RECEIVER,
                        PARAMETER_NEW_STAFF_QUESTIONNAIRE_CC_EMAIL_RECEIVER,
                        "",
                        "the email CC receiver when we send the questionnaire to a new staff"),
                new Parameter(PARAMETER_IBAN_CHECK_API_KEY, "IBAN Validation API Key",
                        "3c63b9cab271a4be7a1ddd1807fb0c3e", "api key that used to get IBAN info"),
                new Parameter(PARAMETER_TEST_EMAIL_RECEIVER, PARAMETER_TEST_EMAIL_RECEIVER,
                        "<EMAIL>", "Payroll Module Test Email"),
                new Parameter(PARAMETER_CFO_APPROVAL_REMINDER, PARAMETER_CFO_APPROVAL_REMINDER,
                        "4", "Number of hours to wait for CFO approval of final settlement before creating a todo reminder."),
                new Parameter(PARAMETER_CFO_EMAIL, PARAMETER_CFO_EMAIL,
                        "<EMAIL>", "CFO email to receive the final settlement notifications."),
                new Parameter(CFO_FINAL_SETTLEMENT, CFO_FINAL_SETTLEMENT,
                        "", "CFO email to receive the final settlement Email NEW"),
                new Parameter(CFO_FINAL_SETTLEMENT_USER_ID, CFO_FINAL_SETTLEMENT_USER_ID,
                        "", "CFO email to receive the final settlement Email NEW"),
                new Parameter(PARAMETER_CFO_ID, PARAMETER_CFO_ID,
                        "1", "CFO ID to use it as manager if an office staff doesn't have a manager."),
                new Parameter(PARAMETER_CEO_ID, PARAMETER_CEO_ID,
                        "1", "CEO ID to use it for the CEO Approval page in Accounting."),
                new Parameter(PARAMETER_DEDUCTION_CALCULATION_IS_ACTIVE, PARAMETER_DEDUCTION_CALCULATION_IS_ACTIVE,
                        "1", "calculate the deductions in cap phase or not (1 to activate)"),
                new Parameter(PARAMETER_PAYROLL_USD_TO_AED_EXCHANGE_RATE, PARAMETER_PAYROLL_USD_TO_AED_EXCHANGE_RATE,
                        "3.676", "the exchange rate between USD and AED"),
                new Parameter(PARAMETER_REMIND_HANDLER_TO_REVOKE_ACCESSES, PARAMETER_REMIND_HANDLER_TO_REVOKE_ACCESSES,
                        "30", "Wait period in minutes to remind handler to revoke accesses."),
                new Parameter(PARAMETER_ANSARI_CHANGE_BANK_DETAILS_EMAIL, PARAMETER_ANSARI_CHANGE_BANK_DETAILS_EMAIL,
                        "<EMAIL>", "Ansari change bank details"),
                new Parameter(PARAMETER_MONEY_EXCHANGE_NAME, PARAMETER_MONEY_EXCHANGE_NAME,
                        "Ansari", "Money Exchange Name"),
                new Parameter(PARAMETER_HOUSEMAID_SALARY_RAISE_LIMIT, PARAMETER_HOUSEMAID_SALARY_RAISE_LIMIT,
                        "300", "Housemaid salary raise limit"),
                new Parameter(PARAMETER_HOUSEMAID_LOAN_REPAYMENT_SKIPPED_LIMIT, PARAMETER_HOUSEMAID_LOAN_REPAYMENT_SKIPPED_LIMIT,
                        "3", "months limit for Housemaid repetitive loan repayments skipped"),
                new Parameter(PARAMETER_HOUSEMAID_REPETITIVE_ADDITION_LIMIT, PARAMETER_HOUSEMAID_REPETITIVE_ADDITION_LIMIT,
                        "3", "months limit for Housemaid repetitive addition"),
                new Parameter(PARAMETER_HOUSEMAID_SICK_LEAVE_DAYS_LIMIT, PARAMETER_HOUSEMAID_SICK_LEAVE_DAYS_LIMIT,
                        "20", "days limit for Housemaid that is paid and is sick for"),
                new Parameter(PARAMETER_HOUSEMAID_NOT_WITH_CLIENT_MONTHS_LIMIT, PARAMETER_HOUSEMAID_NOT_WITH_CLIENT_MONTHS_LIMIT,
                        "2", "months limit for Housemaid not with client"),
                new Parameter(PARAMETER_HOUSEMAID_FILIPINO_RECEIVED_PAYMENT_LIMIT, PARAMETER_HOUSEMAID_FILIPINO_RECEIVED_PAYMENT_LIMIT,
                        "3500", "amount limit for Filipino Housemaid received payment"),
                new Parameter(PARAMETER_HOUSEMAID_ETHIOPIAN_RECEIVED_PAYMENT_LIMIT, PARAMETER_HOUSEMAID_ETHIOPIAN_RECEIVED_PAYMENT_LIMIT,
                        "2300", "amount limit for Ethiopian Housemaid received payment"),
                new Parameter(PARAMETER_HOUSEMAID_OTHER_NATIONALITY_RECEIVED_PAYMENT_LIMIT, PARAMETER_HOUSEMAID_OTHER_NATIONALITY_RECEIVED_PAYMENT_LIMIT,
                        "2400", "amount limit for other nationality Housemaid received payment"),
                new Parameter(PARAMETER_HOUSEMAID_FILIPINO_AIRFARE_TICKET_LIMIT, PARAMETER_HOUSEMAID_FILIPINO_AIRFARE_TICKET_LIMIT,
                        "2000", "amount limit for Filipino Housemaid Airfare Ticket"),
                new Parameter(PARAMETER_HOUSEMAID_OTHER_NATIONALITY_AIRFARE_TICKET_LIMIT, PARAMETER_HOUSEMAID_OTHER_NATIONALITY_AIRFARE_TICKET_LIMIT,
                        "1350", "amount limit for other nationality Housemaid Airfare Ticket"),
                new Parameter(PARAMETER_PAYROLL_AUDIT_REPORT_EMAIL_SUBJECT, PARAMETER_PAYROLL_AUDIT_REPORT_EMAIL_SUBJECT,
                        "payroll audit report", "email subject for payroll audit report"),
                new Parameter(PARAMETER_PAYROLL_AUDIT_REPORT_EMAIL_RECIPIENTS, PARAMETER_PAYROLL_AUDIT_REPORT_EMAIL_RECIPIENTS,
                        "<EMAIL>", "email recipients for payroll audit report"),
                new Parameter(PARAMETER_HOUSEMAID_FILIPINO_SALARY_LIMIT, PARAMETER_HOUSEMAID_FILIPINO_SALARY_LIMIT,
                        "2000", "Filipino normal salary"),
                new Parameter(PARAMETER_HOUSEMAID_ETHIOPIAN_SALARY_LIMIT, PARAMETER_HOUSEMAID_ETHIOPIAN_SALARY_LIMIT,
                        "1150", "Ethiopian normal salary"),
                new Parameter(PARAMETER_HOUSEMAID_INDIAN_SALARY_LIMIT, PARAMETER_HOUSEMAID_INDIAN_SALARY_LIMIT,
                        "1200", "Indian normal salary"),
                new Parameter(PARAMETER_HOUSEMAID_RENEWED_INDIAN_SALARY_LIMIT, PARAMETER_HOUSEMAID_RENEWED_INDIAN_SALARY_LIMIT,
                        "1350", "Renewed Indian normal salary"),
                new Parameter(PARAMETER_HOUSEMAID_INDONESIAN_SALARY_LIMIT, PARAMETER_HOUSEMAID_INDONESIAN_SALARY_LIMIT,
                        "1200", "Indonesian normal salary"),
                new Parameter(PARAMETER_HOUSEMAID_RENEWED_INDONESIAN_SALARY_LIMIT, PARAMETER_HOUSEMAID_RENEWED_INDONESIAN_SALARY_LIMIT,
                        "1550", "Renewed Indonesian normal salary"),
                new Parameter(PARAMETER_HOUSEMAID_AFRICAN_SALARY_LIMIT, PARAMETER_HOUSEMAID_AFRICAN_SALARY_LIMIT,
                        "1150", "African normal salary"),
                new Parameter(PARAMETER_HOUSEMAID_OTHER_NATIONALITY_SALARY_LIMIT, PARAMETER_HOUSEMAID_OTHER_NATIONALITY_SALARY_LIMIT,
                        "1200", "other nationalities normal salary"),
                new Parameter(PARAMETER_PHILIPPINE_MONEY_TRANSFER_CENTER, PARAMETER_PHILIPPINE_MONEY_TRANSFER_CENTER,
                        "Western Union", "Philippine money transfer center"),
                new Parameter(PARAMETER_LEBANON_MONEY_TRANSFER_CENTER, PARAMETER_LEBANON_MONEY_TRANSFER_CENTER,
                        "Srour Exchange", "Lebanon money transfer center"),
                new Parameter(PARAMETER_PAYROLL_CORE_EMAIL_ADDRESS, PARAMETER_PAYROLL_CORE_EMAIL_ADDRESS,
                        "<EMAIL>", "Payroll module email's address"),
                new Parameter(PARAMETER_PAYROLL_CORE_EMAIL_PASSWORD, PARAMETER_PAYROLL_CORE_EMAIL_PASSWORD,
                        "SayUR6Rbnnn66M7a", "Payroll module email's password"),
                new Parameter(PARAMETER_PAYROLL_PAYMENT_APPROVE_RECEIVERS, PARAMETER_PAYROLL_PAYMENT_APPROVE_RECEIVERS,
                        "<EMAIL>", "Payroll payment approve receivers"),
                new Parameter(PARAMETER_PAYROLL_PAYMENT_APPROVE_USER_ID_RECEIVERS, PARAMETER_PAYROLL_PAYMENT_APPROVE_USER_ID_RECEIVERS,
                        "", "Payroll payment approve receivers users ids"),
                new Parameter(PARAMETER_PAYROLL_PAYMENT_APPROVE_RECEIVERS_FOR_BANK_TRANSFER, PARAMETER_PAYROLL_PAYMENT_APPROVE_RECEIVERS_FOR_BANK_TRANSFER,
                        "<EMAIL>", "Payroll payment approve receivers for bank transfer"),
                new Parameter(PARAMETER_PAYROLL_PAYMENT_APPROVE_USER_ID_RECEIVERS_FOR_BANK_TRANSFER, PARAMETER_PAYROLL_PAYMENT_APPROVE_USER_ID_RECEIVERS_FOR_BANK_TRANSFER,
                        "", "Payroll payment approve receivers (user ids) for bank transfer"),
                new Parameter(PARAMETER_PAYROLL_PAYMENT_APPROVE_SUBJECT, PARAMETER_PAYROLL_PAYMENT_APPROVE_SUBJECT,
                        "Approve Payment Files Before Sending to Ansari", "Payroll payment approve subject"),
                new Parameter(PARAMETER_PAYROLL_PAYMENT_APPROVE_SUBJECT_FOR_BANK_TRANSFER, PARAMETER_PAYROLL_PAYMENT_APPROVE_SUBJECT_FOR_BANK_TRANSFER,
                        "Approve Bank Transfer File Before Transferring Money", "Payroll payment approve subject for bank transfer"),
                new Parameter(PARAMETER_PAYROLL_AUDITORS_RECIPIENTS_OF_PAYROLL_FILES_AFTER_CFO_APPROVAL, PARAMETER_PAYROLL_AUDITORS_RECIPIENTS_OF_PAYROLL_FILES_AFTER_CFO_APPROVAL,
                        "<EMAIL>", "Payroll Auditors email receivers of the payroll files after CFO approval"),
                new Parameter(PARAMETER_PAYROLL_AUDITORS_SUBJECT_OF_PAYROLL_FILES_AFTER_CFO_APPROVAL, PARAMETER_PAYROLL_AUDITORS_SUBJECT_OF_PAYROLL_FILES_AFTER_CFO_APPROVAL,
                        "Final Payroll Files of @payroll_month_and_year@", "Payroll Auditors email subject of the payroll files after CFO approval"),
                new Parameter(UPDATE_INFO_QUESTIONNAIRE_EMAIL_SUBJECT,
                        UPDATE_INFO_QUESTIONNAIRE_EMAIL_SUBJECT,
                        "Update info Questionnaire",
                        "The email's subject that will be send to staff to fill a questionnaire with his/her updated information"),
                /*
                 * RM-248 EXCLUDE MAID VISA MAIDS WITH LESS THAN salary + 578
                 */
                new Parameter(PARAMETER_ADDITIONAL_TO_MAID_VISA_PAYMENT, PARAMETER_ADDITIONAL_TO_MAID_VISA_PAYMENT,
                        "578", "Payroll module additional value to maid visa payment"),
                new Parameter(PARAMETER_MAIDS_INCLUDED_IN_PAYROLL, PARAMETER_MAIDS_INCLUDED_IN_PAYROLL,
                        "", "Maids that must be included in payroll"),
                new Parameter(PARAMETER_ANSARI_PREMIUM_SERVICES_EMAIL, PARAMETER_ANSARI_PREMIUM_SERVICES_EMAIL,
                        "<EMAIL>", "Ansari premium services email address."),
                new Parameter(PARAMETER_SYRIA_TRANSFERS_EXCHANGE_RATE_EMAIL_CC_RECEIVERS, PARAMETER_SYRIA_TRANSFERS_EXCHANGE_RATE_EMAIL_CC_RECEIVERS,
                        "<EMAIL>", "Syria transfers exchange rate email cc receivers."),
                new Parameter(PARAMETER_LAST_RUN_DATE_OF_SYRIA_TRANSFERS_JOB, PARAMETER_LAST_RUN_DATE_OF_SYRIA_TRANSFERS_JOB,
                        "", "Last run date of syria transfers job."),
                new Parameter(PARAMETER_AUDIT_TODO_REMINDER_CUTOFF, PARAMETER_AUDIT_TODO_REMINDER_CUTOFF,
                        "16", "the cutoff hour to start the audit todo reminder"),
                new Parameter(PARAMETER_AUDIT_TODO_REMINDER_RECIPIENT_EMAILS, PARAMETER_AUDIT_TODO_REMINDER_RECIPIENT_EMAILS,
                        "", "the Audit Todo reminder recipients emails"),
                new Parameter(PARAMETER_AUDIT_TODO_REMINDER_CC_EMAILS, PARAMETER_AUDIT_TODO_REMINDER_CC_EMAILS,
                        "", "the Audit Todo reminder CC emails"),
                new Parameter(PARAMETER_ACCOUNTANT_TODO_REMINDER_RECIPIENT_EMAILS, PARAMETER_ACCOUNTANT_TODO_REMINDER_RECIPIENT_EMAILS,
                        "", "the Audit Todo reminder recipients emails"),
                new Parameter(PARAMETER_ACCOUNTANT_TODO_REMINDER_CC_EMAILS, PARAMETER_ACCOUNTANT_TODO_REMINDER_CC_EMAILS,
                        "", "the Audit Todo reminder CC emails"),

                new Parameter(PARAMETER_FAILED_CREATE_MAID_FINAL_SETTLEMENT_EMAIL_RECIPIENTS,
                        PARAMETER_FAILED_CREATE_MAID_FINAL_SETTLEMENT_EMAIL_RECIPIENTS,
                        "<EMAIL>, <EMAIL>", "failed on create maid final settlement email recipients, separate them using comma"),
                new Parameter(PARAMETER_PAY_REPEAT_EID_DEDUCTION_AMOUNT,
                        PARAMETER_PAY_REPEAT_EID_DEDUCTION_AMOUNT,
                        "372", "repeat eid deduction amount"),
                new Parameter(PARAMETER_AUDITOR_ACTIONS_ON_PAYROLL_MANAGER_NOTE_JOB_CONF, PARAMETER_AUDITOR_ACTIONS_ON_PAYROLL_MANAGER_NOTE_JOB_CONF,
                        "{\"emailRecipient\":\"<EMAIL>\"," +
                                "\"emailCC\":\"<EMAIL>\"}", "AuditorActionsJob configuration"),
                new Parameter(PARAMETER_AUTHORIZE_PAYROLL_TRANSFER_RECEIVERS_EMAIL, PARAMETER_AUTHORIZE_PAYROLL_TRANSFER_RECEIVERS_EMAIL,
                        "", "Authorize payroll receivers emails"),
                new Parameter(PARAMETER_AUTHORIZE_PAYROLL_TRANSFER_CC_EMAIL, PARAMETER_AUTHORIZE_PAYROLL_TRANSFER_CC_EMAIL,
                        "", "Authorize payroll CC emails"),
                new Parameter(PARAMETER_PAYROLL_GENERATION_START_RECEIVERS_EMAIL, PARAMETER_PAYROLL_GENERATION_START_RECEIVERS_EMAIL,
                        "", "payroll generation start receivers emails"),
                new Parameter(PARAMETER_PAYROLL_GENERATION_START_CC_EMAIL, PARAMETER_PAYROLL_GENERATION_START_CC_EMAIL,
                        "", "payroll generation start CC emails"),
                new Parameter(PARAMETER_PAYROLL_GENERATION_FINISHED_RECEIVERS_EMAIL, PARAMETER_PAYROLL_GENERATION_FINISHED_RECEIVERS_EMAIL,
                        "", "payroll generation finished receivers emails"),
                new Parameter(PARAMETER_PAYROLL_GENERATION_FINISHED_CC_EMAIL, PARAMETER_PAYROLL_GENERATION_FINISHED_CC_EMAIL,
                        "", "payroll generation finished CC emails"),

                new Parameter(PARAMETER_RENEWED_PHILIPPINES_HOUSEMAID_IN_ACCOMMODATION_SALARY, PARAMETER_RENEWED_PHILIPPINES_HOUSEMAID_IN_ACCOMMODATION_SALARY,
                        "1500", "renewed philippines maids default accommodation salary amount "),

                new Parameter(Check_Atm_Website, Check_Atm_Website,
                        "https://ppc.bankfab.com/PPCinquiry/", Check_Atm_Website),

                new Parameter(PARAMETER_ACTIVATE_ATM_CARD, PARAMETER_ACTIVATE_ATM_CARD,
                        "https://youtu.be/fU8-nQhFCFQ", PARAMETER_ACTIVATE_ATM_CARD),

                new Parameter(PARAMETER_CHECK_ATM_CARD_BALANCE, PARAMETER_CHECK_ATM_CARD_BALANCE,
                        "https://youtu.be/fU8-nQhFCFQ", PARAMETER_CHECK_ATM_CARD_BALANCE),

                new Parameter(PARAMETER_USE_ATM_CARD_FROM_OUTSIDE, PARAMETER_USE_ATM_CARD_FROM_OUTSIDE,
                        "https://youtu.be/fU8-nQhFCFQ", PARAMETER_USE_ATM_CARD_FROM_OUTSIDE),

                new Parameter(PARAMETER_SEND_MONEY_TO_FAMILY_WITH_ATM_CARD, PARAMETER_SEND_MONEY_TO_FAMILY_WITH_ATM_CARD,
                        "https://youtu.be/fU8-nQhFCFQ", PARAMETER_SEND_MONEY_TO_FAMILY_WITH_ATM_CARD),

                new Parameter(PARAMETER_SEND_MONEY_TO_FAMILY_WITHOUT_ATM_CARD, PARAMETER_SEND_MONEY_TO_FAMILY_WITHOUT_ATM_CARD,
                        "https://youtu.be/fU8-nQhFCFQ", PARAMETER_SEND_MONEY_TO_FAMILY_WITHOUT_ATM_CARD),

                new Parameter(PARAMETER_COLLECT_ATM_CARD, PARAMETER_COLLECT_ATM_CARD,
                        "https://youtu.be/fU8-nQhFCFQ", PARAMETER_COLLECT_ATM_CARD),

                new Parameter(PARAMETER_ANSARI_LOCATION, PARAMETER_ANSARI_LOCATION,
                        "https://www.google.com/maps?q=25.117663,55.20394", PARAMETER_ANSARI_LOCATION),

                new Parameter(PARAMETER_MUSHRIF_MALL_AUH_LOCATION, PARAMETER_MUSHRIF_MALL_AUH_LOCATION,
                        "https://www.google.com/maps/place/24%C2%B026'05.0%22N+54%C2%B024'46.8%22E/@24.4354726,54.4112457,17z/data=!4m4!3m3!8m2!3d24.4347222!4d54.413?entry=ttu", PARAMETER_MUSHRIF_MALL_AUH_LOCATION),

                new Parameter(PARAMETER_ANSARI_BRANCH, PARAMETER_ANSARI_BRANCH,
                        "http://alansariexchange.com/branches/", PARAMETER_ANSARI_BRANCH),

                new Parameter(PARAMETER_ANSARI_MAIN_BRANCH_ADDRESS, PARAMETER_ANSARI_MAIN_BRANCH_ADDRESS,
                        "Level 7, Al Ansari Business Center, Al Barsha 1,\n" +
                                "Beside Mall of the Emirates\n" +
                                "P.O.Box 6176, Dubai, UAE.", PARAMETER_ANSARI_MAIN_BRANCH_ADDRESS),
                //PAY-526
                new Parameter(PARAMETER_CC_MANAGER_USER_ID, PARAMETER_CC_MANAGER_USER_ID,
                        "", "CC Manager Id"),
                new Parameter(PARAMETER_MV_MANAGER_USER_ID, PARAMETER_MV_MANAGER_USER_ID,
                        "", "MV Manager Id"),
                new Parameter(PARAMETER_EXCLUDE_MAID_EMAIL_TITLE, PARAMETER_EXCLUDE_MAID_EMAIL_TITLE,
                        "@maid_name@ excluded from payroll", "Exclude Maid Email Title"),
                new Parameter(PARAMETER_QUESTION_ABOUT_SALARY_EMAIL_TITLE, PARAMETER_QUESTION_ABOUT_SALARY_EMAIL_TITLE,
                        "Question for @maid_name@ salary from Auditor", "Question About Salary Email title"),
                new Parameter(PARAMETER_REPLAY_QUESTION_ABOUT_SALARY_EMAIL_TITLE, PARAMETER_REPLAY_QUESTION_ABOUT_SALARY_EMAIL_TITLE,
                        "Answer from CC Manager on @maid_name@ salary", "replay Question About Salary Email title"),
                new Parameter(PARAMETER_CC_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_TITLE, PARAMETER_CC_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_TITLE,
                        "Check list of CC Maids excluded from payroll @payroll_month@", "CC maids manually excluded from profile Email title"),
                new Parameter(PARAMETER_CC_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_RECIPIENTS, PARAMETER_CC_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_RECIPIENTS,
                        "", "CC maids manually excluded from profile Email recipients separated by comma"),
                new Parameter(PARAMETER_MV_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_TITLE, PARAMETER_MV_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_TITLE,
                        "Check list of MV Maids excluded from payroll @payroll_month@", "MV maids manually excluded from profile Email title"),
                new Parameter(PARAMETER_MV_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_RECIPIENTS, PARAMETER_MV_MAIDS_MANUALLY_EXCLUDED_FROM_PROFILE_EMAIL_RECIPIENTS,
                        "", "MV maids manually excluded from profile Email recipients separated by comma"),
                new Parameter(PARAMETER_PAYDAY_CELEBRATION_EMAIL_SUBJECT, PARAMETER_PAYDAY_CELEBRATION_EMAIL_SUBJECT,
                        "@employee_first_name@! You just got paid by maids.cc", "subject of office staff payday celebration email"),
                new Parameter(PARAMETER_EMPLOYEES_REQUESTED_FOR_TERMINATION_EMAIL_SUBJECT, PARAMETER_EMPLOYEES_REQUESTED_FOR_TERMINATION_EMAIL_SUBJECT,
                        "Employees requested for termination on @date_of_today@", "subject of employees requested for termination email"),
                new Parameter(PARAMETER_EMPLOYEES_REQUESTED_FOR_TERMINATION_EMAIL_RECEIVERS, PARAMETER_EMPLOYEES_REQUESTED_FOR_TERMINATION_EMAIL_RECEIVERS,
                        "", "receivers emails of employees requested for termination email"),
                new Parameter(PARAMETER_HOUSEMAID_FORGIVENESS_DAYS_TO_LOOK_BACK, PARAMETER_HOUSEMAID_FORGIVENESS_DAYS_TO_LOOK_BACK,
                        "60", "the limit of days that we will use when getting the data for Unpaid days forgiveness screen"),
                new Parameter(PARAMETER_PAY_FIX_CONTRACT_RECIPIENT, PARAMETER_PAY_FIX_CONTRACT_RECIPIENT,
                        "", "receiver email in case the new worker salary is less than mohre + holiday "),
                new Parameter(PARAMETER_DUPLICATE_IBAN_DETECTION_RECIPIENT, PARAMETER_DUPLICATE_IBAN_DETECTION_RECIPIENT,
                        "","receivers in case of duplicate Iban"),
                new Parameter(PARAMETER_PAYROLL_AFTER_NEW_SALARY_EXCEPTION_RECIPIENT,
                        PARAMETER_PAYROLL_AFTER_NEW_SALARY_EXCEPTION_RECIPIENT,
                        "<EMAIL>", "recipient in case new salary is less than the basic salary"),
                //PAY-1733
                new Parameter(INITIAL_PAYROLL_FILES_RECIPIENT_PARAMETER,
                        INITIAL_PAYROLL_FILES_RECIPIENT_PARAMETER,
                "", "recipient of initial payroll files of payroll month"),
                new Parameter(PAYROLL_SEND_INITIAL_PAYROLL_FILES_TEMPLATE_SUBJECT,
                        PAYROLL_SEND_INITIAL_PAYROLL_FILES_TEMPLATE_SUBJECT,
                        "Initial Payroll Files of @employee_type1@ - @employee_type2@ - @payment_method@ - @payroll_type@ of @payroll_month@",
                        "Subject of send initial payroll files template"),
                new Parameter(PENDING_PAYROLL_ROSTER_APPROVAL_REMINDER_TEMPLATE_SUBJECT,
                        PENDING_PAYROLL_ROSTER_APPROVAL_REMINDER_TEMPLATE_SUBJECT,
                        "Alert - The final payroll generation process of @payroll_month@ has not started because of @employee_type@ payroll roster approval.",
                        "Subject of pending payroll roster approval reminder email"),
                new Parameter(PENDING_PAYROLL_ROSTER_APPROVAL_REMINDER_TEMPLATE_RECIPIENT,
                        PENDING_PAYROLL_ROSTER_APPROVAL_REMINDER_TEMPLATE_RECIPIENT,
                        "", "recipient of pending payroll roster approval reminder email"),
                new Parameter(PAYROLL_MOL_LIST_UPLOAD_FAILURE_ALERT_TEMPLATE_SUBJECT,
                        PAYROLL_MOL_LIST_UPLOAD_FAILURE_ALERT_TEMPLATE_SUBJECT,
                        "Alert - the system failed to upload a new MOL list before the final payroll generation",
                        "Subject of Payroll_Mol_List_Upload_Failure_Alert_Template"),
                new Parameter(PAYROLL_MOL_LIST_UPLOAD_FAILURE_ALERT_TEMPLATE_RECIPIENT,
                        PAYROLL_MOL_LIST_UPLOAD_FAILURE_ALERT_TEMPLATE_RECIPIENT,
                        "", "recipient of Payroll_Mol_List_Upload_Failure_Alert_Template"),
                new Parameter(PAYROLL_HAZARDOUS_CONDITIONS_ALERT_TEMPLATE_SUBJECT,
                        PAYROLL_HAZARDOUS_CONDITIONS_ALERT_TEMPLATE_SUBJECT,
                        "One or more hazardous conditions are met",
                        "Subject of Payroll_Hazardous_Conditions_Alert_Template"),
                new Parameter(PAYROLL_HAZARDOUS_CONDITIONS_ALERT_TEMPLATE_RECIPIENT,
                        PAYROLL_HAZARDOUS_CONDITIONS_ALERT_TEMPLATE_RECIPIENT,
                        "", "recipient of Payroll_Hazardous_Conditions_Alert_Template"),
                new Parameter(HOURS_BEFORE_PAYMENT_DATE_TO_CHECK_THE_EMPLOYEE_LIST_PARAM,
                        HOURS_BEFORE_PAYMENT_DATE_TO_CHECK_THE_EMPLOYEE_LIST_PARAM,
                        "5", "Number of hours before payment date to check employee list. - task PAY-1733"),
                new Parameter(MAX_EMPLOYEE_LIST_AGE_IN_HOUR_PARAM,
                        MAX_EMPLOYEE_LIST_AGE_IN_HOUR_PARAM,
                        "48", "Number of hours considered as max employee list age. - task PAY-1733"),
                new Parameter(PAYMENT_PROCESS_PERCENTAGE_PARAM,
                        PAYMENT_PROCESS_PERCENTAGE_PARAM,
                        "70", "Payment process percentage. - task PAY-1733"),
                new Parameter(CC_TOTAL_NET_SALARY_DIFFERENCE_FROM_PREVIOUS_MONTH_PARAM,
                        CC_TOTAL_NET_SALARY_DIFFERENCE_FROM_PREVIOUS_MONTH_PARAM,
                "500000", "Parameter for cc total net salary difference from previous month - task PAY-1733"),
                new Parameter(TOTAL_LOAN_REPAYMENT_PERCENTAGE_FROM_TOTAL_LOAN_BALANCE_PARAM,
                        TOTAL_LOAN_REPAYMENT_PERCENTAGE_FROM_TOTAL_LOAN_BALANCE_PARAM,
                        "20", "Parameter to check total loan repayment percentage from total loan balance - task PAY-1733"),
                new Parameter(MV_MAIDS_SALARY_PERCENTAGE_FROM_TOTAL_MV_PAYMENT_PARAM,
                        MV_MAIDS_SALARY_PERCENTAGE_FROM_TOTAL_MV_PAYMENT_PARAM,
                        "90", "Parameter for mv maids salary percentage from total mv payment - task PAY-1733"),
                new Parameter(GROUP_2_OR_6_EARNING_PERCENTAGE_FROM_GROUP_1_OR_5_EARNING_PARAM,
                        GROUP_2_OR_6_EARNING_PERCENTAGE_FROM_GROUP_1_OR_5_EARNING_PARAM,
                        "0.7", "Parameter for earning percentage - task PAY-1733"),
                new Parameter(RATIO_CC_MAIDS_WITHOUT_CLIENT_PARAM,
                        RATIO_CC_MAIDS_WITHOUT_CLIENT_PARAM,
                        "10", "Parameter for ratio cc maids without client - task PAY-1733"),

                new Parameter(TIME_FRAME_MONTHS_PARAM,
                        TIME_FRAME_MONTHS_PARAM,
                        "20", "Parameter has the value of time frame months for Invalid Airfare Ticket Housemaid Exclusion Rule - PAY-1734"),

                new Parameter(CC_NEW_MULTIPLIER_PARAM,
                        CC_NEW_MULTIPLIER_PARAM,
                        "2", "Parameter has the value of CC new multiplier for Net Payout Violation Housemaid Exclusion Rule - PAY-1734"),

                new Parameter(CC_RENEWAL_MULTIPLIER_PARAM,
                        CC_RENEWAL_MULTIPLIER_PARAM,
                        "2.5", "Parameter has the value of CC renewal multiplier for Net Payout Violation Housemaid Exclusion Rule - PAY-1734"),

                new Parameter(MV_MULTIPLIER_PARAM,
                        MV_MULTIPLIER_PARAM,
                        "2", "Parameter has the value of MV multiplier for Net Payout Violation Housemaid Exclusion Rule - PAY-1734"),

                new Parameter(TIME_INTERVAL_PARAM,
                        TIME_INTERVAL_PARAM,
                        "10", "Parameter has the value of time interval for CC maids with Duplicate Additions Housemaid Exclusion Rule - PAY-1734"),

                new Parameter(TOTAL_RAFFLE_PRIZE_LIMIT_PARAM,
                        TOTAL_RAFFLE_PRIZE_LIMIT_PARAM,
                        "17000", "Parameter has the value of Total Raffle Prize Limit for Raffle Prize Limit Violations Housemaid Exclusion Rule - PAY-1734"),

                new Parameter(STATUSES_ELIGIBLE_FOR_PAYROLL_PARAM,
                        STATUSES_ELIGIBLE_FOR_PAYROLL_PARAM,
                        "WITH_CLIENT;VIP_RESERVATIONS;AVAILABLE;RESERVED_FOR_PROSPECT;RESERVED_FOR_REPLACEMENT;SICK_WITHOUT_CLIENT;LANDED_IN_DUBAI;PENDING_FOR_VIDEOSHOOT;RESERVED_HOME_VISIT;ASSIGNED_OFFICE_WORK", "Parameter has the value of statuses eligible for payroll for CC Housemaids with Not-Eligible for payroll Status Housemaid Exclusion Rule - PAY-1734"),

                new Parameter(OVERSEAS_EMPLOYEE_MAX_SAME_RECIPIENT_PARAM,
                        OVERSEAS_EMPLOYEE_MAX_SAME_RECIPIENT_PARAM,
                        "5", "Parameter has the value of overseas employee max same recipient for Overseas Money receiver Audit Office Staff Exclusion Rule - PAY-1734"),


                new Parameter(PAYROLL_EMPLOYEE_LIST_RBA_CHECK_TEMPLATE_SUBJECT,
                        PAYROLL_EMPLOYEE_LIST_RBA_CHECK_TEMPLATE_SUBJECT,
                        "Urgent - Please check Employee List RBA has worked successfully",
                        "Subject of Payroll_Employee_List_RBA_Check_Template"),
                new Parameter(PAYROLL_EMPLOYEE_LIST_RBA_CHECK_TEMPLATE_RECIPIENT,
                        PAYROLL_EMPLOYEE_LIST_RBA_CHECK_TEMPLATE_RECIPIENT,
                        "", "recipient of Payroll_Employee_List_RBA_Check_Template"),
                new Parameter(PAYROLL_PAYMENT_TODO_NOTIFICATION_EMAIL, PAYROLL_PAYMENT_TODO_NOTIFICATION_EMAIL,
                        "", "receivers email of payroll accountant todos notifications"),
                new Parameter(PARAMETER_SYRIAN_LOW_EXCHANGE_RATE_PERCENTAGE, PARAMETER_SYRIAN_LOW_EXCHANGE_RATE_PERCENTAGE,
                        "5", "low exchange rate percentage for the syrian staffs who receiving their salaries through Al Haram center"),
                new Parameter(PARAMETER_ROSTER_REQUESTS_ARE_APPROVED_EMAIL_SUBJECT, PARAMETER_ROSTER_REQUESTS_ARE_APPROVED_EMAIL_SUBJECT,
                        "All managers approved their rosters", "subject for All managers approved their rosters email"),
                new Parameter(PARAMETER_ROSTER_REQUESTS_ARE_APPROVED_EMAIL_RECEIVERS, PARAMETER_ROSTER_REQUESTS_ARE_APPROVED_EMAIL_RECEIVERS,
                        "", "receivers emails for All managers approved their rosters email"),
                new Parameter(EMIRATES_ROSTER_APPROVAL_EMAIL_SUBJECT, EMIRATES_ROSTER_APPROVAL_EMAIL_SUBJECT,
                        "The Emiratis roster is approved", "subject for The Emiratis roster is approved email"),
                new Parameter(EMIRATES_ROSTER_APPROVAL_EMAIL_RECEIVERS, EMIRATES_ROSTER_APPROVAL_EMAIL_RECEIVERS,
                        "", "receivers emails for The Emiratis roster is approved email"),
                new Parameter(PARAMETER_HOUSEMAID_PRIMARY_PAYMENT_DATE_VALIDATION_DAY, PARAMETER_HOUSEMAID_PRIMARY_PAYMENT_DATE_VALIDATION_DAY,
                        "27", "the day which we can't edit the housemaid primary payment date before it"),
                new Parameter(PARAMETER_PAYROLL_AUTOMATICALLY_EMPLOYEE_ACCESSES, PARAMETER_PAYROLL_AUTOMATICALLY_EMPLOYEE_ACCESSES,
                        "", "All employee accessed that should be automatically added to the user's access once his name is added to the employee access table"),
                new Parameter(PARAMETER_EXCLUDING_OVERLAP_MAIDS_DAYS_COUNT, PARAMETER_EXCLUDING_OVERLAP_MAIDS_DAYS_COUNT,
                        "7", "the number of days that we want to exclude the maid for being overlap within"),
                new Parameter(PARAMETER_STARTING_YEAR_FOR_MAID_IN_PAYROLL_EXCEPTION_REPORT, PARAMETER_STARTING_YEAR_FOR_MAID_IN_PAYROLL_EXCEPTION_REPORT,
                        "2022","it used in payroll exception report Table 1 to check from housemaids salaries after this parameter"),
                new Parameter(PARAMETER_ADDITION_LIMIT_FOR_MAID_IN_PAYROLL_EXCEPTION_REPORT, PARAMETER_ADDITION_LIMIT_FOR_MAID_IN_PAYROLL_EXCEPTION_REPORT,
                        "500.0","it used in payroll exception report Table 2 to check from housemaids addition more than this parameter"),
                new Parameter(PARAMETER_PAYROLL_CC_SWITCHING_TO_MV_DEPLOYMENT_PAYROLL_MONTH, PARAMETER_PAYROLL_CC_SWITCHING_TO_MV_DEPLOYMENT_PAYROLL_MONTH,
                        "2023-01-01", "the payroll month that the task PAY-1014 will be released, in order to consider the last month starting from 1st of month not from audit closing date"),
                new Parameter(PARAMETER_MAX_ADDITION_VALUE_TO_MAID_AFTER_SWITCH_TO_MV,PARAMETER_MAX_ADDITION_VALUE_TO_MAID_AFTER_SWITCH_TO_MV,
                        "5000",PARAMETER_MAX_ADDITION_VALUE_TO_MAID_AFTER_SWITCH_TO_MV),
                new Parameter(PARAMETER_BIRTHDAY_REMINDER_JAD_EMAIL, PARAMETER_BIRTHDAY_REMINDER_JAD_EMAIL,
                        "", "Jad Email Address when sending birthday reminder sms or emails"),
                new Parameter(PARAMETER_FINAL_SETTLEMENT_DOCUMENTS_NAMES_FOR_EMIRATES, PARAMETER_FINAL_SETTLEMENT_DOCUMENTS_NAMES_FOR_EMIRATES,
                        "Final Settlement;Cancel paper;Pension Cancel Employee","FINAL SETTLEMENT DOCUMENTS NAMES FOR EMIRATES"),
                new Parameter(PARAMETER_FINAL_SETTLEMENT_DOCUMENTS_NAMES_FOR_EXPATS, PARAMETER_FINAL_SETTLEMENT_DOCUMENTS_NAMES_FOR_EXPATS,
                        "Final Settlement;Cancel paper","FINAL SETTLEMENT DOCUMENTS NAMES FOR EXPATS"),
                new Parameter(PARAMETER_RECRUITMENT_MANAGER_EMAIL, PARAMETER_RECRUITMENT_MANAGER_EMAIL,
                        "","it used to send email to recruitment manager after deletion office staff todo with type Hire employee"),
                new Parameter(PARAMETER_PAYROLL_REMINDER_EMAIL_RECIPIENTS,
                        PARAMETER_PAYROLL_REMINDER_EMAIL_RECIPIENTS,
                        "<EMAIL>, <EMAIL>", ""),
                new Parameter(PARAMETER_PAYROLL_PRIMARY_REMINDER_CC_RECIPIENTS,
                        PARAMETER_PAYROLL_PRIMARY_REMINDER_CC_RECIPIENTS,
                        "<EMAIL>, <EMAIL>", ""),
                new Parameter(PARAMETER_PAYROLL_SECONDARY_REMINDER_CC_RECIPIENTS,
                        PARAMETER_PAYROLL_SECONDARY_REMINDER_CC_RECIPIENTS,
                        "<EMAIL>, <EMAIL>", ""),
                new Parameter(PARAMETER_PRIMARY_PAYROLL_REMINDER_SUBJECT,
                        PARAMETER_PRIMARY_PAYROLL_REMINDER_SUBJECT,
                        "Attention! Postpone Today’s Deployment", ""),
                new Parameter(PARAMETER_SECONDARY_PAYROLL_REMINDER_SUBJECT,
                        PARAMETER_SECONDARY_PAYROLL_REMINDER_SUBJECT,
                        "Attention! Consider the Payroll Generation in tonight’s deployments", ""),
                new Parameter(PARAMETER_CHUNK_SIZE_FOR_BGT,
                        PARAMETER_CHUNK_SIZE_FOR_BGT, "100", ""),
                new Parameter(PARAMETER_PAY_LOAN_REPAYMENT_APPROVAL_EMAIL_RECIPIENTS, PARAMETER_PAY_LOAN_REPAYMENT_APPROVAL_EMAIL_RECIPIENTS,
                        "770", "approve pay loan"),
                new Parameter(PARAMETER_PAY_LOAN_REPAYMENT_APPROVAL_USER_ID_RECIPIENTS, PARAMETER_PAY_LOAN_REPAYMENT_APPROVAL_USER_ID_RECIPIENTS,
                        "", "approve pay loan"),
                new Parameter(PARAMETER_PAYROLL_ROSTER_FILE_COLUMN_WIDTHS, PARAMETER_PAYROLL_ROSTER_FILE_COLUMN_WIDTHS,
                        "113,100,64,84,62,80,53,85,91,82,89,88,94,90,70,81,62,79,103,111,119"
                        , PARAMETER_PAYROLL_ROSTER_FILE_COLUMN_WIDTHS),
                new Parameter(PARAMETER_MAID_WHATSAPP_HELPLINE, PARAMETER_MAID_WHATSAPP_HELPLINE,
                        "https://api.whatsapp.com/send/?phone=971506715943&text&type=phone_number&app_absent=0", ""),
                new Parameter(PARAMETER_MARK_PAYROLL_UNPAID_EMAIL_RECIPIENTS,
                        PARAMETER_MARK_PAYROLL_UNPAID_EMAIL_RECIPIENTS,
                        "",
                        "Mark payroll as unpaid recipient."),
                new Parameter(PARAMETER_MARK_PAYROLL_UNPAID_USER_ID_RECIPIENTS,
                        PARAMETER_MARK_PAYROLL_UNPAID_USER_ID_RECIPIENTS,
                        "",
                        "Mark payroll as unpaid user id recipient."),
                new Parameter(PARAMETER_MARK_PAYROLL_UNPAID_EMAIL_SUBJECT,
                        PARAMETER_MARK_PAYROLL_UNPAID_EMAIL_SUBJECT,
                        "@employee_name@ salary of @payroll_date@ was not paid",
                        "Mark payroll as unpaid subject."),
                new Parameter(PARAMETER_PAYROLL_ANSARI_MAIDS_REJECTED_SALARIES_EMAIL,
                        PARAMETER_PAYROLL_ANSARI_MAIDS_REJECTED_SALARIES_EMAIL,
                        "<EMAIL>", ""),
                new Parameter(PARAMETER_PAYROLL_MAIDS_REJECTED_SALARIES_EMAIL_RECIPIENTS,
                        PARAMETER_PAYROLL_MAIDS_REJECTED_SALARIES_EMAIL_RECIPIENTS,
                        "<EMAIL>;",
                        ""),
                new Parameter(PARAMETER_PAYROLL_MAIDS_REJECTED_SALARIES_CC_EMAIL_RECIPIENTS,
                        PARAMETER_PAYROLL_MAIDS_REJECTED_SALARIES_CC_EMAIL_RECIPIENTS,
                        "<EMAIL>;",
                        ""),
                new Parameter(PARAMETER_PAYROLL_SALARY_RULES_REPORT_RECIPIENTS, PARAMETER_PAYROLL_SALARY_RULES_REPORT_RECIPIENTS,
                        "", PARAMETER_PAYROLL_SALARY_RULES_REPORT_RECIPIENTS),
                new Parameter(PARAMETER_PAYROLL_NUMBER_OF_DAYS_TO_CREATE_ATTENDANCE_LOG, PARAMETER_PAYROLL_NUMBER_OF_DAYS_TO_CREATE_ATTENDANCE_LOG
                        , "30", PARAMETER_PAYROLL_NUMBER_OF_DAYS_TO_CREATE_ATTENDANCE_LOG),
                new Parameter(PARAMETER_NEGATIVE_SALARY_COMPONENT_RECIPIENTS, PARAMETER_NEGATIVE_SALARY_COMPONENT_RECIPIENTS,
                        "", PARAMETER_NEGATIVE_SALARY_COMPONENT_RECIPIENTS),
                new Parameter(PARAMETER_PAYROLL_ASSIGNED_OFFICE_WORK_ADDITIONS_EMAIL_RECIPIENTS, PARAMETER_PAYROLL_ASSIGNED_OFFICE_WORK_ADDITIONS_EMAIL_RECIPIENTS,
                        "", PARAMETER_PAYROLL_ASSIGNED_OFFICE_WORK_ADDITIONS_EMAIL_RECIPIENTS),
                new Parameter(PARAMETER_PAYROLL_GENERATION_CHUNK_SIZE, PARAMETER_PAYROLL_GENERATION_CHUNK_SIZE,
                        "30", PARAMETER_PAYROLL_GENERATION_CHUNK_SIZE),
                new Parameter(PARAMETER_ZOHO_HR_BASE_API,
                        PARAMETER_ZOHO_HR_BASE_API,
                        "https://recruit.zoho.com/recruit/v2/",
                        "ZOHO HR Base API"),
                new Parameter(PARAMETER_PAYROLL_ANSARI_CHARGES_RATE, PARAMETER_PAYROLL_ANSARI_CHARGES_RATE,
                        "1", PARAMETER_PAYROLL_ANSARI_CHARGES_RATE),
                new Parameter(PARAMETER_PAYROLL_EXCEPTION_NOTIFICATION_EMAILS, PARAMETER_PAYROLL_EXCEPTION_NOTIFICATION_EMAILS,
                        "<EMAIL>", PARAMETER_PAYROLL_EXCEPTION_NOTIFICATION_EMAILS),
                new Parameter(PARAMETER_ACCESS_LINK_USER_ID_RECIPIENTS, PARAMETER_ACCESS_LINK_USER_ID_RECIPIENTS,
                        "", PARAMETER_ACCESS_LINK_USER_ID_RECIPIENTS),
                new Parameter(PARAMETER_PAYROLL_SEND_PAYSLIP_CHUNK_SIZE, PARAMETER_PAYROLL_SEND_PAYSLIP_CHUNK_SIZE,
                        "10", PARAMETER_PAYROLL_SEND_PAYSLIP_CHUNK_SIZE),
                new Parameter(PARAMETER_PAYROLL_NO_SHOW_THRESHOLD, PARAMETER_PAYROLL_NO_SHOW_THRESHOLD,
                        "12", PARAMETER_PAYROLL_NO_SHOW_THRESHOLD),
                new Parameter(PARAMETER_PAYROLL_MULTIPLE_PROFILES_EMAIL_RECIPIENTS, PARAMETER_PAYROLL_MULTIPLE_PROFILES_EMAIL_RECIPIENTS,
                        "", PARAMETER_PAYROLL_MULTIPLE_PROFILES_EMAIL_RECIPIENTS),
                new Parameter(PARAMETER_PAYROLL_PROCESS_OFFICE_STAFF_PAYROLL_CHUNK_SIZE, PARAMETER_PAYROLL_PROCESS_OFFICE_STAFF_PAYROLL_CHUNK_SIZE,
                        "50", PARAMETER_PAYROLL_PROCESS_OFFICE_STAFF_PAYROLL_CHUNK_SIZE),
                new Parameter(PARAMETER_PAYROLL_PROCESS_HOUSEMAID_PAYROLL_CHUNK_SIZE, PARAMETER_PAYROLL_PROCESS_HOUSEMAID_PAYROLL_CHUNK_SIZE,
                        "20", PARAMETER_PAYROLL_PROCESS_HOUSEMAID_PAYROLL_CHUNK_SIZE),
                new Parameter(PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EMIRATI, PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EMIRATI,
                        "24", "Emarati Roster Generation Day of month"),
                new Parameter(PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS, PARAMETER_DAY_TO_STOP_GENERATING_SALARIES_FOR_EXPAT_AND_OVERSEAS,
                        "25", "Expat and overseas Roster Generation Day of month"),
                new Parameter(PARAMETER_PAYROLL_EMARATI_PENSION_RECIPIENTS, PARAMETER_PAYROLL_EMARATI_PENSION_RECIPIENTS,
                        "", ""),
                new Parameter(PARAMETER_PAYROLL_INVALID_PENSION_DEDUCTION_RECIPIENTS, PARAMETER_PAYROLL_INVALID_PENSION_DEDUCTION_RECIPIENTS,
                        "", ""),
                new Parameter(PARAMETER_PAYROLL_DIFFERENCE_BETWEEN_PENSION_AMOUNT_AND_CONTRIBUTION_RECIPIENTS, PARAMETER_PAYROLL_DIFFERENCE_BETWEEN_PENSION_AMOUNT_AND_CONTRIBUTION_RECIPIENTS,
                        "", ""),
                new Parameter(PARAMETER_PAYROLL_PENSION_DEDUCTION_PERCENTAGE_ON_OR_BEFORE_31_10_2023, PARAMETER_PAYROLL_PENSION_DEDUCTION_PERCENTAGE_ON_OR_BEFORE_31_10_2023,
                        "5.0", "The pension deduction percentage for Emirates staff whose start date is on or before November 31, 2023 (start date <= 31/11/2023)"),
                new Parameter(PARAMETER_PAYROLL_PENSION_DEDUCTION_PERCENTAGE_AFTER_31_10_2023, PARAMETER_PAYROLL_PENSION_DEDUCTION_PERCENTAGE_AFTER_31_10_2023,
                        "11.0", "The pension deduction percentage for Emirates staff whose start date is after November 31, 2023 (start date > 31/11/2023)"),
                new Parameter(PARAMETER_PAYROLL_PENSION_AUTHORITY_PERCENTAGE_ON_OR_BEFORE_31_10_2023, PARAMETER_PAYROLL_PENSION_AUTHORITY_PERCENTAGE_ON_OR_BEFORE_31_10_2023,
                        "17.5", "The pension authority percentage for Emirates staff whose start date is on or before November 31, 2023 (start date <= 31/11/2023)"),
                new Parameter(PARAMETER_PAYROLL_PENSION_AUTHORITY_PERCENTAGE_AFTER_31_10_2023, PARAMETER_PAYROLL_PENSION_AUTHORITY_PERCENTAGE_AFTER_31_10_2023,
                        "23.5", "The pension authority percentage for Emirates staff whose start date is after November 31, 2023 (start date > 31/11/2023)"),
                new Parameter(PARAMETER_PAYROLL_STAFF_UPDATE_OF_BANK_INFO_HISTORY_POSITION_NAME, PARAMETER_PAYROLL_STAFF_UPDATE_OF_BANK_INFO_HISTORY_POSITION_NAME,
                        "", PARAMETER_PAYROLL_STAFF_UPDATE_OF_BANK_INFO_HISTORY_POSITION_NAME)
                );
    }

    @Override
    public List<Picklist> getCustomPicklists() {
        return Arrays.asList(
                new Picklist(PICKLIST_JOB_TITLE,
                        "Job Titles",
                        new String[]{}
                ),
                new Picklist(
                        PICKLIST_UNPAID_DAYS_FORGIVENESS_TYPES,
                        "Unpaid Days Forgiveness Types",
                        new String[]{
                                PICKLIST_ITEM_MAID_WAS_WITH_CLIENT,
                                PICKLIST_ITEM_MAID_WAS_IN_ACCOMMODATION
                        }),
                new Picklist(
                        PICKLIST_HOUSEMAID_PURPOSE_FOR_FORGIVENESS_DEDUCTION,
                        "Forgiveness Deduction Purposes",
                        new String[]{
                                PICKLIST_ITEM_MAID_WAS_WITH_CLIENT_PURPOSE,
                                PICKLIST_ITEM_MAID_WAS_IN_ACCOMMODATION_PURPOSE
                        })
        );
    }

    @Override
 
    public com.magnamedia.core.type.ModuleType getModuleType() {
        return com.magnamedia.core.type.ModuleType.BusinessLogic;
    }   @Override
    public String getName() { 
        return "Payroll Management Module";
    }

    @Override
    public String getCode() {
        return "payroll";
    }

    @Override
    public void setup() {
        // TODO: ADD SETUP CODE

        try {
            MessageTemplateService messageTemplateService = Setup.getApplicationContext().getBean(MessageTemplateService.class);
            //get all messages that will be used from MessageTemplateService in order to define them on the Messaging System
            List<Template> messageTemplates = messageTemplateService.getMessageTemplates();
            for (Template template : messageTemplates) {
                createTemplate(template.getName(), template.getSpecs(), template.getText(), template.isUnicode());
            }

            //generate all Payroll email templates
            EmailTemplateService emailTemplateService = Setup.getApplicationContext().getBean(EmailTemplateService.class);
            emailTemplateService.createPayrollEmailTemplates();
            // generate emails templates
            emailTemplateService.createEmailTemplates();

            // create default payment rule configurations
            Setup.getApplicationContext().getBean(MonthlyPaymentRuleService.class)
                    .createDefaultPaymentRuleConfigurations();

            //create default AuditSetup record
            Setup.getApplicationContext().getBean(PayrollAuditTodoService.class)
                    .createDefaultAuditSetupRecord();
        } catch (Exception ex) {
            Logger.getLogger("Setup").log(Level.SEVERE, "Error while setup payroll module", ex);
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while setup payroll module", false);
        }
    }

    @Override
    public String getVersion() {
        return "1.0.0";
    }

    @Override
    public List<JobDefinition> getCustomJobDefinitions() {
        return Arrays.asList(
                new JobDefinition("Changing Maids Salary on Renewal", "ChangingMaidsSalaryOnRenewalSchedualedJob", ChangingMaidsSalaryOnRenewalSchedualedJob.class, null),
                new JobDefinition("Client Replacement Deduction", "ClientReplacementDeductionSchedualedJob", ClientReplacementDeductionSchedualedJob.class, null),
                new JobDefinition("Converting MaidsCC to Maid Visa Changes", "ConvertingMaidsCCToMaidVisaChangesJob", ConvertingMaidsCCToMaidVisaChangesJob.class, null),
                new JobDefinition("Housemaid Rules", "HousemaidRulesScheduledJob", HousemaidRulesScheduledJob.class, null),
                new JobDefinition("Housemaid Start Date Correct", "HousemaidStartDateCorrectSchedualedJob", HousemaidStartDateCorrectSchedualedJob.class, null),
                new JobDefinition("NoKids Deduction", "NoKidsDeductionSchedualedJob", NoKidsDeductionSchedualedJob.class, null),
                new JobDefinition("Payslips", "PayslipsScheduledJob", PayslipsScheduledJob.class, null),
                new JobDefinition("Pension Compensation Local Employee Deduction", "PensionCompensationLocalEmployeeDeductionJob", PensionCompensationLocalEmployeeDeductionJob.class, null),
                new JobDefinition("Weekly OfficeStaff Payroll Report", "WeeklyOfficeStaffPayrollReportScheduledTask", WeeklyOfficeStaffPayrollReportScheduledTask.class, null),
                new JobDefinition("Total Loans", "TotalLoansScheduledJob", TotalLoansScheduledJob.class, null),
                new JobDefinition("Vacation Allowance", "VacationAllowanceScheduledJob", VacationAllowanceScheduledJob.class, null),
                new JobDefinition("Payroll Data Correction and Integrity", "PayrollDataCorrectionandIntegrityScheduledJob", DataCorrectionandIntegrityScheduledJob.class, null),
                new JobDefinition("Fetch New Hired Employees From JazzHR Job", "Fetch_New_Hired_Employees_From_JazzHR", FetchNewHiredEmployeesFromJazzHRJob.class, null),
                new JobDefinition("Update Office Staff Salary Based On Manager Note Job", "update_officeStaff_salary_based_on_manager_note_job", UpdateOfficeStaffSalaryBasedOnManagerNoteJob.class, null),
                new JobDefinition("Change Office Staff Status On Termination Date", "change_officeStaff_status_on_termination_date", ChangeOfficeStaffStatusOnTerminationDate.class, null),
                new JobDefinition("Remind the CFO to approve final settlements", "remind_cfo_to_approve_final_settlement", RemindCFOToApproveFinalSettlementJob.class, null),
//                new JobDefinition("Remind access handlers to revoke accesses", "remind_access_handler_to_revoke_access", RemindAccessHandlerToRevokeAccess.class, null),
               // new JobDefinition("Maid Visa Salary Hold Job", "Maid_Visa_Salary_Hold", MaidVisaSalaryHoldJob.class, null),
                new JobDefinition("Generate All Monthly Payroll Rules", "generate_monthly_payroll_rules", MonthlyPaymentRulesJob.class, null),
                new JobDefinition("Generate Payroll Accountant To-do List", "generate_payroll_accountant_todo_list", PayrollAccountantToDoJob.class, null),
                new JobDefinition("Create Pension Authority Accountant ToDo", "create_pension_authority_accountant_todo", CreatePensionAuthorityAccountantToDo.class, null),
                new JobDefinition("Payroll Roster Approval Job", "Payroll_Roster_Approval_Job", PayrollRosterApprovalJob.class, null),
                new JobDefinition("Payroll Roster Approval Job For Emarati", "Payroll_Roster_Approval_Job_For_Emarati", PayrollRosterApprovalJobForEmarati.class, null),
                new JobDefinition("Generate Payroll Audit To-do List", "generate_payroll_audit_todo_list", PayrollAuditTodoJob.class, null),
//                new JobDefinition("Send all Revoke Access mails to managers and final managers every day", "send_revoke_access_mails_to_managers_and_final_managers", RevokeAccessNotifyMailJob.class, null),
                new JobDefinition("Send Pending Approval Requests SMS To Managers", "send_pending_approval_requests_sms", PendingApprovalRequestsJob.class, null),
                new JobDefinition("remind the final manager to enter his notes on the final settlement before sending it to CFO", "final_settlement_reminders_for_final_managers", FinalSettlementReminderJob.class, null),
                new JobDefinition("Syria Transfers Exchange Rate Job", "Syria_Transfers_Exchange_Rate_Job", SyriaTransfersExchangeRateJob.class, null),
                new JobDefinition("Send an email notification for the Auditor to remind him to close his to-dos", "send_reminder_to_editors_to_close_the_audit_todos", PayrollAuditTodoReminderJob.class, null),
                new JobDefinition("Send an email to notify managers asking them to close the accountant todo", "send_reminder_to_managers_to_close_the_accountant_todos", PayrollAccountantTodoReminderJob.class, null),
                new JobDefinition("Send reminder emails to trustee and manager to react to roster files", "send_payroll_roster_approve_requests_reminders", PayrollRosterApproveRequestReminderJob.class, null),
                new JobDefinition("Send auditor actions job", "send_auditor_actions_job", AuditorActionsJob.class, null),
                new JobDefinition("Salary Rules Over Time Job", "Salary_Rules_Over_Time_Job", SalaryRulesOverTimeJob.class, null),
                new JobDefinition("Office Staff Birthday Reminder", "office_staff_birthday_reminder", OfficeStaffBirthDateReminderJob.class, null),
                new JobDefinition("Employees Requested For Termination Job", "employees_requested_for_termination_job", EmployeesRequestedForTerminationJob.class, null),
                new JobDefinition("Pay Housemaid Final Settlements Job", "pay_housemaid_final_settlements_job", PayHousemaidsFinalSettlementsJob.class, null),
                new JobDefinition("Housemaid Unpaid Day Job", "housemaid_unpaid_day_job", HousemaidUnpaidDayJob.class, null),
                new JobDefinition("Syrian International Transfer Addition Job", "syrian_international_transfer_addition_job", SyrianInternationalTransferAdditionJob.class, null),
                new JobDefinition("Payroll Mv App Notifications Job", "payroll_mv_app_notifications_job", PayrollMvAppNotificationsJob.class, null),
                new JobDefinition("Office Staff Visa Cancellation After Termination Job","office_staff_visa_cancellation_after_termination_job",OfficeStaffVisaCancellationAfterTerminationJob.class,null),
                new JobDefinition("Bank Transfer Report Job", "bank_transfer_report_job", BankTransferReportJob.class, null),
                new JobDefinition("Payroll Audit Reminder Job", "payroll_audit_reminder_job", PayrollAuditReminderJob.class, null),
                new JobDefinition("Salary Start Date Job", "salary_start_date_job", SalaryStartDateJob.class, null),
                new JobDefinition("Bank Transfer Report Job", "bank_transfer_report_job", BankTransferReportJob.class, null),
                new JobDefinition("Maids Rejected Salaries", "maids_rejected_salaries", MaidRejectedSalaryJob.class, null),
                new JobDefinition("Housemaid Replacement Salary Start Date Job", "housemaid_replacement_salary_start_date_job", HousemaidFillReplacementSalaryStartDateJob.class, null),
                new JobDefinition("Assigned Office Work Additions Job", "assigned_office_work_additions_job", AssignedOfficeWorkAdditionsConfirmationJob.class, null),
                new JobDefinition("Replacement Salary Start Date Job", "replacement_salary_start_date_job", ReplacementSalaryStartDateJob.class, null),
                new JobDefinition("Pension Deduction For Dubai Staff Emirati Job", "pension_deduction_for_dubai_staff_emirati_job", PensionDeductionForDubaiStaffEmiratiJob.class, null),
                new JobDefinition("Adding Prorated Salary For Office Staff Job", "adding_prorated_salary_for_office_staff_job", AddingProratedSalaryForOfficeStaffJob.class, null),
                new JobDefinition("Fetch New Hired Employees From ZOHO HR Job", "Fetch_New_Hired_Employees_From_ZOHO_HR", FetchNewHiredEmployeesFromZohoHRJob.class, null),
                new JobDefinition("Check Employee List Before Payment Date Job", "check_employee_list_before_payment_date_job", CheckEmployeeListBeforePaymentDateJob.class, null)
        );
    }


    @Override
    public List<GmailInbox> getMailInboxes() {
        return Arrays.asList(new GmailInbox(
                Setup.getParameter(Setup.getCurrentModule(), PARAMETER_PAYROLL_CORE_EMAIL_ADDRESS),
                Setup.getParameter(Setup.getCurrentModule(), PARAMETER_PAYROLL_CORE_EMAIL_PASSWORD),
                null,
                new SyrianExchangeRate()),
                new GmailInbox(
                        Setup.getParameter(Setup.getCurrentModule(), PARAMETER_PAYROLL_CORE_EMAIL_ADDRESS),
                        Setup.getParameter(Setup.getCurrentModule(), PARAMETER_PAYROLL_CORE_EMAIL_PASSWORD),
                        null,
                        new ProcessingMaidsRejectedSalaries()));
    }
}
