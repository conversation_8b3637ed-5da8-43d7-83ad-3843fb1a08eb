package com.magnamedia.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.HousemaidPayrollBean;
import com.magnamedia.entity.OfficeStaffPayrollBean;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.YayapotApisHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.MonthlyPayrollDocumentType;
import com.magnamedia.module.type.OfficeStaffStatus;
import com.magnamedia.module.type.OfficeStaffType;
import com.magnamedia.repository.HousemaidRepository;
import com.magnamedia.repository.MonthlyPayrollDocumentRepository;
import com.magnamedia.service.ScheduledMonthlyService;
import com.opencsv.CSVWriter;
import com.opencsv.bean.BeanToCsv;
import com.opencsv.bean.ColumnPositionMappingStrategy;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URISyntaxException;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.magnamedia.controller.OfficestaffPayrollController.setOfficeStaffPayrollBeanValues;

/**
 *
 * <AUTHOR> Abbas <<EMAIL>>
 */
@RequestMapping("/pwo")
@RestController
public class PaymentWorkOrderController extends BaseRepositoryController<Housemaid> {

    @Autowired
    private HousemaidRepository housemaidRep;

    @Autowired
    private HousemaidPayrollController housemaidPayrollController;

    @Autowired
    private PicklistRepository picklistRepository;

    @Autowired
    private YayapotApisHelper apisHelper;

    @Autowired
    private MonthlyPayrollDocumentRepository monthlyPayrollDocumentRepository;

    @Autowired
    private ScheduledMonthlyService scheduledMonthlyService;

    @Override
    public BaseRepository<Housemaid> getRepository() {
        return housemaidRep;
    }

    public List<String> localReceivers = Arrays.asList(
            "Lama Labbad",
            "Nojoud Nofal Sneij",
            "Samer Fateh Dofesh",
            "Youmna wasel salloum",
            "Judi Anne Angeles Torres"
    );

    Map<String, String> localReceiversTransferDistination = new HashMap<String, String>() {
        {
            put("Lama Labbad", "lama_labbad");
            put("Nojoud Nofal Sneij", "nojoud_nofal_sneij");
            put("Samer Fateh Dofesh", "samer_fateh_dofesh");
            put("Youmna wasel salloum", "youmna_wasel_salloum");
            put("Judi Anne Angeles Torres", "judi_anne_angeles_torres");

        }
    };

    //***********************Housemaids Payment Work Orders ***********************************
    @PreAuthorize("hasPermission('pwo','housemaidPWO')")
    @RequestMapping("/housemaidPWO")
    public ResponseEntity<?> housemaidPWO(HttpServletResponse response,
            @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(required = true, value = "sentByEmail") boolean isSentByEmail, @RequestParam(required = false, value = "finalFile") boolean finalFile) throws FileNotFoundException, IOException, URISyntaxException, InterruptedException, NoSuchAlgorithmException {
        
        Thread one = new Thread() {
            public void run() {
                try {
                    //Jirra ACC-1085
                    boolean withJop = true;
                    LocalDate dt;
                    if (date != null) {
                        dt = new LocalDate(date).withDayOfMonth(26);
                    } else {
                        dt = new LocalDate().withDayOfMonth(26);
                    }
                    List<MonthlyPayrollDocument> payrollfiles = monthlyPayrollDocumentRepository.
                            findByPayrollDateAndFinalFileAndTypeOrderByCreationDateDesc(new java.sql.Date(dt.toDate().getTime()), true, MonthlyPayrollDocumentType.MONTHLY_PAYROLL);
                    List<MonthlyPayrollDocument> pwofiles = monthlyPayrollDocumentRepository.
                            findByPayrollDateAndFinalFileAndTypeOrderByCreationDateDesc(new java.sql.Date(dt.toDate().getTime()), true, MonthlyPayrollDocumentType.PPWO);
                    if (payrollfiles.size() > 0 || pwofiles.size() > 0) {
                        withJop = false;
                    }
                    createAndDownloadFiles(response, date, isSentByEmail, finalFile, withJop);
                } catch (FileNotFoundException | NoSuchAlgorithmException ex) {
                    Logger.getLogger(PaymentWorkOrderController.class.getName()).log(Level.SEVERE, null, ex);
                }
            }

        };
        one.start();

        if (isSentByEmail) {
            return new ResponseEntity<>("Your request was registered, the file will be sent by email shortly", HttpStatus.OK);
        }
        TimeUnit.SECONDS.sleep(5);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    public void createAndDownloadFiles(
            HttpServletResponse response,
            Date date, boolean isSentByEmail, boolean finalFile, boolean withJop)
            throws FileNotFoundException, NoSuchAlgorithmException {
        //Payroll start and end date
        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }
        LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(dt);
        LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);
        LocalDate firstDayThisMonth = payrollEnd.dayOfMonth().withMinimumValue();
        LocalDate lastDayThisMonth = payrollEnd.dayOfMonth().withMaximumValue();
        System.out.println("First day " + firstDayThisMonth);
        System.out.println("Last day " + lastDayThisMonth);

        System.out.println(payrollStart);
        System.out.println(payrollEnd);

        List<HousemaidPayrollBean> result = new ArrayList<>();
        Double totalSalaries = 0.0;
        //Jirra ACC-322 ACC-645
        totalSalaries = housemaidPayrollController.generateHousemaidsPayrollList(totalSalaries, payrollStart, payrollEnd, null, result, false, finalFile, withJop, false);

        Date todayDate = new Date();

        HousemaidPayrollBean bean1 = new HousemaidPayrollBean();
        bean1.setSn("Record Type");
        bean1.setRecordType("MOL Company Number");
        bean1.setEmployeeUniqueId("Routing Bank Code");
        bean1.setEmployeeName("File Creation Date");
        bean1.setAgentId("File Creation Time");
        bean1.setEmployeeAccountWithAgent("Salary Month");
        bean1.setPayStartDateV("EDR Count");
        bean1.setPayEndDateV("Total Salary");
        bean1.setDaysInPeriodV("Payment Currency");
        bean1.setIncomeFixedComponent("Employer Reference");
        bean1.setIncomeVariableComponent("");
        bean1.setConveyanceAllowance("");

        HousemaidPayrollBean bean2 = new HousemaidPayrollBean();
        bean2.setSn("");
        bean2.setRecordType("SCR");
        bean2.setEmployeeUniqueId("*************");
        bean2.setEmployeeName("*********");
        bean2.setAgentId(DateUtil.formatDateDashed(todayDate));
        bean2.setEmployeeAccountWithAgent(DateUtil.formatSimpleTime(todayDate));
        bean2.setPayStartDateV(DateUtil.formatSimpleMonthYear(payrollEnd.toDate()));
        bean2.setPayEndDateV(String.valueOf(result.size()));
        bean2.setDaysInPeriodV(String.valueOf(Math.round(totalSalaries)));
        bean2.setIncomeFixedComponent("AED");
        bean2.setIncomeVariableComponent("");
        bean2.setConveyanceAllowance("");

        if (!isSentByEmail) {
            result.add(bean1);
        }
        result.add(bean2);
        if (isSentByEmail) {
            try {
                PayrollGenerationLibrary.generateHousemaidPWOFile(response, "Housemaid Ansari WPS Payroll Payment Work Order_M_" + DateUtil.formatFullDate(todayDate) + (finalFile ? " FINAL" : " NOT_FINAL") + ".xlsx", result, totalSalaries, payrollEnd, finalFile);
            } catch (IOException ex) {
                Logger.getLogger(PaymentWorkOrderController.class.getName()).log(Level.SEVERE, null, ex);
            } catch (URISyntaxException ex) {
                Logger.getLogger(PaymentWorkOrderController.class.getName()).log(Level.SEVERE, null, ex);
            }
        } else {
            try {
                createDownloadResponse(response, "Housemaid Ansari WPS Payroll Payment Work Order_M_" + DateUtil.formatFullDate(todayDate) + ".csv", generateCSVFromData(result));
            } catch (FileNotFoundException ex) {
                Logger.getLogger(PaymentWorkOrderController.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    public InputStream generateCSVFromData(List<HousemaidPayrollBean> data) throws FileNotFoundException {
        CSVWriter csvWriter = null;
        File file = Paths.get(System.getProperty("java.io.tmpdir"),
                new Date().getTime() + "")
                .toFile();

        try {
            //Create CSVWriter for writing to Employee.csv 
            csvWriter = new CSVWriter(new FileWriter(file));

            BeanToCsv bc = new BeanToCsv();

            ColumnPositionMappingStrategy mappingStrategy
                    = new ColumnPositionMappingStrategy();
            mappingStrategy.setType(HousemaidPayrollBean.class);
            String[] columns = new String[]{"sn", "recordType", "employeeUniqueId", "employeeName", "agentId", "employeeAccountWithAgent",
                "payStartDateV", "payEndDateV", "daysInPeriodV", "incomeFixedComponent", "incomeVariableComponent", "daysOnLeaveForPeriod",
                "conveyanceAllowance", "medicalAllowance", "annualPassageAllowance", "overtimeAllowance", "otherAllowance"
            };
            //Setting the colums for mappingStrategy
            mappingStrategy.setColumnMapping(columns);
            //Writing empList to csv file
            bc.write(mappingStrategy, csvWriter, data);

            System.out.println("CSV File written successfully!!!");

        } catch (Exception ee) {
            ee.printStackTrace();
        } finally {
            try {
                //closing the writer
                csvWriter.close();
            } catch (Exception ee) {
                ee.printStackTrace();
            }
        }
        return new FileInputStream(file);
    }

    // ACC-486
    //*************************** Qatar staff-cleaners payment work orders ******************************************
    @PreAuthorize("hasPermission('pwo','officeStaffPwo')")
    @RequestMapping("/qatarStaffCleanersPwo")
    public void qatarStaffCleanersPwo(HttpServletResponse response,
            @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(required = true, value = "sentByEmail") boolean isSentByEmail) throws FileNotFoundException, IOException, URISyntaxException {
        
        /*************************************Qatar Staff****************************************/
        //Payroll start and end date
        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }
        LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(dt);
        LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);

        //Get Office Staff
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.filterBy(
                new SelectFilter()
                        .or("status", "=", OfficeStaffStatus.ACTIVE)
                        .or("status", "=", OfficeStaffStatus.TERMINATED)
        );
        query.filterBy(
                new SelectFilter()
                        .or("excludedFromPayroll", "=", false)
                        .or("excludedFromPayroll", "IS NULL", null)
        );

        List<OfficeStaff> staffs = query.execute();
        int counter = 0;
        //PicklistItem vacationType = getItem(AccountingModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE, "pre-paid_vacation");
        List<OfficeStaffPayrollBean> result = new ArrayList<>();

        List<OfficeStaff> finalStaffs = new ArrayList<>();

        for (OfficeStaff staff : staffs) {
            if (staff.getStatus().equals(OfficeStaffStatus.TERMINATED) && staff.getTerminationDate() == null) {
                continue;
            }
            if (staff.getStatus().equals(OfficeStaffStatus.TERMINATED)) {
                if (staff.getTerminationDate() != null) {
                    LocalDate termination = new LocalDate(staff.getTerminationDate());
                    if (termination.isBefore(payrollEnd.withDayOfMonth(1))) {
                        continue;
                    }
                }
            }
//            boolean flag = false;
//            List<ScheduledAnnualVacation> annualVacations = staff.getScheduledAnnualVacations();
//            if (annualVacations != null && annualVacations.size() > 0) {
//
//                for (ScheduledAnnualVacation vacation : annualVacations) {
//                    LocalDate payrollDueDate = new LocalDate(vacation.getPayrollDueDate());
//                    if ((payrollDueDate.equals(payrollStart.minusMonths(1)) || payrollDueDate.isAfter(payrollStart.minusMonths(1)))
//                            && (payrollDueDate.equals(payrollEnd.minusMonths(1).plusDays(1)) || payrollDueDate.isBefore(payrollEnd.minusMonths(1).plusDays(1)))
//                            && vacation.getType().getCode().equals(vacationType.getCode()) && vacation.getAmount() > 0.0) {
//                        flag = true;
//                        break;
//                    }
//                }
//            }
//            if (!flag) {
            finalStaffs.add(staff);
//            }
        }

        Integer jobStartDate =
                Integer.parseInt(
                        Setup.getParameter(Setup.getCurrentModule(),
                                PayrollManagementModule.PARAMETER_PAYROLL_JOBS_START));
        Integer jobEndDate =
                Integer.parseInt(
                        Setup.getParameter(Setup.getCurrentModule(),
                                PayrollManagementModule.PARAMETER_PAYROLL_JOBS_END));
        //Jirra ACC-1085
        LocalDate todayDT = new LocalDate();
        if ((todayDT.getDayOfMonth() >= jobStartDate && todayDT.getMonthOfYear() == dt.getMonthOfYear())
                || (todayDT.getDayOfMonth() <= jobEndDate && todayDT.getMonthOfYear() == dt.getMonthOfYear() + 1)){
            scheduledMonthlyService.officeStaffRepayments(finalStaffs, dt.withDayOfMonth(1));
        }
        counter = 0;
        Double totalSalaries = 0.0;
        PicklistItem paymentMethod = getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_PAYMENT_METHOD_CODE, "qnb_wps");
        Date todayDate = new Date();
        OfficeStaffPayrollBean headerOne = getHeaderRow("qtr_cln_1");

        OfficeStaffPayrollBean headerOneInfo = new OfficeStaffPayrollBean();
        headerOneInfo.setIndexV("********");
        headerOneInfo.setEmployeeUniqueId(DateUtil.formatFullDate(todayDate));
        headerOneInfo.setVisaId(DateUtil.formatSimpleTime(todayDate));
        headerOneInfo.setOfficeStaffName("********");
        headerOneInfo.setBankShortName("");
        headerOneInfo.setEmployeeAccountWithAgent("QNB");
        headerOneInfo.setSalaryFrequency("*****************************");
        headerOneInfo.setDaysInPeriodV(DateUtil.formatSimpleMonthYear(todayDate));

        headerOneInfo.setColumn1("");
        headerOneInfo.setColumn2("");
        headerOneInfo.setColumn3("");
        headerOneInfo.setColumn4("");
        headerOneInfo.setColumn5("");

        result.add(headerOne);
        result.add(headerOneInfo);
        OfficeStaffPayrollBean headerTwo = getHeaderRow("qnb_wps");
        result.add(headerTwo);
        for (OfficeStaff staff : finalStaffs) {

            if (staff.getSalaryPayementMethod() != null && staff.getSalaryPayementMethod().getId().equals(paymentMethod.getId())) {
//                List<ScheduledAnnualVacation> annualVacations = staff.getScheduledAnnualVacations();
                OfficeStaffPayrollBean bean =
                        getQnbWPS(staff, payrollStart, payrollEnd);
                bean.setIndexNum(++counter);
                bean.setColumn6(bean.getBasicSalaryV());
//                    if (bean.getBalance() > 0) {
                result.add(bean);
//                    }
                totalSalaries += bean.getBasicSalary();
            }
        }



        /*************************************Qatar cleaners****************************************/

        String url = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.SALESFORCE_APIS_BASE_URL)
                + Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.SALESFORCE_APIS_QATAR_CLEANERS);

        Map<String, Object> mappedResult = new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();
        TypeReference<Map<String, Object>> typeRef
                = new TypeReference<Map<String, Object>>() {
        };
        String qtrClnUrl = url + "?file=ppwoqtr&date=" + DateUtil.formatDateSlashedV2(date);
        String s = apisHelper.getRequest(qtrClnUrl);
        if (s.equals("Error")){
            OfficeStaffPayrollBean exception = new OfficeStaffPayrollBean();
            exception.setIndexV("Sorry, an error occurred while retrieving Qatar cleaners data. Please try again in few moments");
            result.add(exception);
        }
        else{
            mappedResult = mapper.readValue(s, typeRef);
            if(mappedResult.get("status").toString().equals("success")) {
                Map<String, Object> mappedHeaderOne = (Map<String, Object>) mappedResult.get("qtrppwoData");
                totalSalaries += Double.valueOf(mappedHeaderOne.get("total_Salaries").toString());
                int counter2 = counter;
                counter += Integer.parseInt(mappedHeaderOne.get("total_records").toString());
                result.get(1).setBalanceV(totalSalaries.toString());
                result.get(1).setBasicSalaryV(String.valueOf(counter));


                List<Map<String, Object>> data = (List<Map<String, Object>>) mappedHeaderOne.get("content");

                for(Map<String, Object> row : data) {
                    result.add(getQtrClnRow(row, ++counter2));
                }
            } else {
                OfficeStaffPayrollBean exception = new OfficeStaffPayrollBean();
                exception.setIndexV("Sorry, an error occurred while retrieving Qatar cleaners data. Please try again in few moments");
                result.add(exception);
            }
        }

        PayrollGenerationLibrary.generateQatarStaffCleanersPWO(response, "QNP WPS payment work order of "+ DateUtil.formatMonth(payrollEnd.toDate())
                ,"SIF_********_QNB_" + DateUtil.formatFullDate(todayDate) + "_" + DateUtil.formatSimpleTime(todayDate)
                , result, totalSalaries, isSentByEmail);
    }

    //*************************** office staff payment work orders ******************************************
    @PreAuthorize("hasPermission('pwo','officeStaffPwo')")
    @RequestMapping("/officeStaffPwo")
    @Deprecated
    public void officeStaffPwo(HttpServletResponse response,
                               @RequestParam(value = "type") String type,
                               @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                               @RequestParam(required = false, value = "sentByEmail") boolean isSentByEmail) throws IOException, URISyntaxException {

        //Payroll start and end date
        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }
        LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(dt);
        LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);

        // START COMMENTED PAY-2
//        //Get Office Staff
//        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
//        query.filterBy(
//                new SelectFilter()
//                        .or("status", "=", OfficeStaffStatus.ACTIVE)
//                        .or("status", "=", OfficeStaffStatus.TERMINATED)
//        );
//        query.filterBy(
//                new SelectFilter()
//                        .or("excludedFromPayroll", "=", false)
//                        .or("excludedFromPayroll", "IS NULL", null)
//        );
        // END COMMENTED PAY-2

        //Get Office Staff
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.leftJoin("finalSettlement");

        // Not excluded from payroll
        SelectFilter notExcludedFromPayroll = new SelectFilter("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null);

        // Active and not excluded from payroll
        SelectFilter activeAndNotExcludedFromPayroll = new SelectFilter("status", "=", OfficeStaffStatus.ACTIVE)
                .and(notExcludedFromPayroll);

        // Terminated within payroll
        SelectFilter terminatedWithinPayrollDate = new SelectFilter("terminationDate", "IS NOT NULL", null)
                .and("terminationDate", ">=", payrollEnd.withDayOfMonth(1).toDate())
                .and("terminationDate", "<=", payrollEnd.dayOfMonth().withMaximumValue().toDate());


        // Terminated within payroll date and not excluded form payroll (Old case)
        SelectFilter terminatedWithinPayrollNotExcluded =   new SelectFilter("status", "=", OfficeStaffStatus.TERMINATED)
                .and("finalSettlement", "IS NULL", null)
                .and(terminatedWithinPayrollDate)
                .and(notExcludedFromPayroll);

        // Terminated within payroll date and Final settlement is approved before lock date (New case)
        SelectFilter terminatedWithinPayrollFinalSettlementApproved =   new SelectFilter("finalSettlement", "IS NOT NULL", null)
                .and("finalSettlement.approvedByCFO", "=", true)
                .and("finalSettlement.includeInPayroll", "=", true)
                .and(terminatedWithinPayrollDate);

        query.filterBy(new SelectFilter(activeAndNotExcludedFromPayroll).or(terminatedWithinPayrollNotExcluded)
                .or(terminatedWithinPayrollFinalSettlementApproved));

        //Jirra ACC-431
        String localStaffTagName = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_lOCAL_STAFF_NATIONALITY_TAG);


        if (type.equals("Ansari_WPS_Local"))
            query.filterBy("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EMARATI);
        else if(type.equals("Ansari_WPS")) {
            query.filterBy("employeeType", "!=", OfficeStaffType.DUBAI_STAFF_EMARATI);
        }

        List<OfficeStaff> staffs = query.execute();
        int counter = 0;
        List<OfficeStaffPayrollBean> result = new ArrayList<OfficeStaffPayrollBean>();

        List<OfficeStaff> finalStaffs = new ArrayList<OfficeStaff>();

        for (OfficeStaff staff : staffs) {
            //START PAY-2
//            if (staff.getStatus().equals(OfficeStaffStatus.TERMINATED) && staff.getTerminationDate() == null) {
//                continue;
//            }
//            if (staff.getStatus().equals(OfficeStaffStatus.TERMINATED)) {
//                if (staff.getTerminationDate() != null) {
//                    LocalDate termination = new LocalDate(staff.getTerminationDate());
//                    if (termination.isBefore(payrollEnd.withDayOfMonth(1))) {
//                        continue;
//                    }
//                }
//            }
            //END PAY-2

//            boolean flag = false;
//            List<ScheduledAnnualVacation> annualVacations = staff.getScheduledAnnualVacations();
//            if (annualVacations != null && annualVacations.size() > 0) {
//
//                for (ScheduledAnnualVacation vacation : annualVacations) {
//                    LocalDate payrollDueDate = new LocalDate(vacation.getPayrollDueDate());
//                    if ((payrollDueDate.equals(payrollStart.minusMonths(1)) || payrollDueDate.isAfter(payrollStart.minusMonths(1)))
//                            && (payrollDueDate.equals(payrollEnd.minusMonths(1).plusDays(1)) || payrollDueDate.isBefore(payrollEnd.minusMonths(1).plusDays(1)))
//                            && vacation.getType().getCode().equals(vacationType.getCode()) && vacation.getAmount() > 0.0) {
//                        flag = true;
//                        break;
//                    }
//                }
//            }
//            if (flag) {
//                continue;
//            } else {
            finalStaffs.add(staff);
//            }
        }
        //Jirra ACC-1085

        //START PAY-2
//        Integer jobStartDate =
//                Integer.parseInt(
//                        Setup.getParameter(Setup.getCurrentModule(),
//                                PayrollManagementModule.PARAMETER_PAYROLL_JOBS_START));
//        Integer jobEndDate =
//                Integer.parseInt(
//                        Setup.getParameter(Setup.getCurrentModule(),
//                                PayrollManagementModule.PARAMETER_PAYROLL_JOBS_END));
//        LocalDate todayDT = new LocalDate();
//        if ((todayDT.getDayOfMonth() >= jobStartDate && todayDT.getMonthOfYear() == dt.getMonthOfYear())
//                || (todayDT.getDayOfMonth() <= jobEndDate && todayDT.getMonthOfYear() == dt.getMonthOfYear() + 1)){
//            scheduledMonthlyService.officeStaffRepayments(finalStaffs, dt.withDayOfMonth(1));
//        }
        //END PAY-2

        //Ansari International PWO Generation
        if (type.equals("Ansari_International")) {
            for (OfficeStaff staff : finalStaffs) {

//                List<ScheduledAnnualVacation> annualVacations = staff.getScheduledAnnualVacations();

                OfficeStaffPayrollBean bean = new OfficeStaffPayrollBean();

                if (staff.getLocalReceiverName() == null && staff.getSalaryPayementMethod() != null && (staff.getSalaryPayementMethod().equals(
                        getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_PAYMENT_METHOD_CODE, "ansari_transfer"))
                        || staff.getSalaryPayementMethod().equals(getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_PAYMENT_METHOD_CODE, "western_union")))
                        && staff.getSalaryTransfereDestination() != null
                        && !staff.getSalaryTransfereDestination().equals(getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_TRANSFER_DESTINATION_CODE, "lama_labbad"))
                        && !staff.getSalaryTransfereDestination().equals(getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_TRANSFER_DESTINATION_CODE, "judi_anne_angeles_torres"))
                        && !staff.getSalaryTransfereDestination().equals(getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_TRANSFER_DESTINATION_CODE, "samer_fateh_dofesh"))
                        && !staff.getSalaryTransfereDestination().equals(getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_TRANSFER_DESTINATION_CODE, "nojoud_nofal_sneij"))
                        && !staff.getSalaryTransfereDestination().equals(getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_TRANSFER_DESTINATION_CODE, "youmna_wasel_salloum"))) {
                    bean = getAnsariInternational(finalStaffs, staff, payrollStart, payrollEnd);
                    if (bean.getBalance() > 0) {
                        result.add(bean);
                    }
                }
            }

            PayrollGenerationLibrary.generateOfficeStaffPWOFileAnsariInternational(response,"Ansari International of "+ DateUtil.formatMonth(payrollEnd.toDate()), "Ansari International", result, 0.0,isSentByEmail);

        }

        //qnb_wps
        if (type.equals("qnb_wps")) {
            counter = 0;
            Double totalSalaries = 0.0;
            PicklistItem paymentMethod = getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_PAYMENT_METHOD_CODE, "qnb_wps");
            Date todayDate = new Date();

            OfficeStaffPayrollBean bean2 = new OfficeStaffPayrollBean();
            bean2.setIndexV("********");
            bean2.setEmployeeUniqueId(DateUtil.formatFullDate(todayDate));
            bean2.setVisaId(DateUtil.formatSimpleTime(todayDate));
            bean2.setOfficeStaffName("********");
            bean2.setBankShortName("");
            bean2.setEmployeeAccountWithAgent("QNB");
            bean2.setSalaryFrequency("*****************************");
            bean2.setDaysInPeriodV(DateUtil.formatSimpleMonthYear(todayDate));

            bean2.setColumn1("");
            bean2.setColumn2("");
            bean2.setColumn3("");
            bean2.setColumn4("");
            bean2.setColumn5("");
            result.add(bean2);
            for (OfficeStaff staff : finalStaffs) {

                if (staff.getSalaryPayementMethod() != null && staff.getSalaryPayementMethod().getId().equals(paymentMethod.getId())) {
//                    List<ScheduledAnnualVacation> annualVacations = staff.getScheduledAnnualVacations();
                    OfficeStaffPayrollBean bean = new OfficeStaffPayrollBean();
                    bean = getQnbWPS(staff, payrollStart, payrollEnd);
                    bean.setIndexNum(++counter);
                    bean.setColumn6(bean.getBasicSalaryV());
                    //Jirra ACC-1258
                    bean2.setOfficeStaffAccountName(staff.getAccountName());
                    result.add(bean);
                    totalSalaries += bean.getBasicSalary();
                }
            }

            result.get(0).setColumn6(totalSalaries.toString());
            result.get(0).setBasicSalaryV(String.valueOf(counter));
            PayrollGenerationLibrary.generateOfficeStaffQNPWPS(response,"QNP WPS payment work order of "+ DateUtil.formatMonth(payrollEnd.toDate()), "SIF_********_QNB_" + DateUtil.formatFullDate(todayDate) + "_" + DateUtil.formatSimpleTime(todayDate), result, 0.0,isSentByEmail);

        }
        //international bank transfer
        else if (type.equals("international_bank_transfer") || type.equals("uae_bank_transfer")) {
            PicklistItem paymentMethod = getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_PAYMENT_METHOD_CODE, type);
            for (OfficeStaff staff : finalStaffs) {


                OfficeStaffPayrollBean bean = new OfficeStaffPayrollBean();
                if (staff.getSalaryPayementMethod() != null && staff.getSalaryPayementMethod().getId().equals(paymentMethod.getId())) {
                    bean = getInternationalOrUaeOfficeStaffBean(staff, payrollStart, payrollEnd);
                    if (bean.getBalance() > 0) {
                        result.add(bean);
                    }
                }

            }
            PayrollGenerationLibrary.generateOfficeStaffPWOFileInternationalOrUaeBankTransfer(response,paymentMethod.getName() + " Payroll Payment Work Order of "+ DateUtil.formatMonth(payrollEnd.toDate()), paymentMethod.getName() + " Payroll Payment Work Order", result, type,isSentByEmail);
        }

        //Ansari Other Payroll Payments Work Order
        if (type.equals("Ansari_Other")) {
//            result.add(getHeaderRow(type));
            for (OfficeStaff staff : finalStaffs) {

//                List<ScheduledAnnualVacation> annualVacations = staff.getScheduledAnnualVacations();

                OfficeStaffPayrollBean bean = new OfficeStaffPayrollBean();

                if (staff.getLocalReceiverName() != null && staff.getSalaryPayementMethod() != null && (staff.getSalaryPayementMethod().equals(
                        getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_PAYMENT_METHOD_CODE, "ansari_transfer"))
                        || staff.getSalaryPayementMethod().equals(getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_PAYMENT_METHOD_CODE, "western_union")))
                        && staff.getSalaryTransfereDestination() != null
                        && !staff.getSalaryTransfereDestination().equals(getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_TRANSFER_DESTINATION_CODE, "lama_labbad"))
                        && !staff.getSalaryTransfereDestination().equals(getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_TRANSFER_DESTINATION_CODE, "judi_anne_angeles_torres"))
                        && !staff.getSalaryTransfereDestination().equals(getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_TRANSFER_DESTINATION_CODE, "samer_fateh_dofesh"))
                        && !staff.getSalaryTransfereDestination().equals(getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_TRANSFER_DESTINATION_CODE, "nojoud_nofal_sneij"))
                        && !staff.getSalaryTransfereDestination().equals(getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_TRANSFER_DESTINATION_CODE, "youmna_wasel_salloum"))) {
                    bean = getAnsariInternational(finalStaffs, staff, payrollStart, payrollEnd);
                    if (bean.getBalance() > 0) {
                        result.add(bean);
                    }
                }
            }

            PayrollGenerationLibrary.generateOfficeStaffPWOFileAnsariInternational(response, "Ansari Other Payroll Payments Work Order of "+ DateUtil.formatMonth(payrollEnd.toDate()),"Ansari Other Payroll Payments Work Order", result, 0.0,isSentByEmail);

        }

        //Ansari Local Transfer
        if (type.equals("Ansari_Local_Transfer")) {

            PicklistItem paymentMethod = getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_PAYMENT_METHOD_CODE, "local_transfer");
            for (OfficeStaff staff : finalStaffs) {

//                List<ScheduledAnnualVacation> annualVacations = staff.getScheduledAnnualVacations();

                OfficeStaffPayrollBean bean = new OfficeStaffPayrollBean();

                if (staff.getSalaryPayementMethod() != null && staff.getSalaryPayementMethod().getId().equals(paymentMethod.getId())) {
                    bean = getAnsariLocalTransfer(staff, payrollStart, payrollEnd);
                    if (bean.getBalance() > 0) {
                        result.add(bean);
                    }
                }
            }
            PayrollGenerationLibrary.generateOfficeStaffPWOFileAnsariInternational(response,"Ansari Local Payroll Payments Work Order of "+ DateUtil.formatMonth(payrollEnd.toDate()), "Ansari Local Payroll Payments Work Order", result, 0.0,isSentByEmail);

        }

        //Non-Housemaid_Non-Cleaner Ansari
        if (type.equals("Ansari_WPS") || type.equals("Ansari_WPS_Local")) {
            OfficeStaffPayrollBean headerRow = getHeaderRow(type);
            headerRow.setBalance(1000000000.0);
            counter = 0;
            Double totalSalaries = 0.0;
            PicklistItem paymentMethod = getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_PAYMENT_METHOD_CODE, "ansari_wps");
            for (OfficeStaff staff : finalStaffs) {

//                List<ScheduledAnnualVacation> annualVacations = staff.getScheduledAnnualVacations();

                OfficeStaffPayrollBean bean = new OfficeStaffPayrollBean();

                if (staff.getSalaryPayementMethod() != null && staff.getSalaryPayementMethod().getId().equals(paymentMethod.getId())) {
                    bean = getAnsariWPS(staff, payrollStart, payrollEnd);

                    if (bean.getBalance() > 0) {
                        counter++;
                        totalSalaries += bean.getBalance();
                        result.add(bean);
                    }
                    result.sort(Comparator.comparingDouble(OfficeStaffPayrollBean::getBalance)
                            .reversed());

                }
            }
            for (int i = 0; i < result.size(); i++) {
                result.get(i).setSn("OS-" + String.valueOf(i + 1));
            }

            Date todayDate = new Date();

//            OfficeStaffPayrollBean bean1 = new OfficeStaffPayrollBean();
//            bean1.setSn("Record Type");
//            bean1.setRecordType("MOL Company Number");
//            bean1.setEmployeeUniqueId("Routing Bank Code");
//            bean1.setOfficeStaffName("File Creation Date");
//            bean1.setAgentId("File Creation Time");
//            bean1.setEmployeeAccountWithAgent("Salary Month");
//            bean1.setPayStartDateV("EDR Count");
//            bean1.setPayEndDateV("Total Salary");
//            bean1.setDaysInPeriodV("Payment Currency");
//            bean1.setIncomeFixedComponent("Employer Reference");
//            bean1.setIncomeVariableComponent("");
//            bean1.setConveyanceAllowance("");
//            bean1.setHousing("");
            OfficeStaffPayrollBean bean2 = new OfficeStaffPayrollBean();
            bean2.setSn("");
            bean2.setRecordType("SCR");
            bean2.setEmployeeUniqueId("*************");
            bean2.setOfficeStaffName("*********");
            bean2.setLocal("");
            bean2.setAgentId(DateUtil.formatFullDate(todayDate));
            bean2.setEmployeeAccountWithAgent(DateUtil.formatSimpleTime(todayDate));
            bean2.setPayStartDateV(DateUtil.formatSimpleMonthYear(todayDate));
            bean2.setPayEndDateV(String.valueOf(counter));
            bean2.setDaysInPeriodV(String.valueOf(totalSalaries));
            bean2.setIncomeFixedComponent("AED");
            bean2.setIncomeVariableComponent("");
            bean2.setConveyanceAllowance("");
            bean2.setHousing("");

//            result.add(bean1);
            result.add(bean2);

            //Delete the first row
//            BufferedReader reader = new BufferedReader(
//                new InputStreamReader(generateCSVFromData(result, "Ansari_WPS"),StandardCharsets.UTF_8));
//            reader.readLine();
//            
//            InputStream in2 = new ReaderInputStream(reader);
            //Generate CSV
//            createDownloadResponse(response, "Non-Housemaid_Non-Cleaner Ansari.csv",in2 );
            PayrollGenerationLibrary.generateOfficeStaffPWOFileAnsariWPS(response,(type.contains("Local") ? "Local " : "") + "Non-Housemaid_Non-Cleaner Ansari of "+ DateUtil.formatMonth(payrollEnd.toDate()), (type.contains("Local") ? "Local " : "") + "Non-Housemaid_Non-Cleaner Ansari", result, totalSalaries,isSentByEmail);

        }

    }

    public InputStream generateCSVFromData(List<OfficeStaffPayrollBean> data, String type) throws FileNotFoundException {
        CSVWriter csvWriter = null;
        File file = Paths.get(System.getProperty("java.io.tmpdir"),
                new Date().getTime() + "")
                .toFile();

        try {

            FileOutputStream os = new FileOutputStream(file);
            os.write(0xef);
            os.write(0xbb);
            os.write(0xbf);
            csvWriter = new CSVWriter(new OutputStreamWriter(os, "UTF-8"));

            BeanToCsv bc = new BeanToCsv();

            ColumnPositionMappingStrategy mappingStrategy
                    = new ColumnPositionMappingStrategy();
            mappingStrategy.setType(OfficeStaffPayrollBean.class);
            String[] columns = null;
            if (type.equals("Ansari_International") || type.equals("Ansari_Other") || type.equals("Ansari_Local_Transfer"))//Ansari_International
            {
                columns = new String[]{"officeStaffName", "officeStaffArabicName", "destinationOfTransfere", "mobileNumber", "balanceV",
                        "currencyUsed"};

            } else if (type.equals("qnb_wps"))//qnb_wps
            {
                columns = new String[]{"indexV", "employeeUniqueId", "visaId", "officeStaffName", "bankShortName", "employeeAccountWithAgent", "salaryFrequency", "daysInPeriodV", "balanceV",
                        "basicSalaryV", "column1", "column2", "column3", "column4", "column5"};
            } else if (type.equals("adeeb"))//Manual Adeeb
            {
                columns = new String[]{"officeStaffName", "bankShortName", "currencyUsed"};
            } //Setting the colums for mappingStrategy
            else if (type.equals("Ansari_WPS")) {
                columns = new String[]{"sn", "recordType", "employeeUniqueId", "officeStaffName", "agentId", "employeeAccountWithAgent",
                        "payStartDateV", "payEndDateV", "daysInPeriodV", "incomeFixedComponent", "incomeVariableComponent", "daysOnLeaveForPeriod",
                        "housing", "conveyanceAllowance", "medicalAllowance", "annualPassageAllowance", "overtimeAllowance", "otherAllowance", "leaveEncashment"
                };
            }
            mappingStrategy.setColumnMapping(columns);
            //Writing to csv file
            bc.write(mappingStrategy, csvWriter, data);

            System.out.println("CSV File written successfully!!!");

        } catch (Exception ee) {
            ee.printStackTrace();
        } finally {
            try {
                //closing the writer
                csvWriter.close();
            } catch (Exception ee) {
                ee.printStackTrace();
            }
        }
        return new FileInputStream(file);
    }

    public OfficeStaffPayrollBean getAnsariInternational(List<OfficeStaff> finalStaffs, OfficeStaff staff, LocalDate payrollStart, LocalDate payrollEnd) {

        OfficeStaffPayrollBean bean =
                setOfficeStaffPayrollBeanValues(
                        null, staff, payrollStart, payrollEnd, payrollEnd.withDayOfMonth(1));

        //StaffName in Arabic
        if (staff.getUpdatedArabicName() != null && !staff.getUpdatedArabicName().isEmpty()) {
            bean.setOfficeStaffArabicName(staff.getUpdatedArabicName());
        } else {
            bean.setOfficeStaffArabicName(staff.getFullNameInArabic());
        }

        //Staff name
        if (staff.getUpdatedEnglishName() != null && !staff.getUpdatedEnglishName().isEmpty()) {
            bean.setOfficeStaffName(staff.getUpdatedEnglishName());
        } else {
            bean.setOfficeStaffName(staff.getName());
        }

        //Jirra ACC-1258
        //Staff account name
        bean.setOfficeStaffAccountName(staff.getAccountName());

        if (staff.getUpdatedMobileNumber() != null && !staff.getUpdatedMobileNumber().isEmpty()) {
            bean.setMobileNumber(staff.getUpdatedMobileNumber());
        } else if (staff.getPhoneNumber() != null) {
            bean.setMobileNumber(staff.getPhoneNumber());
        }
        if (staff.getSalaryCurrency() != null) {
            bean.setCurrencyUsed(staff.getSalaryCurrency().toString());
        }

        if(staff.getSelectedTransferDestination() == null) {
            if (staff.getUpdatedSalaryTransfereDestination() != null) {
                if (staff.getUpdatedSalaryTransfereDestination().getCode().equals("other")) {
                    bean.setDestinationOfTransfere(staff.getUpdatedOtherSalaryTransfereDestination() != null ? staff.getUpdatedOtherSalaryTransfereDestination() : "");
                } else {
                    bean.setDestinationOfTransfere(staff.getUpdatedSalaryTransfereDestination().getName());
                }
            } else if (staff.getSalaryTransfereDestination() != null) {
                if (staff.getSalaryTransfereDestination().getCode().equals("other")) {
                    bean.setDestinationOfTransfere(staff.getSalaryTransferDestinationOther() != null ? staff.getSalaryTransferDestinationOther() : "");
                } else {
                    bean.setDestinationOfTransfere(staff.getSalaryTransfereDestination().getName());
                }
            }
        } else {
            bean.setDestinationOfTransfere(staff.getSelectedTransferDestination().getNearestCenter());
        }

        //setTotalIncome(staff, bean, annualVacations, payrollStart, payrollEnd);

        //Payment Method
        if (staff.getSalaryPayementMethod() != null) {
            bean.setPaymentMethod(staff.getSalaryPayementMethod().getName());
        }
        if (staff.getSalaryCurrency() != null) {
            bean.setCurrencyUsed(staff.getSalaryCurrency().toString());
        }

        if (localReceivers.contains(staff.getName())) {
            Double proxyBalance = 0.0;
            PicklistItem salaryDestination = getItem(PayrollManagementModule.PICKLIST_OFFICESTAFF_SALARY_TRANSFER_DESTINATION_CODE,
                    localReceiversTransferDistination.get(staff.getName()));
            for (OfficeStaff temp : finalStaffs) {
                if (temp.getSalaryTransfereDestination() == salaryDestination) {
                    OfficeStaffPayrollBean tempBean =
                            setOfficeStaffPayrollBeanValues(
                                    null, staff, payrollStart, payrollEnd, payrollEnd.withDayOfMonth(1));
                    //setTotalIncome(temp, tempBean, vacations, payrollStart, payrollEnd);
                    //Payment Method
                    if (staff.getSalaryPayementMethod() != null) {
                        bean.setPaymentMethod(staff.getSalaryPayementMethod().getName());
                    }
                    if (staff.getSalaryCurrency() != null) {
                        bean.setCurrencyUsed(staff.getSalaryCurrency().toString());
                    }
                    proxyBalance += tempBean.getBalance();
                }
            }
            bean.setProxyBalance(proxyBalance);
            System.out.println("proxyBalance" + bean.getProxyBalance());
        }

        return bean;

    }

    public OfficeStaffPayrollBean getQnbWPS(OfficeStaff staff, LocalDate payrollStart, LocalDate payrollEnd) {

        OfficeStaffPayrollBean bean =
                setOfficeStaffPayrollBeanValues(
                        null, staff, payrollStart, payrollEnd, payrollEnd.withDayOfMonth(1));

        if (staff.getQid() != null) //Employee QID
        {
            bean.setEmployeeUniqueId(staff.getQid());
        }
        // Visa id
        bean.setVisaId("");
        //Employee Name
        bean.setOfficeStaffName(staff.getName());
        //Employee Bank Short Name
        bean.setBankShortName("QNB");
        //Employee Account
        if (staff.getEmployeeAccountQnb() != null) {
            bean.setEmployeeAccountWithAgent(staff.getEmployeeAccountQnb());
        }
        //Salary Frequency
        bean.setSalaryFrequency("M");
        //Number of Working Days
        bean.setDaysInPeriodV(String.valueOf(Math.abs(Days.daysBetween(payrollEnd.withDayOfMonth(1), payrollEnd.dayOfMonth().withMaximumValue()).getDays()) + 1));

        //setTotalIncome(staff, bean, annualVacations, payrollStart, payrollEnd);

        //Payment Method
        if (staff.getSalaryPayementMethod() != null) {
            bean.setPaymentMethod(staff.getSalaryPayementMethod().getName());
        }
        if (staff.getSalaryCurrency() != null) {
            bean.setCurrencyUsed(staff.getSalaryCurrency().toString());
        }
        //Basic salry
        bean.setBasicSalary(bean.getBalance());
        //Extra hours
        bean.setColumn1("0");
        //Extra Income
        bean.setColumn2("0");
        //Deductions 
        bean.setColumn3("0");
        //Payment Type 
        bean.setColumn4("Normal Payment");
        //Notes / Comments 
        bean.setColumn5("Normal");

        return bean;

    }

    public OfficeStaffPayrollBean getInternationalOrUaeOfficeStaffBean(OfficeStaff staff, LocalDate payrollStart, LocalDate payrollEnd) {

        OfficeStaffPayrollBean bean =
                setOfficeStaffPayrollBeanValues(
                        null, staff, payrollStart, payrollEnd, payrollEnd.withDayOfMonth(1));
        //Staff name
        bean.setOfficeStaffName(staff.getName());
        //setTotalIncome(staff, bean, annualVacations, payrollStart, payrollEnd);
        //Payment Method
        if (staff.getSalaryPayementMethod() != null) {
            bean.setPaymentMethod(staff.getSalaryPayementMethod().getName());
        }
        if (staff.getSalaryCurrency() != null) {
            bean.setCurrencyUsed(staff.getSalaryCurrency().toString());
        }
        if (staff.getSalaryCurrency() != null) {
            bean.setCurrencyUsed(staff.getSalaryCurrency().toString());
        }

        //Jirra ACC-1258
        bean.setOfficeStaffAccountName(staff.getAccountName());
        bean.setBankShortName(bean.getBalanceV());
        bean.setTeam(staff.getTeam()!= null?staff.getTeam().getName():"");
        bean.setDepartments((staff.getDepartments() != null && !staff.getDepartments().isEmpty()) ? staff.getDepartmentNames() : ""); //todo: check with Team leader if needed

        if(staff.getSelectedTransferDestination() == null) {
            if (staff.getUpdatedSalaryTransfereDestination() != null) {
                if (staff.getUpdatedSalaryTransfereDestination().getCode().equals("other")) {
                    bean.setDestinationOfTransfere(staff.getUpdatedOtherSalaryTransfereDestination() != null ? staff.getUpdatedOtherSalaryTransfereDestination() : "");
                } else {
                    bean.setDestinationOfTransfere(staff.getUpdatedSalaryTransfereDestination().getName());
                }
            } else if (staff.getSalaryTransfereDestination() != null) {
                if (staff.getSalaryTransfereDestination().getCode().equals("other")) {
                    bean.setDestinationOfTransfere(staff.getSalaryTransferDestinationOther() != null ? staff.getSalaryTransferDestinationOther() : "");
                } else {
                    bean.setDestinationOfTransfere(staff.getSalaryTransfereDestination().getName());
                }
            }
        } else {
            // put data here...
            TransferDestination transferDestination = staff.getSelectedTransferDestination();
            String details = "";
            if(transferDestination.getBankName() != null) {
                details += "Bank Name: " + transferDestination.getBankName() + "\n";
            }
            if(transferDestination.getAccountHolderName() != null) {
                details += "Account Holder Name: " + transferDestination.getAccountHolderName() + "\n";
            }
            if(transferDestination.getIban() != null) {
                details += "IBAN: " + transferDestination.getIban() + "\n";
            }
            if(transferDestination.getAccountNumber() != null) {
                details += "Account Number: " + transferDestination.getAccountNumber() + "\n";
            }
            if(transferDestination.getSwiftCode() != null) {
                details += "Swift Code: " + transferDestination.getSwiftCode() + "\n";
            }
            bean.setDestinationOfTransfere(details);
        }
        return bean;
    }

    public OfficeStaffPayrollBean getAnsariLocalTransfer(OfficeStaff staff, LocalDate payrollStart, LocalDate payrollEnd) {

        OfficeStaffPayrollBean bean =
                setOfficeStaffPayrollBeanValues(
                        null, staff, payrollStart, payrollEnd, payrollEnd.withDayOfMonth(1));

        //StaffName in Arabic
        if (staff.getUpdatedArabicName() != null && !staff.getUpdatedArabicName().isEmpty()) {
            bean.setOfficeStaffArabicName(staff.getUpdatedArabicName());
        } else {
            bean.setOfficeStaffArabicName(staff.getFullNameInArabic());
        }

        //Staff name
        if (staff.getUpdatedEnglishName() != null && !staff.getUpdatedEnglishName().isEmpty()) {
            bean.setOfficeStaffName(staff.getUpdatedEnglishName());
        } else {
            bean.setOfficeStaffName(staff.getName());
        }

        //Jirra ACC-1258
        bean.setOfficeStaffAccountName(staff.getAccountName());

        if (staff.getUpdatedMobileNumber() != null && !staff.getUpdatedMobileNumber().isEmpty()) {
            bean.setMobileNumber(staff.getUpdatedMobileNumber());
        } else if (staff.getPhoneNumber() != null) {
            bean.setMobileNumber(staff.getPhoneNumber());
        }
        if (staff.getSalaryCurrency() != null) {
            bean.setCurrencyUsed(staff.getSalaryCurrency().toString());
        }

        if (staff.getUpdatedSalaryTransfereDestination() != null) {
            if (staff.getUpdatedSalaryTransfereDestination().getCode().equals("other")) {
                bean.setDestinationOfTransfere(staff.getUpdatedOtherSalaryTransfereDestination() != null ? staff.getUpdatedOtherSalaryTransfereDestination() : "");
            } else {
                bean.setDestinationOfTransfere(staff.getUpdatedSalaryTransfereDestination().getName());
            }
        } else if (staff.getSalaryTransfereDestination() != null) {
            if (staff.getSalaryTransfereDestination().getCode().equals("other")) {
                bean.setDestinationOfTransfere(staff.getSalaryTransferDestinationOther() != null ? staff.getSalaryTransferDestinationOther() : "");
            } else {
                bean.setDestinationOfTransfere(staff.getSalaryTransfereDestination().getName());
            }
        }
        //setTotalIncome(staff, bean, annualVacations, payrollStart, payrollEnd);
        //Payment Method
        if (staff.getSalaryPayementMethod() != null) {
            bean.setPaymentMethod(staff.getSalaryPayementMethod().getName());
        }
        if (staff.getSalaryCurrency() != null) {
            bean.setCurrencyUsed(staff.getSalaryCurrency().toString());
        }
        return bean;

    }

    public OfficeStaffPayrollBean getAnsariWPS(OfficeStaff staff, LocalDate payrollStart, LocalDate payrollEnd) {

        OfficeStaffPayrollBean bean =
                setOfficeStaffPayrollBeanValues(
                        null, staff, payrollStart, payrollEnd, payrollEnd.withDayOfMonth(1));

        //Jirra ACC-431
        String localStaffTagName = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_lOCAL_STAFF_NATIONALITY_TAG);
        if (staff.getNationality() != null
                && staff.getNationality().hasTag(localStaffTagName))
            bean.setLocal("Yes");

        bean.setOfficeStaffName(staff.getName());
        NewRequest visaNewRequest = staff.getVisaNewRequest();
        if (visaNewRequest != null) {
            if (visaNewRequest.getEmployeeUniqueId() != null) {
                bean.setEmployeeUniqueId(visaNewRequest.getEmployeeUniqueId());
            }
            if (visaNewRequest.getAgentId() != null) {
                bean.setAgentId(visaNewRequest.getAgentId());
            }
            if (visaNewRequest.getEmployeeAccountWithAgent() != null) {
                bean.setEmployeeAccountWithAgent(visaNewRequest.getEmployeeAccountWithAgent());
            }
        }
        //setTotalIncome(staff, bean, annualVacations, payrollStart, payrollEnd);
        //Payment Method
        if (staff.getSalaryPayementMethod() != null) {
            bean.setPaymentMethod(staff.getSalaryPayementMethod().getName());
        }
        if (staff.getSalaryCurrency() != null) {
            bean.setCurrencyUsed(staff.getSalaryCurrency().toString());
        }
        bean.setIncomeVariableComponent("0.0");
        bean.setPayStartDate(new java.sql.Date(payrollEnd.withDayOfMonth(1).toDate().getTime()));
        bean.setPayEndDate(new java.sql.Date(payrollEnd.dayOfMonth().withMaximumValue().toDate().getTime()));
        bean.setDaysInPeriod(Math.abs(DateUtil.getDaysBetween(bean.getPayEndDate(), bean.getPayStartDate())) + 1);
        //Leave Encashment
        bean.setColumn1("");

        bean.setLeaveEncashment("");
        bean.setRecordType("EDR");
        if (bean.getHousing() == null) {
            bean.setHousing("");
        }
        bean.setColumn6("");

        //Jirra ACC-1258
        bean.setOfficeStaffAccountName(staff.getAccountName());
        return bean;

    }

    public OfficeStaffPayrollBean getHeaderRow(String type) {
        OfficeStaffPayrollBean bean = new OfficeStaffPayrollBean();

        switch (type) {
            case "Ansari_International":
            case "Ansari_Other":
            case "Ansari_Local_Transfer":
                /**
                 * "officeStaffName", "officeStaffArabicName",
                 * "destinationOfTransfere", "mobileNumber", "balanceV",
                 * "currencyUsed"
                 */
                bean.setOfficeStaffName("Full Name in English");
                bean.setOfficeStaffArabicName("Full Name in Arabic");
                bean.setDestinationOfTransfere("Destination of Transfer");
                bean.setMobileNumber("Mobile Number");
                bean.setBalanceV("Salary");
                bean.setCurrencyUsed("Currency");
                break;
            case "qnb_wps":
                /**
                 * "indexV", "employeeUniqueId", "visaId",
                 * "officeStaffName","bankShortName",
                 * "employeeAccountWithAgent","salaryFrequency", "daysInPeriodV",
                 * "balanceV",
                 * "basicSalaryV","column1","column2","column3","column4","column5"
                 */
                bean.setIndexV("Record Sequence");
                bean.setEmployeeUniqueId("Employee QID");
                bean.setVisaId("Employee Visa ID");
                bean.setOfficeStaffName("Employee Name");
                bean.setBankShortName("Employee Bank Short Name");
                bean.setEmployeeAccountWithAgent("Employee Account");
                bean.setSalaryFrequency("Salary Frequency");
                bean.setDaysInPeriodV("Number of Working Days");
                bean.setBalanceV("Net Salary");
                bean.setBasicSalaryV("Basic Salary");
                bean.setColumn1("Extra hours");
                bean.setColumn2("Extra Income");
                bean.setColumn3("Deductions");
                bean.setColumn4("Payment Type");
                bean.setColumn5("Notes / Comments");
                break;
            case "adeeb":
                bean.setOfficeStaffName("Full Name in English");
                bean.setBankShortName("Amount");
                bean.setCurrencyUsed("Currency");
                break;
            case "Ansari_WPS":
                /**
                 * sn", "recordType", "employeeUniqueId",
                 * "officeStaffName","agentId", "employeeAccountWithAgent",
                 * "payStartDateV","payEndDateV","daysInPeriodV","incomeFixedComponent","incomeVariableComponent","daysOnLeaveForPeriod",
                 * "housingAllowance","conveyanceAllowance","medicalAllowance","annualPassageAllowance","overtimeAllowance","otherAllowance","leaveEncashment"
                 */
                bean.setSn("SN");
                bean.setRecordType("Record Type");
                bean.setEmployeeUniqueId("Employee Unique ID");
                bean.setOfficeStaffName("Employee Name");
                bean.setAgentId("Agent ID");
                bean.setEmployeeAccountWithAgent("Employee Account with Agent");
                bean.setPayStartDateV(String.valueOf("Pay Start Date"));
                bean.setPayEndDateV("Pay End Date");
                bean.setDaysInPeriodV("Days in Period");
                bean.setIncomeFixedComponent("Income Fixed Component ");
                bean.setIncomeVariableComponent("Income Variable Component");
                bean.setConveyanceAllowance("Days on leave for period");
                bean.setHousing("Housing Allowance");
                bean.setConveyanceAllowance("Conveyance Allowance");
                bean.setMedicalAllowance("Medical Allowance");
                bean.setAnnualPassageAllowance("Annual Passage Allowance");
                bean.setOvertimeAllowance("Overtime Allowance");
                bean.setOtherAllowance("Other Allowance");
                bean.setLeaveEncashment("Leave Encashment");
                break;
            case "qtr_cln_1":
                bean.setIndexV("Employer EID");
                bean.setEmployeeUniqueId("File Creation Date");
                bean.setVisaId("File Creation Time");
                bean.setOfficeStaffName("Payer EID");
                bean.setBankShortName("Payer QID");
                bean.setEmployeeAccountWithAgent("Payer Bank Short Name");
                bean.setSalaryFrequency("Payer IBAN");
                bean.setDaysInPeriodV("Salary Year and Month");
                bean.setBalanceV("Total Salaries");
                bean.setBasicSalaryV("Total records");
                bean.setColumn1("");
                bean.setColumn2("");
                bean.setColumn3("");
                bean.setColumn4("");
                bean.setColumn5("");
                break;
            default:
                break;
        }
        return bean;
    }

    public OfficeStaffPayrollBean getQtrClnRow(Map<String, Object> data, int counter) {
        OfficeStaffPayrollBean bean = new OfficeStaffPayrollBean();

        bean.setIndexNum(counter);
        //bean.setIndexV(data.get("record_Sequence").toString());
        bean.setEmployeeUniqueId(data.get("employee_QID").toString());
        bean.setVisaId(data.get("employee_Visa_ID").toString());
        bean.setOfficeStaffName(data.get("employee_Name").toString());
        bean.setBankShortName(data.get("employee_Bank_Short_Name").toString());
        bean.setEmployeeAccountWithAgent(data.get("employee_Account").toString());
        bean.setSalaryFrequency(data.get("salary_Frequency").toString());
        bean.setDaysInPeriodV(data.get("number_of_Working_Days").toString());
        bean.setBalanceV(data.get("net_Salary").toString());
        bean.setBasicSalaryV(data.get("basic_Salary").toString());
        bean.setColumn1(data.get("extra_hours").toString());
        bean.setColumn2(data.get("extra_Income").toString());
        bean.setColumn3(data.get("deductions").toString());
        bean.setColumn4(data.get("payment_Type").toString());
        bean.setColumn5(data.get("notes_Comments").toString());

        return bean;
    }
}
