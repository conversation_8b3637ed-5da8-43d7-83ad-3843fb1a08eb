package com.magnamedia.controller;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.OfficeStaffCandidate;
import com.magnamedia.entity.OfficeStaffDocument;
import com.magnamedia.entity.OfficeStaffTodo;
import com.magnamedia.helper.*;
import com.magnamedia.module.type.OfficeStaffDocumentType;
import com.magnamedia.module.type.OfficeStaffTodoType;
import com.magnamedia.module.type.OfficeStaffType;
import com.magnamedia.module.type.SalaryCurrency;
import com.magnamedia.repository.*;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.PendingManagerApprovalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.DayOfWeek;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;


/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 5/9/2020
 **/
@RestController
@RequestMapping("/officeStaffCandidate")
public class OfficeStaffCandidateController extends BaseRepositoryController<OfficeStaffCandidate> {

    Logger logger = Logger.getLogger(OfficeStaffCandidateController.class.getName());

    @Autowired
    private OfficeStaffCandidateRepository officeStaffCandidateRepository;

    @Autowired
    private OfficeStaffTodoRepository officeStaffTodoRepository;

    @Autowired
    private OfficeStaffRepository officeStaffRepository;

    @Autowired
    private PublicPageHelper publicPageHelper;

    @Autowired
    private MessageTemplateService messageTemplateService;

    @Autowired
    private PendingManagerApprovalService pendingManagerApprovalService;

    @Override
    public BaseRepository<OfficeStaffCandidate> getRepository() {
        return officeStaffCandidateRepository;
    }

    @Override
    protected ResponseEntity<?> createEntity(OfficeStaffCandidate entity) {
        if(entity.getSalary() < 0.0 || entity.getHousing() < 0.0 || entity.getTransportation() < 0.0
        || entity.getBasicSalary() < 0.0)
            throw new BusinessException("Salary/Salary Components can't be negative!");

        if (!entity.getSalary().equals(entity.getBasicSalary() + entity.getHousing() + entity.getTransportation()))
            throw new BusinessException("Salary must be equals to Basic Salary + Housing + Transportation");
        return super.createEntity(entity);
    }

    @Override
    protected ResponseEntity<?> updateEntity(OfficeStaffCandidate entity) {
        if(entity.getSalary() < 0.0 || entity.getHousing() < 0.0 || entity.getTransportation() < 0.0
                || entity.getBasicSalary() < 0.0)
            throw new BusinessException("Salary/Salary Components can't be negative!");

        if (!entity.getSalary().equals(entity.getBasicSalary() + entity.getHousing() + entity.getTransportation()))
            throw new BusinessException("Salary must be equals to Basic Salary + Housing + Transportation");
        return super.updateEntity(entity);
    }

    public OfficeStaffCandidate updateEntity(ObjectNode objectNode) throws IOException {
        OfficeStaffCandidate updated = parse(objectNode);
        OfficeStaffCandidate origin = getRepository().findOne(updated.getId());

        Map<OfficeStaffDocumentType, List<Attachment>> attachmentsMap = new HashMap<>();
        if(updated.getDocuments() != null  && !updated.getDocuments().isEmpty()) {
            for (OfficeStaffDocument document : updated.getDocuments()){
                List<Attachment> neededAttachments = new ArrayList<>();
                for (Attachment attachment : document.getAttachments()) {
                    attachment = Setup.getRepository(AttachementRepository.class)
                            .findOne(attachment.getId());
                    neededAttachments.add(Storage.storeTemporary(attachment.getName(), Storage.getStream(attachment), attachment.getTag(), true));
                }
                attachmentsMap.put(document.getType(), neededAttachments);
            }
            if (origin.getDocuments() != null && !origin.getDocuments().isEmpty()) {
                Setup.getRepository(OfficeStaffDocumentRepository.class)
                        .delete(origin.getDocuments());
            }

            origin.setDocuments(new ArrayList<>());
            for(OfficeStaffDocument doc: updated.getDocuments()) {
                doc.setOfficeStaffCandidate(origin);
                doc.setAttachments(attachmentsMap.get(doc.getType()));
                Setup.getRepository(OfficeStaffDocumentRepository.class).save(doc);
                origin.getDocuments().add(doc);
            }
        }

        // Don't update candidate's documents using generic update method
        if(objectNode.has("documents")) {
            objectNode.remove("documents");
        }

        update(origin, updated, objectNode);

        if(origin.getSelectedTransferDestination() != null) {
            Setup.getRepository(TransferDestinationRepository.class)
                    .save(origin.getSelectedTransferDestination());
        }

        updateEntity(origin);
        return origin;
    }

    @RequestMapping(value = "/saveNewEmployee", method = RequestMethod.POST)
    @PreAuthorize("hasPermission('officeStaffCandidate','saveNewEmployee')")
    @ResponseBody
    @Transactional
    public ResponseEntity<?> saveNewEmployee(@RequestParam(value = "fromOfficeStaffListPage", required = false, defaultValue = "false") boolean fromOfficeStaffLis,@RequestBody ObjectNode objectNode) throws IOException {

        OfficeStaffCandidate updated = null;
        OfficeStaffCandidate origin = null;

        //update OfficeStaffCandidate
        if(!fromOfficeStaffLis) {
            updated = parse(objectNode);
            origin = getRepository().findOne(updated.getId());
            update(origin, updated, objectNode);
        }else{
            origin = parse(objectNode);
            origin.setSource("ERP System");
            create(origin);
        }

        if(origin.getEmployeeType() == OfficeStaffType.DUBAI_STAFF_EMARATI) {
            origin.setNationality(
                    PicklistHelper.getItem("nationalities", "emirati")
            );
        }

        if(origin.getEmployeeType() == OfficeStaffType.DUBAI_STAFF_EMARATI
        || origin.getEmployeeType() == OfficeStaffType.DUBAI_STAFF_EXPAT) {
            origin.setCountry(
                    PicklistHelper.getItem("countries", "uae")
            );
            origin.setCityName("Dubai");
            origin.setCity(Setup.getItem("payroll_cities", "dubai"));
            origin.setSalaryCurrency(SalaryCurrency.AED);
        }

        if(origin.getEmployeeType() == OfficeStaffType.OVERSEAS_STAFF) {
            origin.getWeeklyOffDayList().add(DayOfWeek.FRIDAY);
        }

        if(origin.getEmployeeType() == OfficeStaffType.DUBAI_STAFF_EXPAT) {
            origin.setVisaRequired(true);
        }
        // to fetch jobTitle
        PicklistItem jobTitle = Setup.getRepository(PicklistItemRepository.class).findOne(origin.getJobTitle().getId());
        if(jobTitle != null) {
            origin.setJazzHRJobTitle(jobTitle.getName());
        }
        if(origin.getEmployeeManager() != null) {
            origin.setEmployeeManager(officeStaffRepository.findOne(origin.getEmployeeManager().getId()));
        }

        // Normalize phone numbers
        if (origin.getPhoneNumber() != null && !origin.getPhoneNumber().isEmpty()) {
            String normalizedPhoneNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(origin.getPhoneNumber());
            if (!normalizedPhoneNumber.equals(origin.getPhoneNumber())) {
                origin.setPhoneNumber(normalizedPhoneNumber);
            }
            origin.setPhoneNumber(PhoneNumberUtil.normalizeInternationalPhoneNumber(origin.getPhoneNumber()));
        }

        if (origin.getEmergencyContactPhoneNumber() != null && !origin.getEmergencyContactPhoneNumber().isEmpty()) {
            String normalizedEmergencyNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(origin.getEmergencyContactPhoneNumber());
            if (!normalizedEmergencyNumber.equals(origin.getEmergencyContactPhoneNumber())) {
                origin.setEmergencyContactPhoneNumber(normalizedEmergencyNumber);
            }
        }

        updateEntity(origin);


        //send Approval Message to Final Manager
        OfficeStaffTodo todo = officeStaffTodoRepository.findFirstByTaskNameAndCandidateOrderByCreationDateDesc(OfficeStaffTodoType.HIRE_EMPLOYEE.toString(), origin);
        OfficeStaff officeStaff = origin.getFinalManager();
        if (officeStaff == null || officeStaff.getPhoneNumber() == null) {
            throw new BusinessException("Can't find Final Manager or the Manager doesn't have phone number!");
        } else {
            String url = "";
            String startingDateInfo = (OfficeStaffType.DUBAI_STAFF_EMARATI.equals(origin.getEmployeeType()) || OfficeStaffType.DUBAI_STAFF_EXPAT.equals(origin.getEmployeeType())) ? DateUtil.formatFullDate(origin.getPotentialStartDate()): DateUtil.formatFullDate(origin.getStartingDate());
            if(fromOfficeStaffLis) {
                url = publicPageHelper.generatePublicURLWithoutShorten(PublicPageHelper.FINAL_MANAGER_APPROVE, origin.getId().toString() + "#Payroll_New_Hire_Approval_From_OfficeStaff_List_Page", String.valueOf(officeStaff.getUser().getId()));
                pendingManagerApprovalService.insertNewPendingApprovalRequest(origin.getEmployeeName(), startingDateInfo,origin.getSalaryWithCurrency(), jobTitle != null ? jobTitle.getName() : "", origin.getEmployeeManager(), officeStaff, (origin.getSalaryCurrency() != null ? origin.getSalaryCurrency().name() + " " :"") + NumberFormatter.formatNumber(origin.getSalary()), "New Hire", origin.getNotes(), url, origin.getId().toString() + "#Payroll_New_Hire_Approval_From_OfficeStaff_List_Page");
            }
            else {
                url = publicPageHelper.generatePublicURLWithoutShorten(PublicPageHelper.FINAL_MANAGER_APPROVE, todo.getId().toString() + "#Payroll_New_Hire_Approval", String.valueOf(officeStaff.getUser().getId()));
                pendingManagerApprovalService.insertNewPendingApprovalRequest(origin.getEmployeeName(),startingDateInfo,origin.getSalaryWithCurrency(), jobTitle != null ? jobTitle.getName() : "", origin.getEmployeeManager(), officeStaff, (origin.getSalaryCurrency() != null ? origin.getSalaryCurrency().name() + " " :"") + NumberFormatter.formatNumber(origin.getSalary()), "New Hire", origin.getNotes(), url, todo.getId().toString() + "#Payroll_New_Hire_Approval");
            }
//            Map<String, String> paramValues = new HashMap<>();
//            paramValues.put("employee_name", origin.getFirstLastName());
//            paramValues.put("job_title", jobTitle.getName());
//            paramValues.put("salary", NumberFormatter.formatNumber(origin.getSalary()) + " " + origin.getSalaryCurrency());
//            paramValues.put("start_date", DateUtil.formatClientFullDate(origin.getStartingDate()));
//            paramValues.put("url", url);
//
//            SmsResponse smsResponse = messageTemplateService.sendMessageOrEmail(
//                    "New Hire",
//                    normalizePhoneNumber(officeStaff.getPhoneNumber()),
//                    officeStaff.getEmail(),
//                    SmsReceiverType.Office_Staff,
//                    officeStaff.getId(),
//                    officeStaff.getName(),
//                    "Payroll_New_Hire_Approval",
//                    paramValues,
//                    null,
//                    officeStaff.getPreferredCommunicationMethod());
//
//            if (smsResponse == null || !smsResponse.isSuccess())
//                throw new RuntimeException("Failed to send the Approval Message to the Final Manager");
        }

        //set the HIRE_EMPLOYEE To-Do as completed
        if(!fromOfficeStaffLis) {
            todo.setCompleted(true);
            officeStaffTodoRepository.save(todo);
        }

        return ResponseEntity.ok("Success");
    }


}