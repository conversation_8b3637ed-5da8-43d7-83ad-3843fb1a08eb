package com.magnamedia.helper;

import org.joda.time.DateTime;

import java.text.DateFormat;
import java.text.DateFormatSymbols;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import static java.util.Calendar.*;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on Nov 29, 2017
 */
public class DateUtil {

    private static final DateFormat fullDateFormat = new SimpleDateFormat(
            "dd MMMM, yyyy");
    private static final DateFormat fullDayMonthFormat = new SimpleDateFormat(
            "EEEE, MMMM dd");
    private static final DateFormat clientFullDateFormat = new SimpleDateFormat(
            "EEEE, MMMM dd, YYYY");
    private static final DateFormat clientDateFormatWithoutYear = new SimpleDateFormat(
            "EEEE, MMMM dd");
    private static final DateFormat monthDayYearFormat = new SimpleDateFormat(
            "MMMM dd, YYYY");
    private static final DateFormat fullDateFormatWithTime = new SimpleDateFormat(
            "dd MMMM, yyyy HH:mm:ss.SSS");
    private static final DateFormat clientFullDateFormatWithTime = new SimpleDateFormat(
            "EEEE, MMMM dd, YYYY HH:mm:ss.SSS");
    private static final DateFormat dashedDateFormatWithTime = new SimpleDateFormat(
            "yyyy-MM-dd hh:mm:ss");
    private static final DateFormat dashedYearMonthFormat = new SimpleDateFormat(
            "yyyy-MM");
    private static final DateFormat dashedDateFormatWithTimeV2 = new SimpleDateFormat(
            "yyyy-MM-dd hh:mm:ss");
    private static final DateFormat simpleMonthYearFormat = new SimpleDateFormat(
            "MMM yyyy");
    private static final DateFormat fullFormatWithoutSecond = new SimpleDateFormat(
            "dd MMM yyyy HH:mm");
    private static final DateFormat connectedMonthYearFormat = new SimpleDateFormat(
            "MMM yyyy");
    private static final DateFormat dayMonthDayYearFormat = new SimpleDateFormat(
            "EEE, MMMMM dd,yyyy");
    private static final DateFormat monthFormat = new SimpleDateFormat(
            "MMMM");
    private static final DateFormat fullMonthFullYearFormat = new SimpleDateFormat(
            "MMMM yyyy");
    private static final DateFormat fullMonthDashedFullYearFormat = new SimpleDateFormat(
            "MMMM - yyyy");
    private static final DateFormat monthFormatWithTime = new SimpleDateFormat(
            "MMMM hh:mm:ss");
    private static final DateFormat monthFormatWithSimpleTime = new SimpleDateFormat(
            "MMMM mm:ss");
    private static final DateFormat simpleMonthFormat = new SimpleDateFormat(
            "MMM");
    private static final DateFormat dashedDateFormat = new SimpleDateFormat(
            "yyyy-MM-dd");
    private static final DateFormat dashedDateFormatV2 = new SimpleDateFormat(
            "dd-MMM-yyyy");
    private static final DateFormat slashedDateFormat = new SimpleDateFormat(
            "dd/MM/yyyy");
    private static final DateFormat slashedDateFormatV2 = new SimpleDateFormat(
            "MM/dd/yyyy");
    private static final DateFormat simpleTimeFormat = new SimpleDateFormat(
            "HH:mm");
    private static final DateFormat timeFormat = new SimpleDateFormat(
            "HH:mm:ss");
    private static final DateFormat simpleTimeFormatAMPM = new SimpleDateFormat(
            "HH:mm a");
    private static final DateFormat dayFormat = new SimpleDateFormat(
            "dd");

    static {
        dashedDateFormat.setLenient(false);
        dashedDateFormatWithTime.setLenient(false);
    }

    public static boolean isThisDateValid(Object dateToValidate, String dateFromat) {

        if ((dateToValidate == null) || (!(dateToValidate instanceof String))) {
            return false;
        }

        String dateToValidateStr = (String) dateToValidate;
        SimpleDateFormat sdf = new SimpleDateFormat(dateFromat);
        sdf.setLenient(false);

        try {
            //if not valid, it will throw ParseException
            Date d = sdf.parse(dateToValidateStr);
            System.out.println(d);

        } catch (ParseException e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

    public static String getDateFormatDDMM(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd MMMM");
        return sdf.format(date);
    }

    public static String getTimeFormatHHMMA(Date date) {
        return simpleTimeFormatAMPM.format(date);
    }

    public static Date getStartDayValue(Date dateValue) {
        DateTime dateTimeValue = new DateTime(dateValue);
        return new Date(dateTimeValue.withTimeAtStartOfDay().getMillis());
    }

    public static Date getEndDayValue(Date dateValue) {
        DateTime dateTimeValue = new DateTime(dateValue).plusDays(1);
        return new Date(dateTimeValue.withTimeAtStartOfDay().getMillis() - 1);
    }

    public static Date getFirstOfMonthDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(calendar.get(YEAR), calendar.get(MONTH), 1, 0, 0, 0);
        return calendar.getTime();
    }

    public static Date getLastOfMonthDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    public static Date getDayOfMonthDate(Date date, Integer day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(calendar.get(YEAR), calendar.get(MONTH), day, 0, 0, 0);
        return calendar.getTime();
    }

    public static java.sql.Date getFirstOfMonthDateSQL(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(calendar.get(YEAR), calendar.get(MONTH), 1, 0, 0, 0);
        return new java.sql.Date(calendar.getTime().getTime());
    }

    public static Integer secondsBetween(Date d1, Date d2) {
        long seconds = Math.abs(d1.getTime() - d2.getTime()) / 1000;
        return (int) seconds;
    }

    public static String getMonthForInt(int num) {
        String month = "wrong";
        DateFormatSymbols dfs = new DateFormatSymbols();
        String[] months = dfs.getMonths();
        if (num >= 0 && num <= 11) {
            month = months[num];
        }
        return month;
    }

    public static Date getTodayDate() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static Date now() {
        Calendar cal = Calendar.getInstance();
        return cal.getTime();
    }

    public static Date getDateFromDateTime(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static Integer getDaysBetween(Date date1, Date date2) {
        try {
            Integer diff = (int) TimeUnit.DAYS.convert(
                    date2.getTime() - date1
                            .getTime(), TimeUnit.MILLISECONDS);
            return diff;
        } catch (Exception e) {
            return 0;
        }
    }

    public static Integer getHoursBetween(Date date1, Date date2) {
        try {
            Integer diff = (int) TimeUnit.HOURS.convert(
                    date2.getTime() - date1
                            .getTime(), TimeUnit.MILLISECONDS);
            return diff;
        } catch (Exception e) {
            return 0;
        }
    }

    public static Date getPreviousDays(Date d, Integer days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(DATE,
                -days);
        return cal.getTime();
    }

    public static Date getPreviousMonths(Date d, Integer months) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(MONTH,
                -months);
        return cal.getTime();
    }

    public static Date addHours(Date d, Integer hours) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.HOUR_OF_DAY,
                hours);
        return cal.getTime();
    }

    public static Date getNextDateAtHour(Integer hours) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.set(Calendar.HOUR_OF_DAY,
                hours);
        Date dateAtHour = cal.getTime();

        if (dateAtHour.compareTo(new Date()) >= 0 )
            return dateAtHour;
        else {
            cal.add(DATE, 1);
            return cal.getTime();
        }
    }

    public static Date addMinutes(Date d, Integer minutes) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.MINUTE,
                minutes);
        return cal.getTime();
    }

    public static Date addDays(Date d, Integer days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(DATE,
                days);
        return cal.getTime();
    }

    public static java.sql.Date addDaysSql(java.sql.Date d, Integer days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(DATE,
                days);
        return new java.sql.Date(cal.getTime().getTime());
    }

    public static Date addMonths(Date d, Integer months) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(MONTH,
                months);
        return cal.getTime();
    }

    public static java.sql.Date addMonthsSql(java.sql.Date d, Integer months) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(MONTH,
                months);
        return new java.sql.Date(cal.getTime().getTime());
    }

    public static Date addYears(Date d, Integer years) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(YEAR,
                years);
        return cal.getTime();
    }

    public static Date getDate(int year, int month, int day, int hour, int minute, int seconds) {
        Calendar cal = Calendar.getInstance();
        cal.set(year, month, day, hour, minute, seconds);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static Date getDate(int year, int month, int day) {
        Calendar cal = Calendar.getInstance();
        cal.set(year, month, day);
        return cal.getTime();
    }

    public static String getOrdinalSuffix(Integer day) {
        switch (day) {
            case 1:
                return "st";
            case 2:
                return "nd";
            case 3:
                return "rd";
            default:
                return "th";
        }
    }

    //Jirra ACC-1092
    public static Date getDayStart(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    //Jirra ACC-1182

    public static Date getDayEnd(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.set(Calendar.HOUR_OF_DAY, c.getActualMaximum(Calendar.HOUR_OF_DAY));
        c.set(Calendar.MINUTE, c.getActualMaximum(Calendar.MINUTE));
        c.set(Calendar.SECOND, c.getActualMaximum(Calendar.SECOND));
        c.set(Calendar.MILLISECOND, c.getActualMaximum(Calendar.MILLISECOND));
        return c.getTime();
    }

    public static String formatFullDate(Object d) {
        if (d == null)
            return "";
        return fullDateFormat.format(d);
    }

    public static String formatFullDayMonthDate(Object d) {
        return fullDayMonthFormat.format(d);
    }

    public static String formatClientFullDate(Object d) {
        return clientFullDateFormat.format(d);
    }

    public static String formatClientWithoutYearDate(Object d) {
        if (d == null)
            return "";
        return clientDateFormatWithoutYear.format(d);
    }

    public static String formatMonthDayYear(Object d) {
        return monthDayYearFormat.format(d);
    }

    public static String formatFullDateWithTime(Object d) {
        return fullDateFormatWithTime.format(d);
    }

    public static String formatClientFullDateWithTime(Object d) {
        return clientFullDateFormatWithTime.format(d);
    }

    public static String formatDateDashed(Object date) {
        if (date == null) {
            return null;
        }
        return dashedDateFormat.format(date);
    }

    public static String formatDateDashedV2(Date date) {
        if (date == null) {
            return null;
        }
        return dashedDateFormatV2.format(date);
    }

    public static String formatDateDashedWithTime(Date date) {
        if (date == null) {
            return null;
        }
        return dashedDateFormatWithTime.format(date);
    }

    public static String formatYearMonthDashed(Date date) {
        if (date == null) {
            return null;
        }
        return dashedYearMonthFormat.format(date);
    }

    public static String formatDateDashedWithTimeV2(Date date) {
        if (date == null) {
            return null;
        }
        return dashedDateFormatWithTimeV2.format(date);
    }


    public static String formatMonth(Date date) {
        if (date == null) {
            return null;
        }
        return monthFormat.format(date);
    }

    public static String formatDaySuffixMonth(Date date) {
        if (date == null) {
            return null;
        }
        int day = new org.joda.time.LocalDate(date).getDayOfMonth();
        return day + getOrdinalSuffix(day) + " " + monthFormat.format(date);
    }

    public static String formatFullMonthFullYear(Date date) {
        if (date == null) {
            return null;
        }
        return fullMonthFullYearFormat.format(date);
    }

    public static String formatFullMonthDashedFullYear(Date date) {
        if (date == null) {
            return null;
        }
        return fullMonthDashedFullYearFormat.format(date);
    }

    public static String formatMonthWithTime(Date date) {
        if (date == null) {
            return null;
        }
        return monthFormatWithTime.format(date);
    }

    public static String formatMonthWithSimpleTime(Date date) {
        if (date == null) {
            return null;
        }
        return monthFormatWithSimpleTime.format(date);
    }

    public static String formatSimpleMonth(Date date) {
        if (date == null) {
            return null;
        }
        return simpleMonthFormat.format(date);
    }

    public static String formatFullWithoutSecond(Date date) {
        if (date == null) {
            return null;
        }
        return fullFormatWithoutSecond.format(date);
    }

    public static String formatSimpleMonthYear(Date date) {
        if (date == null) {
            return null;
        }
        return simpleMonthYearFormat.format(date);
    }

    public static String formatConnectedMonthYear(Date date) {
        if (date == null) {
            return null;
        }
        return connectedMonthYearFormat.format(date);
    }

    public static String formatDayMonthDayYear(Date date) {
        if (date == null) {
            return null;
        }
        return dayMonthDayYearFormat.format(date);
    }

    public static String formatDateSlashed(Date date) {
        if (date == null) {
            return null;
        }
        return slashedDateFormat.format(date);
    }

    public static String formatDateSlashedV2(Date date) {
        if (date == null) {
            return null;
        }
        return slashedDateFormatV2.format(date);
    }

    public static String formatSimpleTime(Date date) {
        if (date == null) {
            return null;
        }
        return simpleTimeFormat.format(date);
    }

    public static String formatTime(Date date) {
        if (date == null) {
            return null;
        }
        return timeFormat.format(date);
    }

    public static Date parseFullDate(String date) throws ParseException {
        if (date == null) {
            return null;
        }
        return fullDateFormat.parse(date);
    }

    public static Date parseClientFullDate(String date) throws ParseException {
        if (date == null) {
            return null;
        }
        return clientFullDateFormat.parse(date);
    }

    public static Date parseFullDateWithTime(String date) throws ParseException {
        if (date == null) {
            return null;
        }
        return fullDateFormatWithTime.parse(date);
    }

    public static Date parseClientFullDateWithTime(String date) throws ParseException {
        if (date == null) {
            return null;
        }
        return clientFullDateFormatWithTime.parse(date);
    }

    public static Date parseDateDashed(String date) throws ParseException {
        if (date == null) {
            return null;
        }
        return dashedDateFormat.parse(date);
    }

    public static java.sql.Date parseDateDashedSql(String date) {
        if (date == null) {
            return null;
        }
        try {
            return new java.sql.Date(dashedDateFormat.parse(date).getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Date parseDateSlashed(String date) throws ParseException {
        if (date == null) {
            return null;
        }
        return slashedDateFormat.parse(date);
    }

    public static Date parseDateDashedV2(String date) throws ParseException {
        if (date == null) {
            return null;
        }
        return dashedDateFormatV2.parse(date);
    }

    public static Date parseDateTimeDashed(String date) throws ParseException {
        if (date == null) {
            return null;
        }
        return dashedDateFormatWithTime.parse(date);
    }

    public static DateFormat getDashedDateFormat() {
        return dashedDateFormat;
    }

    public static DateFormat getDashedDateFormatWithTime() {
        return dashedDateFormatWithTime;
    }

    public static DateFormat getDashedDateFormatWithTimeV2() {
        return dashedDateFormatWithTimeV2;
    }

    public static Date toDate(LocalDate date) {
        return Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDate toLocalDate(Date date) {

        return new Date(date.getTime()).toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
    }

    public static int getDiffYears(Date first, Date last) {
        Calendar a = Calendar.getInstance();
        a.setTime(first);

        Calendar b = Calendar.getInstance();
        b.setTime(last);

        int diff = b.get(YEAR) - a.get(YEAR);
        if (a.get(MONTH) > b.get(MONTH) ||
                (a.get(MONTH) == b.get(MONTH) && a.get(DATE) > b.get(DATE))) {
            diff--;
        }
        return diff;
    }

    public static int getDiffMonths(Date first, Date last) {
        Calendar c1 = Calendar.getInstance();
        c1.setTime(first);
        Calendar c2 = Calendar.getInstance();
        c2.setTime(last);
        int diff = 0;
        if (c2.after(c1)) {
            while (c2.after(c1)) {
                c1.add(MONTH, 1);
                if (c2.after(c1)) {
                    diff++;
                }
            }
        } else if (c2.before(c1)) {
            while (c2.before(c1)) {
                c1.add(MONTH, -1);
                if (c1.before(c2)) {
                    diff--;
                }
            }
        }
        return diff;
    }

    public static String formatDay(Date date) {
        if (date == null) {
            return null;
        }
        return dayFormat.format(date);
    }

    public static String formatMMMMDD(Date date) {
        if (date == null) {
            return null;
        }

        String monthName = DateUtil.formatMonth(date);
        String day = DateUtil.formatDay(date);
        return monthName.substring(0, 3) + "-" + day;
    }

    public static int getNumDaysOfMonth(Date date) {
        if (date == null)
            return 0;

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getActualMaximum(DAY_OF_MONTH);
    }

    public static Date getNextPaymentDate(){
        Date current = new Date();

        org.joda.time.LocalDate  currentLd = new org.joda.time.LocalDate(current);
        org.joda.time.LocalDate  ld7 = new org.joda.time.LocalDate(current).withDayOfMonth(7);
        org.joda.time.LocalDate  ld14 = new org.joda.time.LocalDate(current).withDayOfMonth(14);
        org.joda.time.LocalDate  ld21 = new org.joda.time.LocalDate(current).withDayOfMonth(21);
        org.joda.time.LocalDate  ld3 = new org.joda.time.LocalDate(current).plusMonths(1).withDayOfMonth(3);

        if(currentLd.isBefore(ld7))
            return ld7.toDate();
        else if (currentLd.compareTo(ld7) >= 0  && currentLd.isBefore(ld14))
            return ld14.toDate();
        else if (currentLd.compareTo(ld14) >= 0  && currentLd.isBefore(ld21))
            return ld21.toDate();
        else
            return ld3.toDate();
    }

    public static String getDayOfWeek(int numberOfDay){
        switch (numberOfDay){
            case 0 :{
                return "SUNDAY";
            }
            case 1 :{
                return "MONDAY";
            }
            case 2 :{
                return "TUESDAY";
            }
            case 3 :{
                return "WEDNESDAY";
            }
            case 4 :{
                return "THURSDAY";
            }
            case 5 :{
                return "FRIDAY";
            }
            case 6 :{
                return "SATURDAY";
            }
        }
        return "";
    }

    public static Long difBet2DateInHOUR(Date d1, Date d2) {
        long diff = Math.abs(d1.getTime() - d2.getTime()); // Difference in milliseconds
        long diffHOUR = diff / (60 * 60 * 1000);
        return diffHOUR;
    }

    public static Double difBet2DateInHOURDouble(Date d1, Date d2) {
        long diff = Math.abs(d1.getTime() - d2.getTime()); // Difference in milliseconds
        double diffHOUR = diff / (60.0 * 60 * 1000); // Convert to hours (Double)
        return diffHOUR;
    }

    public static int remainingHoursTillNextDayAt(int hour) {
        final int h = Math.max(0, Math.min(23, hour));
        final Date nextDayAt9 = DateUtil.getNextDateAtHour(h);
        return DateUtil.difBet2DateInHOUR(new Date(), nextDayAt9).intValue();
    }

    public static Date dateAt(Date date, int hour, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, Math.min(23, Math.max(0, hour)));
        calendar.set(Calendar.MINUTE, Math.min(59, Math.max(0, minute)));
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date todayAt(int hour, int minute) {
        return dateAt(new Date(), hour, minute);
    }

    public static Date getEndOfMonthDate(Date date) {
        return new org.joda.time.LocalDate(date).dayOfMonth().withMaximumValue().toDate();
    }

    public static int getDaysOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(DAY_OF_MONTH);
    }

    public static Date parseISODate(String isoDateString) throws DateTimeParseException {
        OffsetDateTime offsetDateTime = OffsetDateTime.parse(isoDateString, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
        Instant instant = offsetDateTime.toInstant();
        Date date = Date.from(instant);
        return date;
    }


    public static Date getTodayAt(int hour, int minute) {
        Calendar today = Calendar.getInstance();
        today.set(Calendar.HOUR_OF_DAY, hour);
        today.set(Calendar.MINUTE, minute);
        today.set(Calendar.SECOND, 0);
        today.set(Calendar.MILLISECOND, 0);
        return today.getTime();
    }
}
