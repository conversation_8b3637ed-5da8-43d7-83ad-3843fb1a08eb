package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.EmployeeLoan;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.Repayment;
import com.magnamedia.entity.projection.HousemaidRepaymentProjectionV2;
import com.magnamedia.entity.projection.RepaymentProjection;
import com.magnamedia.extra.RepaymentStatus;
import com.magnamedia.extra.payroll.init.HousemaidAmountProjection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Esrawi <<EMAIL>>
 * Created at Dec 13, 2017
 */
@Repository
public interface RepaymentRepository extends BaseRepository<Repayment> {

    @Query("select r from Repayment r join r.housemaid h where r.paidRepayment=true and r.exculdedFromPayroll=false and r.repaymentDate >= ?2 and r.repaymentDate < ?3 " +
            "and h.id in ?1")
    List<Repayment> findCurrentMonthRepayments(List<Long> ids, Date start, Date end);

    List<Repayment> findByHousemaid(Housemaid housemaid);

    @Query("SELECT MAX(r.id) as lastId, SUM(r.amount) as amount, r.repaymentDate as repaymentDate, s.salaryCurrency as salaryCurrency, MAX(r.paidRepayment) as paidRepayment, " +
            " Max(r.employeeLoan.doNotDeductFromSalary) as doNotDeductFromSalary , MAX(r.status) as status,r.currency as currency FROM Repayment r join r.officestaff s WHERE s.id = ?1 and r.employeeLoan is not null group by r.repaymentDate, r.officestaff")
    List<RepaymentProjection> getRepaymentsByOfficestaff(Long officeStaff_id);

    @Query("SELECT MAX(r.id) as lastId, SUM(r.amount) as amount, r.repaymentDate as repaymentDate, s.salaryCurrency as salaryCurrency, MAX(r.paidRepayment) as paidRepayment,Max(r.employeeLoan.doNotDeductFromSalary) as doNotDeductFromSalary , MAX(r.status) as status FROM Repayment r join r.officestaff s WHERE s.id = ?1 and (r.repaymentDate >= ?2 and r.repaymentDate <= ?3 ) and r.employeeLoan is not null group by r.repaymentDate, r.officestaff")
    RepaymentProjection getRepaymentsByOfficestaffAndRepaymentDateBetween(Long officeStaff_id, Date repaymentDateStart, Date repaymentDateEnd);

    List<Repayment> findByOfficestaff(OfficeStaff officeStaff);

    @Query("SELECT SUM (e.amount) FROM Repayment e WHERE e.housemaid = ?1 AND e.paidRepayment=true")
    public Double sumRepaymentsByMaid(Housemaid maid);

    @Query("SELECT SUM (e.amount) FROM Repayment e WHERE e.housemaid IN ?1 AND e.paidRepayment=true")
    public Double sumMaidsRepayments(List<Housemaid> maids);

    @Query("SELECT new com.magnamedia.extra.payroll.init.HousemaidAmountProjection(h.id, SUM (r.amount)) FROM Repayment r inner join r.housemaid h where h.id in ?1 AND r.paidRepayment=true group by h.id")
    List<HousemaidAmountProjection> housemaidRepaymentsAmount(List<Long> housemaids);

    @Query("SELECT SUM (r.amount) FROM Repayment r where r.housemaid = ?1 AND r.paidRepayment=true group by r.housemaid")
    Double housemaidRepaymentsAmountByHousemaid(Housemaid housemaid);

    @Query("SELECT new com.magnamedia.extra.payroll.init.HousemaidAmountProjection(h.id, SUM (r.amount)) FROM Repayment r inner join r.housemaid h where h.id in ?1 AND r.paidRepayment=true and r.repaymentDate < ?2 group by h.id")
    List<HousemaidAmountProjection> housemaidRepaymentsAmount(List<Long> housemaids, Date date);

    @Query("SELECT new com.magnamedia.extra.payroll.init.HousemaidAmountProjection(h.id, SUM (r.amount)) FROM Repayment r inner join r.housemaid h where h.id in ?1 AND r.paidRepayment=true " +
            "and r.exculdedFromPayroll=false and r.repaymentDate >= ?2 and r.repaymentDate < ?3 group by h.id")
    List<HousemaidAmountProjection> housemaidRepaymentsAmountForCurrentMonth(List<Long> housemaids, Date start, Date end);

    @Query("SELECT SUM (r.amount) FROM Repayment r where r.housemaid = ?1 AND r.paidRepayment=true " +
            "and r.exculdedFromPayroll=false and r.repaymentDate >= ?2 and r.repaymentDate < ?3 and r.dontConsiderAsDeduction = ?4 group by r.housemaid")
    Double housemaidRepaymentsAmountForCurrentMonthByHousemaid(Housemaid housemaid, Date start, Date end, boolean dontConsiderAsDeduction);

    @Query("SELECT SUM (e.amount) FROM Repayment e WHERE e.housemaid = ?1 AND e.paidRepayment=true AND e.repaymentDate < ?2")
    public Double sumRepaymentsByMaid(Housemaid maid, Date repaymentDate);

    //Jirra ACC-627 ACC-1019
    public Page<Repayment> findByRepaymentDateGreaterThanEqualAndRepaymentDateLessThanAndHousemaidNotNull(
            Date startDate, Date endDate, Pageable pageable);

    //Jirra ACC-805 ACC-1019
    public Page<Repayment> findByRepaymentDateGreaterThanEqualAndRepaymentDateLessThanAndHousemaidNameContainingAndHousemaidNotNull(
            Date startDate, Date endDate, String name, Pageable pageable);

    @Query("SELECT SUM (e.amount) FROM Repayment e WHERE e.officestaff = ?1 AND e.paidRepayment=true")
    public Double sumRepaymentsByOfficeStaff(OfficeStaff staff);

    //Jirra ACC-463
    @Query(nativeQuery = true,
            value = "SELECT SUM(AMOUNT) FROM REPAYMENTS WHERE HOUSEMAID_ID IS NOT NULL AND REPAYMENT_DATE>=?1 AND REPAYMENT_DATE<?2")
    public BigInteger sumRepaymentsByDatesBetween(Date loanDate1, Date loanDate2);

    @Query(nativeQuery = true,
            value = "SELECT SUM(AMOUNT) FROM REPAYMENTS WHERE HOUSEMAID_ID IS NOT NULL AND (HOUSEMAID_ID NOT IN ?1) AND REPAYMENT_DATE<?2")
    public BigInteger sumRepaymentsByMaidNotInAndDateBefore(List<Long> maids, Date loanDate);

    @Query(nativeQuery = true,
            value = "SELECT SUM(AMOUNT) FROM REPAYMENTS WHERE HOUSEMAID_ID IS NOT NULL AND REPAYMENT_DATE<?1")
    public BigInteger sumRepaymentsByDateBefore(Date loanDate);

    @Query(nativeQuery = true,
            value = "SELECT SUM(AMOUNT) FROM REPAYMENTS WHERE HOUSEMAID_ID IS NOT NULL AND (HOUSEMAID_ID IN ?1) AND REPAYMENT_DATE<?2")
    public BigInteger sumRepaymentsByMaidInAndDateBefore(List<Long> maids, Date loanDate);

    //Jirra 771
    @Query(nativeQuery = true,
            value = "SELECT SUM(E.AMOUNT) FROM REPAYMENTS E INNER JOIN HOUSEMAIDS H ON E.HOUSEMAID_ID = H.ID "
                    + "WHERE (H.DATE_OF_TERMINATION IS NULL OR H.DATE_OF_TERMINATION >= ?1) "
                    + "AND (H.VISA_CANCELLATION_DATE IS NULL OR H.VISA_CANCELLATION_DATE >= ?1) "
                    + "AND (H.REJECT_DATE IS NULL OR H.REJECT_DATE >= ?1) "
                    + "AND REPAYMENT_DATE < ?1 ")
    public BigInteger sumRepaymentsOfNonTerminatedMaidsByDateBefore(Date loanDate);

    @Query(nativeQuery = true,
            value = "SELECT SUM(E.AMOUNT) FROM REPAYMENTS E INNER JOIN HOUSEMAIDS H ON E.HOUSEMAID_ID = H.ID "
                    + "WHERE (H.DATE_OF_TERMINATION IS NOT NULL AND H.DATE_OF_TERMINATION >= ?1 AND H.DATE_OF_TERMINATION < ?2) "
                    + "OR (H.VISA_CANCELLATION_DATE IS NOT NULL AND H.VISA_CANCELLATION_DATE >= ?1 AND H.VISA_CANCELLATION_DATE < ?2) "
                    + "OR (H.REJECT_DATE IS NOT NULL AND H.REJECT_DATE >= ?1 AND H.REJECT_DATE < ?2) ")
    public BigInteger sumRepaymentsOfLostLoansBecauseOfTerminationByDateBefore(Date loanDate1, Date loanDate2);

    //Jirra ACC-1085
    List<Repayment> findByNotFinal(boolean notFinal);

    List<Repayment> findByNotFinalAndHousemaid(boolean notFinal, Housemaid housemaid);

    void deleteByNotFinalAndHousemaidId(boolean notFinal, Long housemaid);

    @Modifying
    @Query("UPDATE Repayment r SET r.notFinal = false WHERE r.housemaid = ?1 and r.notFinal = true")
    int updateNotFinalNotesByHousemaid(Housemaid housemaid);

    @Query(value = "SELECT SUM(r.amount) FROM Repayment r WHERE r.employeeLoan = ?1 AND r.repaymentDate IS NOT NULL AND r.repaymentDate >= ?2")
    public Double sumAmountByEmployeeLoanAndRepaymentDateGreaterThanEqual(EmployeeLoan loan, Date repaymentDate);

    @Query(value = "SELECT SUM(r.amount) FROM Repayment r WHERE r.employeeLoan = ?1 AND r.paidRepayment = ?2")
    public Double sumAmountByEmployeeLoanAndPaidRepayment(EmployeeLoan loan, Boolean paidRepayment);

    @Query(value = "SELECT SUM(r.amount) FROM Repayment r WHERE r.employeeLoan = ?1 AND r.repaymentDate IS NOT NULL AND r.repaymentDate < ?2")
    public Double sumAmountByEmployeeLoanAndRepaymentDateLessThan(EmployeeLoan loan, Date repaymentDate);

    public void deleteByEmployeeLoanAndRepaymentDateGreaterThanEqual(EmployeeLoan loan, Date repaymentDate);

    public void deleteByEmployeeLoanAndPaidRepayment(EmployeeLoan loan, Boolean paidRepayment);

    public List<Repayment> findByOfficestaffIdAndRepaymentDateBetweenAndStatus(Long id, Date repaymentStartDate, Date repaymentEndDate, RepaymentStatus status);

    public void deleteByEmployeeLoan(EmployeeLoan employeeLoan);

    @Query("SELECT SUM(r.amount) FROM Repayment r join r.employeeLoan l WHERE r.officestaff= ?1 AND l.isEditable = true AND r.paidRepayment = true")
    public Double sumEditableLoansRepaymentsByOfficeStaff(OfficeStaff officeStaff);

    @Query("SELECT r FROM Repayment r WHERE r.officestaff= ?1 AND r.employeeLoan IS NOT NULL AND r.repaymentDate = ?2")
    public List<Repayment> getCurrentMonthRepayments(OfficeStaff officeStaff, Date repaymentDate);

    @Query("SELECT r.id FROM Repayment r WHERE r.employeeLoan IS NOT NULL AND r.repaymentDate = ?1")
    public List<Long> getCurrentMonthRepaymentsForAll(java.sql.Date repaymentDate);

    public Repayment findTopByEmployeeLoanAndPaidRepaymentOrderByRepaymentDateDesc(EmployeeLoan loan, Boolean paidRepayment);

    @Query("SELECT SUM(r.amount) FROM Repayment r WHERE r.housemaid IN ?1 AND r.repaymentDate < ?2 AND r.paidRepayment = true")
    public Double sumHousemaidsRepaymentsAmounts(List<Housemaid> housemaidList, Date currentMonthLockDate);

    @Query("SELECT SUM(r.amount) FROM Repayment r WHERE r.housemaid = ?1 AND r.repaymentDate < ?2 and r.paidRepayment = ?3")
    Double sumByHousemaidAndRepaymentDateLessThanAndPaidRepayment(Housemaid housemaid, Date date,Boolean paid);

    @Query("SELECT r.housemaid as housemaid, SUM(r.amount) as amount," +
            " r.amount as loanRepayments, r.repaymentDate as repaymentDate, r.description as description " +
            "   FROM  Repayment r " +
            "   WHERE r.housemaid = ?1 " +
            " GROUP BY r.repaymentDate, r.description ")
    List<HousemaidRepaymentProjectionV2> findRepaymentsByHousemaid(Housemaid housemaid);

    @Query("select sum(r.amount) from Repayment r inner join r.employeeLoan l " +
            "where l.doNotDeductFromSalary = false and r.repaymentDate = ?1 and r.officestaff = ?3 group by r.officestaff.id " +
            "having sum(r.amount) <> (select coalesce(sum(r2.amount),0) from Repayment r2 inner join r2.employeeLoan l2 where l2.doNotDeductFromSalary = false and r2.repaymentDate = ?2 " +
            "and r2.officestaff = ?3)")
    Double sumOfRepaymentsChanged(java.sql.Date currentPayrollMonth, java.sql.Date previousPayrollMonth, OfficeStaff staff);

    @Query("SELECT r FROM Repayment r " +
            "WHERE r.housemaid = ?1 AND r.repaymentDate >= ?2 AND r.repaymentDate <= ?3")
    List<Repayment> findByHousemaidAndRepaymentDate(Housemaid housemaid, Date startDate, Date endDate);

    @Query("SELECT r FROM Repayment r join r.employeeLoan l WHERE r.officestaff= ?1 AND l.isEditable = true AND r.paidRepayment = true")
    public List<Repayment> getAllEditableLoansRepaymentsByOfficeStaff(OfficeStaff officeStaff);

    @Query(value = "SELECT r FROM Repayment r WHERE r.employeeLoan = ?1 AND r.repaymentDate IS NOT NULL AND r.repaymentDate >= ?2")
    public List<Repayment> findRepaymentByEmployeeLoanAndRepaymentDateGreaterThanEqual(EmployeeLoan loan, Date repaymentDate);

    @Query("SELECT SUM (e.amount) FROM Repayment e WHERE e.housemaid IS NOT NULL AND e.repaymentDate between ?1 and ?2 ")
    public Double sumByRepaymentDate(Date repaymentDateFrom, Date repaymentDateTo);
}
