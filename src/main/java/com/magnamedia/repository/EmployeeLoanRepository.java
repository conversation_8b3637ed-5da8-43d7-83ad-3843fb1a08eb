package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.AbstractEmployeeLoan;
import com.magnamedia.entity.EmployeeLoan;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.projection.EmployeeLoanProjection;
import com.magnamedia.entity.projection.payrollAudit.CumulativeNewLoansAddedProjection;
import com.magnamedia.entity.projection.payrollAudit.SkippedLoanProjection;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.extra.LoansInformation;
import com.magnamedia.extra.payroll.init.HousemaidAmountProjection;
import com.magnamedia.helper.DateUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface EmployeeLoanRepository extends BaseRepository<EmployeeLoan> {

    List<EmployeeLoan> findByHousemaid(Housemaid housemaid);

    //Jirra ACC-1093
    List<EmployeeLoan> findByNotFinal(boolean notFinal);

    List<EmployeeLoan> findByNotFinalAndHousemaid(boolean notFinal, Housemaid housemaid);

    void deleteByNotFinalAndHousemaidId(boolean notFinal, Long housemaid);

    @Modifying
    @Query("UPDATE EmployeeLoan e SET e.notFinal = false WHERE e.housemaid = ?1 and e.notFinal = true")
    int updateNotFinalNotesByHousemaid(Housemaid housemaid);

    @Query("SELECT SUM (e.amount) FROM EmployeeLoan e WHERE e.housemaid = ?1 AND e.loanType = ?2")
    public Double sumLoansByMaidandType(Housemaid maid,
                                        EmployeeLoan.LoanType loanType);

    @Query("SELECT SUM (e.amount) FROM EmployeeLoan e WHERE e.housemaid = ?1")
    public Double sumLoansByMaid(Housemaid maid);

    @Query("SELECT SUM (e.amount) FROM EmployeeLoan e WHERE e.housemaid IN ?1")
    public Double sumMaidsLoans(List<Housemaid> maids);

    @Query("SELECT SUM (e.amount) FROM EmployeeLoan e WHERE e.housemaid = ?1 and e.loanDate < ?2 ")
    public Double sumLoansByMaidUntilDate(Housemaid maid, Date date);

    @Query("SELECT new com.magnamedia.extra.payroll.init.HousemaidAmountProjection(h.id, SUM (e.amount)) FROM EmployeeLoan e inner join e.housemaid h where h.id in ?1 group by h.id")
    List<HousemaidAmountProjection> housemaidLoansAmount(List<Long> housemaids);

    @Query("SELECT new com.magnamedia.extra.payroll.init.HousemaidAmountProjection(h.id, SUM (e.amount)) FROM EmployeeLoan e inner join e.housemaid h where h.id in ?1 and e.loanDate <= ?2 group by h.id")
    List<HousemaidAmountProjection> housemaidLoansAmount(List<Long> housemaids, Date date);

    @Query("SELECT SUM (e.amount) FROM EmployeeLoan e where e.housemaid = ?1 and e.loanDate <= ?2 group by e.housemaid")
    Double housemaidLoansAmountByHousemaid(Housemaid housemaid, Date date);

    @Query("SELECT SUM (e.amount) FROM EmployeeLoan e WHERE e.housemaid = ?1 AND e.loanDate< ?2")
    public Double sumLoansByMaid(Housemaid maid, Date loanDate);

    //Jirra ACC-1561
    List<EmployeeLoan> findByHousemaidAndLoanDateGreaterThanEqualAndLoanTypeIn(Housemaid maid, Date loanDate, List<EmployeeLoan.LoanType> loanTypes);

    @Query("SELECT e FROM EmployeeLoan e inner join e.housemaid h where h.id in ?1 and e.loanDate >= ?2 and e.loanType in ?3")
    List<EmployeeLoan> findCapLoans(List<Long> housemaids, Date loanDate, List<EmployeeLoan.LoanType> loanTypes);

    @Query("SELECT h.id FROM EmployeeLoan e inner join e.housemaid h where h.id in ?1 and e.loanDate >= ?2 and e.loanDate < ?3 and e.skipLoanRepaymentForCurrentPayroll=true")
    List<Long> findSkippedRepaymentMaids(List<Long> housemaids, Date start, Date end);

    //Jirra ACC-627 ACC-1019
    public Page<EmployeeLoan> findByLoanDateGreaterThanEqualAndLoanDateLessThanAndHousemaidNotNull(
            Date startDate, Date endDate, Pageable pageable);

    //Jirra ACC-805 ACC-1019
    public Page<EmployeeLoan> findByLoanDateGreaterThanEqualAndLoanDateLessThanAndHousemaidNameContainingAndHousemaidNotNull(
            Date startDate, Date endDate, String name, Pageable pageable);

    @Query("SELECT SUM (e.amount) FROM EmployeeLoan e WHERE e.officeStaff = ?1 and e.isEditable = true")
    public Double sumLoansByOfficeStaff(OfficeStaff staff);

    @Query("SELECT SUM (e.amount) FROM EmployeeLoan e WHERE e.officeStaff = ?1")
    public Double sumOldLoansByOfficeStaff(OfficeStaff staff);

    @Query("SELECT new com.magnamedia.extra.LoansInformation(  e.loanType, SUM(e.amount)) FROM EmployeeLoan e WHERE e.housemaid = ?1 GROUP BY e.loanType")
    public List<LoansInformation> getMaidLoansInfo(Housemaid maid);

    @Query("SELECT e from EmployeeLoan e WHERE e.officeStaff = ?1 AND e.isEditable = true")
    public List<EmployeeLoan> getEditableLoansByOfficeStaff(OfficeStaff officeStaff);

    @Query("SELECT e.id as id, s.name as employeeName, s.salaryCurrency as salaryCurrency,e.loanType as loanType, e.amount as amount, e.monthlyRepaymentAmount as monthlyRepaymentAmount, e.loanDate as loanDate, " +
            " e.isEditable as isEditable, e.notes as notes,e.currency as currency FROM EmployeeLoan e left join e.officeStaff s WHERE s.id = ?1 and e.isShown = true")
    public List<EmployeeLoanProjection> getLoansByOfficeStaff(Long officeStaff_id);

    @Query("SELECT e FROM EmployeeLoan e left join e.officeStaff s WHERE s.id = ?1 and e.isShown = true")
    public List<EmployeeLoan> getLoansByOfficeStaffFullEntity(Long officeStaff_id);

    //Jirra ACC-463
    @Query(nativeQuery = true,
            value = "SELECT SUM(AMOUNT) FROM EMPLOYEELOANS WHERE HOUSEMAID_ID IS NOT NULL AND LOAN_DATE>=?1 AND LOAN_DATE<?2")
    public BigInteger sumLoansByDatesBetween(Date loanDate1, Date loanDate2);

    @Query(nativeQuery = true,
            value = "SELECT SUM(AMOUNT) FROM EMPLOYEELOANS WHERE HOUSEMAID_ID IS NOT NULL AND (HOUSEMAID_ID NOT IN ?1) AND LOAN_DATE<?2")
    public BigInteger sumLoansByMaidNotInAndDateBefore(List<Long> maids, Date loanDate);

    @Query(nativeQuery = true,
            value = "SELECT SUM(AMOUNT) FROM EMPLOYEELOANS WHERE HOUSEMAID_ID IS NOT NULL AND LOAN_DATE<?1")
    public BigInteger sumLoansByDateBefore(Date loanDate);

    @Query(nativeQuery = true,
            value = "SELECT SUM(AMOUNT) FROM EMPLOYEELOANS WHERE HOUSEMAID_ID IS NOT NULL AND (HOUSEMAID_ID IN ?1) AND LOAN_DATE<?2")
    public BigInteger sumLoansByMaidInAndDateBefore(List<Long> maids, Date loanDate);

    //Jirra 771
    @Query(nativeQuery = true,
            value = "SELECT SUM(E.AMOUNT) FROM EMPLOYEELOANS E INNER JOIN HOUSEMAIDS H ON E.HOUSEMAID_ID = H.ID "
                    + "WHERE (H.DATE_OF_TERMINATION IS NULL OR H.DATE_OF_TERMINATION >= ?1) "
                    + "AND (H.VISA_CANCELLATION_DATE IS NULL OR H.VISA_CANCELLATION_DATE >= ?1) "
                    + "AND (H.REJECT_DATE IS NULL OR H.REJECT_DATE >= ?1) "
                    + "AND LOAN_DATE < ?1 ")
    public BigInteger sumLoansOfNonTerminatedMaidsByDateBefore(Date loanDate);

    @Query(nativeQuery = true,
            value = "SELECT SUM(E.AMOUNT) FROM EMPLOYEELOANS E INNER JOIN HOUSEMAIDS H ON E.HOUSEMAID_ID = H.ID "
                    + "WHERE ((H.DATE_OF_TERMINATION IS NOT NULL AND H.DATE_OF_TERMINATION >= ?1 AND H.DATE_OF_TERMINATION < ?2) "
                    + "OR (H.VISA_CANCELLATION_DATE IS NOT NULL AND H.VISA_CANCELLATION_DATE >= ?1 AND H.VISA_CANCELLATION_DATE < ?2) "
                    + "OR (H.REJECT_DATE IS NOT NULL AND H.REJECT_DATE >= ?1 AND H.REJECT_DATE < ?2)) ")
    public BigInteger sumLoansOfLostLoansBecauseOfTerminationByDateBefore(Date loanDate1, Date loanDate2);

    @Query("select l from EmployeeLoan l join l.officeStaff o " +
            "where o = ?1 and l.confirmedAmountByAuditor = false and l.amount > o.salary * ?2 and cast(l.loanDate as date) >= ?3 and cast(l.loanDate as date) < ?4 AND l.isShown = true")
    List<EmployeeLoan> findByLoanDateAndAmountLimit(OfficeStaff officeStaff,
                                                    double months,
                                                    Date previousMonthLockDate,
                                                    Date currentMonthLockDate);

    @Query("select count(l) from EmployeeLoan l join l.officeStaff o " +
            "where o = ?1 and l.confirmedAmountByAuditor = false and l.amount > o.salary * ?2 and cast(l.loanDate as date) >= ?3 and cast(l.loanDate as date) < ?4 AND l.isShown = true")
    Integer findCountByLoanDateAndAmountLimit(OfficeStaff officeStaff,
                                              double months,
                                              Date previousMonthLockDate,
                                              Date currentMonthLockDate);

    @Query("select l from EmployeeLoan l join l.housemaid h " +
            "where h = ?3 and l.confirmedRepetitiveSkippedByAuditor = false and l.skipLoanRepaymentForCurrentPayroll = true and cast(l.loanDate as date) >= ?1 and cast(l.loanDate as date) < ?2 AND l.isShown = true order by l.loanDate desc")
    List<EmployeeLoan> findByRepetitiveSkippedLoanLimit(Date lastPayrollLockDate, Date currentMonthLockDate, Housemaid housmaids);

    @Query("SELECT SUM(e.amount) FROM EmployeeLoan e WHERE e.housemaid IN ?1 AND cast(e.loanDate as date) < ?2")
    public Double sumHousemaidsLoanAmounts(List<Housemaid> housemaidList, Date currentMonthLockDate);

    @Query(nativeQuery = true, value = "select " +
            "     sum(case WHEN e.LOAN_DATE >= ?3 and e.LOAN_DATE < ?2 THEN e.AMOUNT " +
            "              else 0 " +
            "         end) as currentMonthAmount, " +
            "     sum(case WHEN e.LOAN_DATE >= ?4 and e.LOAN_DATE < ?3 THEN e.AMOUNT " +
            "              else 0 " +
            "         end) as previousOneMonthAmount, " +
            "     sum(case WHEN e.LOAN_DATE >= ?5 and e.LOAN_DATE < ?4 THEN e.AMOUNT " +
            "              else 0 " +
            "         end) as previousTwoMonthAmount " +
            "FROM EMPLOYEELOANS e " +
            "WHERE e.HOUSEMAID_ID is not null and e.HOUSEMAID_ID in ?1 and e.SKIP_LOAN_REPAYMENT_FOR_CURRENT_PAYROLL = TRUE and e.LOAN_DATE >= ?5 and e.LOAN_DATE < ?2 ")
    public SkippedLoanProjection sumHousemaidSkippedLoansAmount(List<Long> housemaidIDs, Date currentMonthLockDate, Date prevOneMonthLockDate, Date prevTowLockDate, Date prevThreeLockDate);

    @Query("SELECT SUM(e.amount) FROM EmployeeLoan e WHERE e.housemaid IN ?1 AND cast(e.loanDate as date) >= ?2 AND cast(e.loanDate as date) < ?3 AND e.loanType = ?4")
    public Double sumHousemaidLoansAmountByType(List<Housemaid> housemaidList, Date lastPayrollLockDate, Date currentMonthLockDate, AbstractEmployeeLoan.LoanType loanType);

    @Query("SELECT COUNT(e) FROM EmployeeLoan e WHERE e.housemaid IN ?1 AND cast(e.loanDate as date) >= ?2 AND cast(e.loanDate as date) < ?3 AND e.loanType = ?4")
    public Integer countHousemaidLoansByType(List<Housemaid> housemaidList, Date lastPayrollLockDate, Date currentMonthLockDate, AbstractEmployeeLoan.LoanType loanType);

    @Query(nativeQuery = true,
            value = "SELECT e.LOAN_TYPE as loanType," +
                    "     sum(case WHEN e.LOAN_DATE >= ?3 and e.LOAN_DATE < ?2 THEN e.AMOUNT " +
                    "              else 0 " +
                    "         end) as currentMonthAmount, " +
                    "     sum(case WHEN e.LOAN_DATE >= ?4 and e.LOAN_DATE < ?3 THEN e.AMOUNT " +
                    "              else 0 " +
                    "         end) as previousOneMonthAmount, " +
                    "     sum(case WHEN e.LOAN_DATE >= ?5 and e.LOAN_DATE < ?4 THEN e.AMOUNT " +
                    "              else 0 " +
                    "         end) as previousTwoMonthAmount, " +
                    "     sum(case WHEN e.LOAN_DATE >= ?3 and e.LOAN_DATE < ?2 THEN 1 " +
                    "              else 0 " +
                    "         end) as currentMonthCount, " +
                    "     sum(case WHEN e.LOAN_DATE >= ?4 and e.LOAN_DATE < ?3 THEN 1 " +
                    "              else 0 " +
                    "         end) as previousOneMonthCount, " +
                    "     sum(case WHEN e.LOAN_DATE >= ?5 and e.LOAN_DATE < ?4 THEN 1 " +
                    "              else 0 " +
                    "         end) as previousTwoMonthCount " +
                    "FROM EMPLOYEELOANS e " +
                    "INNER JOIN HOUSEMAIDS h on h.ID = e.HOUSEMAID_ID " +
                    "WHERE e.HOUSEMAID_ID is not null and e.HOUSEMAID_ID in ?1 and e.LOAN_DATE >= ?5 and e.LOAN_DATE < ?2 " +
                    "GROUP BY e.LOAN_TYPE"
    )
    public List<CumulativeNewLoansAddedProjection> getHousemaidsCumulativeNewLoansAdded(List<Long> housemaidList, Date currentMonthLockDate, Date prevOneMonthLockDate, Date prevTowLockDate, Date prevThreeLockDate);

    @Query(nativeQuery = true,
            value = "SELECT e.LOAN_TYPE as loanType," +
                    "     sum(case WHEN e.LOAN_DATE >= ?3 and e.LOAN_DATE < ?2  and o.SALARY_CURRENCY = 'USD' THEN e.AMOUNT " +
                    "              else 0 " +
                    "         end) as currentMonthAmount, " +
                    "     sum(case WHEN e.LOAN_DATE >= ?3 and e.LOAN_DATE < ?2 and o.SALARY_CURRENCY = 'AED' THEN e.AMOUNT " +
                    "              else 0 " +
                    "         end) as currentMonthAmountInAED, " +
                    "     sum(case WHEN e.LOAN_DATE >= ?4 and e.LOAN_DATE < ?3 and o.SALARY_CURRENCY = 'USD' THEN e.AMOUNT " +
                    "              else 0 " +
                    "         end) as previousOneMonthAmount, " +
                    "     sum(case WHEN e.LOAN_DATE >=  ?4 and e.LOAN_DATE < ?3 and o.SALARY_CURRENCY = 'AED' THEN e.AMOUNT " +
                    "              else 0 " +
                    "         end) as previousOneMonthAmountInAED, " +
                    "     sum(case WHEN e.LOAN_DATE >= ?5 and e.LOAN_DATE <  ?4 and o.SALARY_CURRENCY = 'USD' THEN e.AMOUNT " +
                    "              else 0 " +
                    "         end) as previousTwoMonthAmount, " +
                    "     sum(case WHEN e.LOAN_DATE >= ?5 and e.LOAN_DATE <  ?4 and o.SALARY_CURRENCY = 'AED' THEN e.AMOUNT " +
                    "              else 0 " +
                    "         end) as previousTwoMonthAmountInAED, " +
                    "     sum(case WHEN e.LOAN_DATE >= ?3 and e.LOAN_DATE < ?2 THEN 1 " +
                    "              else 0 " +
                    "         end) as currentMonthCount, " +
                    "     sum(case WHEN e.LOAN_DATE >=  ?4 and e.LOAN_DATE < ?3 THEN 1 " +
                    "              else 0 " +
                    "         end) as previousOneMonthCount, " +
                    "     sum(case WHEN e.LOAN_DATE >= ?5 and e.LOAN_DATE <  ?4 THEN 1 " +
                    "              else 0 " +
                    "         end) as previousTwoMonthCount " +
                    "FROM EMPLOYEELOANS e " +
                    "INNER JOIN OFFICESTAFFS o on o.ID = e.OFFICE_STAFF_ID " +
                    "WHERE e.OFFICE_STAFF_ID is not null and e.OFFICE_STAFF_ID in ?1 and e.LOAN_DATE >= ?5 and e.LOAN_DATE < ?2 AND e.IS_SHOWN = true " +
                    "GROUP BY e.LOAN_TYPE"
    )
    public List<CumulativeNewLoansAddedProjection> getOfficeStaffsCumulativeNewLoansAdded(List<Long> officeStaffIDs, Date currentMonthLockDate, Date prevOneMonthLockDate, Date prevTowLockDate, Date prevThreeLockDate);

    @Query("select h from Housemaid h left join Repayment r on h = r.housemaid and cast(r.repaymentDate as date) >= ?2 and cast(r.repaymentDate as date) < ?3 " +
            "where h in ?1 and h.startDate < ?4 and r is null order by h.name")
    List<Housemaid> getCheckListSkippedRepaymentHousemaids(List<Housemaid> housemaids, Date lastPayrollLockDate, Date currentMonthLockDate, java.sql.Date payrollMonth);

    Boolean existsByHousemaidAndLoanType(Housemaid maid,
                                        EmployeeLoan.LoanType loanType);

    EmployeeLoan findTopByHousemaidAndLoanType(Housemaid maid,
                                         EmployeeLoan.LoanType loanType);

     public EmployeeLoan findTopByHousemaidAndExpenseRequestTodo(Housemaid housemaid, ExpenseRequestTodo expenseRequestTodo);

    Boolean existsByHousemaidIdAndAmountAndLoanType(Long housemaidId, Double amount, EmployeeLoan.LoanType loanType);

    @Query("SELECT e FROM EmployeeLoan e WHERE e.officeStaff = ?1 and e.isEditable = true")
    public List<EmployeeLoan> getLoansByOfficeStaff(OfficeStaff staff);

    @Query("SELECT SUM(e.amount - COALESCE(r.amount, 0) - COALESCE(hf.amount, 0)) " +
            "FROM EmployeeLoan e " +
            "INNER JOIN e.housemaid h " +
            "LEFT JOIN Repayment r ON h = r.housemaid " +
            "LEFT JOIN HousemaidForgiveness hf ON h = hf.housemaid " +
            "WHERE h.status NOT IN ?1 " +
            "AND r.paidRepayment = true")
    public Double sumLoanBalanceForAllMaids(List<HousemaidStatus> rejectedStatuses);
}
