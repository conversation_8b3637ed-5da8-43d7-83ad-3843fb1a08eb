/**
 *
 */
package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Tag;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.*;
import com.magnamedia.extra.payroll.init.HousemaidFieldProjection;
import com.magnamedia.extra.payroll.init.HousemaidVisaInfoProjection;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.module.type.PendingStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface HousemaidRepository extends BaseRepository<Housemaid> {

    public List<Housemaid> findByStatus(HousemaidStatus status);

    public List<Housemaid> findByName(String Name);

    public Housemaid findFirstByName(String Name);

    //    @Query("SELECT COUNT(c) FROM Complaint c WHERE c.primaryType = ?1 OR ?1 MEMBER OF c.otherTypes")
//com.magnamedia.module.type.HousemaidLiveplace.IN 
    //@Query("SELECT SUM (amount) FROM EmployeeLoan e WHERE e.housemaid = ?1 AND ")
//  public long ticketToExit(Housemaid maid, Emlo);
    public List<Housemaid> findByStartDateGreaterThanEqual(Date startDate);

    @Query("SELECT h FROM Housemaid h"
            + " where  ("
            + "(h.status<>com.magnamedia.core.type.HousemaidStatus.WITH_CLIENT and (( h.foodAllowance IS NOT NULL and h.foodAllowance >0) or (h.housingAllowance IS NOT NULL and h.housingAllowance>0))) or "
            + "(h.status=com.magnamedia.core.type.HousemaidStatus.WITH_CLIENT and (( h.foodAllowance IS NOT NULL and h.foodAllowance >0) or (h.housingAllowance IS NOT NULL and h.housingAllowance>0)) and h.living=com.magnamedia.module.type.HousemaidLiveplace.IN) or "
            + "(h.status=com.magnamedia.core.type.HousemaidStatus.WITH_CLIENT and (h.foodAllowance IS NULL or h.housingAllowance IS NULL or h.foodAllowance<=0 or h.housingAllowance<=0) and h.living=com.magnamedia.module.type.HousemaidLiveplace.OUT)"
            + ")")
    public List<Housemaid> findInvalidAllowances();

    @Query("SELECT h"
            + " FROM Housemaid h"
            + " WHERE h.status<>com.magnamedia.core.type.HousemaidStatus.WITH_CLIENT AND h.living=com.magnamedia.module.type.HousemaidLiveplace.OUT")
    public List<Housemaid> findHousemaidsLiveOutNotWithClient();

    @Query("SELECT h"
            + " FROM Housemaid h"
            + " WHERE (h.startDate is not null or (h.landedInDubaiDate is not null and h.startDate is null)) and (h.basicSalary is null or h.basicSalary<1)")
    public List<Housemaid> findWorkingHousemaidsWithoutSalary();

    @Query("SELECT h"
            + " FROM Housemaid h"
            + " WHERE (SELECT c FROM Contract c where c.housemaid =h and c.status=com.magnamedia.module.type.ContractStatus.ACTIVE) is not null and (SELECT c.living FROM Contract c where c.housemaid =h and c.status=com.magnamedia.module.type.ContractStatus.ACTIVE)<> h.living")
    public List<Housemaid> findHousemaidDifferentLivingThanContract();
//
    @Query("SELECT h"
            + " FROM Housemaid h"
            + " WHERE (SELECT c FROM Contract c where c.housemaid =h and c.status=com.magnamedia.module.type.ContractStatus.ACTIVE) is not null and h.status<>com.magnamedia.core.type.HousemaidStatus.WITH_CLIENT")
    public List<Housemaid> findHousemaidsWithActiveContractNotWithClient();

    @Query("SELECT h"
            + " FROM Housemaid h"
            + " WHERE (SELECT count(c) FROM Contract c where c.housemaid =h and c.status=com.magnamedia.module.type.ContractStatus.ACTIVE) "
            + " is null and h.status=com.magnamedia.core.type.HousemaidStatus.WITH_CLIENT")
    public List<Housemaid> findHHousemaidsWithClientWithoutContract();

    @Query("SELECT h"
            + " FROM Housemaid h"
            + " WHERE h.status not in ?1 and  ("
            + " (SELECT SUM (e.amount) FROM EmployeeLoan e WHERE e.housemaid = h)"
            + "-(SELECT SUM (f.amount) FROM HousemaidForgiveness f WHERE f.housemaid = h)"
            + "-(SELECT SUM (r.amount) FROM Repayment r WHERE r.housemaid = h AND r.paidRepayment=true)"
            + ")<-1")
    public List<Housemaid> findNegativeLoanBalanceHousemaids(Set<HousemaidStatus> rejectedStatuses);

    @Query("SELECT h"
            + " FROM Housemaid h WHERE "
            + " h.basicSalary - (h.primarySalary + h.overTime+h.monthlyLoan+h.holiday+h.airfareFee) NOT BETWEEN -1 AND 1")
    public List<Housemaid> findHousemaidsWithBasicSalaryDifferentThanBreakdown();

    @Query("SELECT h FROM Housemaid h "
            + " WHERE h.freedomMaid=true AND (h.basicSalary=0 OR h.basicSalary is null) AND h.startDate is not null")
    public List<Housemaid> findFreedomHousemaidsWithStartDateAndNoSalary();

    @Query("SELECT h FROM Housemaid h "
            + " WHERE h.isAgency=true AND (h.basicSalary=0 OR h.basicSalary is null) AND h.startDate is not null")
    public List<Housemaid> findAgencyHousemaidsWithStartDateAndNoSalary();

    @Query("SELECT h FROM Housemaid h "
            + " WHERE h.isAgency=false AND h.freedomMaid=false AND (h.basicSalary=0 OR h.basicSalary is null) AND h.startDate is not null")
    public List<Housemaid> findCleanExitHousemaidsWithStartDateAndNoSalary();

    @Query("SELECT h FROM Housemaid h "
            + " WHERE h.isBeingPaid50PercentSalary=true AND h.startDate<=?1")
    public List<Housemaid> findMaidsPaid50PercentSalaryAndStartDateMoreThan(Date minStartDate);

    //ACC-275
    @Query("SELECT h FROM Housemaid h JOIN h.contracts c "
            + "WHERE (h.basicSalary = 0 OR h.basicSalary is null) AND h.startDate is not null AND "
            + "c.status = com.magnamedia.module.type.ContractStatus.ACTIVE AND "
            + "c.contractProspectType = ?1")
    public List<Housemaid> findVisaHousemaidsWithStartDateAndNoSalary(PicklistItem item);

    //Jirra ACC-462
    @Query(
            nativeQuery = true,
            value =
                    "SELECT h.id as hid, hr.ID as hrid, hr.LAST_MODIFICATION_DATE as LAST_MODIFICATION_DATE, hr.STATUS as status, hr2.ID as hr2id " +
                            "FROM HOUSEMAIDS h " +
                            "INNER JOIN (select * from HOUSEMAIDS_REVISIONS where LAST_MODIFICATION_DATE < ?1) as hr on h.id=hr.ID " +
                            "LEFT JOIN (select * from HOUSEMAIDS_REVISIONS where LAST_MODIFICATION_DATE < ?1) as hr2 on hr.ID = hr2.ID and hr.LAST_MODIFICATION_DATE < hr2.LAST_MODIFICATION_DATE " +
                            "WHERE hr2.ID is null and hr.STATUS not in ?2")
    public List<Object[]> getHousmaidRevisionsNotInStatusesAndBeforeDate(Date date, List<String> statuses);

    //Jirra ACC-627
    @Query(
            nativeQuery = true,
            value =
                    "SELECT * FROM (SELECT DISTINCT H.ID AS HID, H.NAME AS NAME, HR.ID AS HRID, HR.LAST_MODIFICATION_DATE AS LAST_MODIFICATION_DATE, HR.STATUS AS STATUS, HR2.ID AS HR2ID "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM EMPLOYEELOANS E WHERE E.HOUSEMAID_ID = H.ID AND LOAN_DATE< ?1), 0) AS LOAN_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM HOUSEMAIDFORGIVENESS HF WHERE HF.HOUSEMAID_ID = H.ID AND FORGIVENESS_DATE< ?1), 0) AS FORGIVENESS_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM REPAYMENTS R WHERE R.HOUSEMAID_ID = H.ID AND PAID_REPAYMENT=TRUE AND REPAYMENT_DATE < ?1), 0) AS REPAYMENTS_AMOUNT "
                            + "FROM HOUSEMAIDS H "
                            + "INNER JOIN (SELECT * FROM HOUSEMAIDS_REVISIONS WHERE LAST_MODIFICATION_DATE < ?1) AS HR ON H.ID=HR.ID "
                            + "LEFT JOIN (SELECT * FROM HOUSEMAIDS_REVISIONS WHERE LAST_MODIFICATION_DATE < ?1) AS HR2 ON HR.ID = HR2.ID AND HR.LAST_MODIFICATION_DATE < HR2.LAST_MODIFICATION_DATE "
                            + "WHERE HR2.ID IS NULL AND HR.STATUS NOT IN ?2 ) AS TT "
                            + "WHERE (TT.LOAN_AMOUNT - TT.FORGIVENESS_AMOUNT - TT.REPAYMENTS_AMOUNT ) <> 0 AND TT.NAME LIKE ?3 "
                            + "ORDER BY  ?#{#pageable}",
            countQuery =
                    "SELECT count(distinct HID)"
                            + " FROM (SELECT DISTINCT H.ID AS HID, H.NAME AS NAME, HR.ID AS HRID, HR.LAST_MODIFICATION_DATE AS LAST_MODIFICATION_DATE, HR.STATUS AS STATUS, HR2.ID AS HR2ID "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM EMPLOYEELOANS E WHERE E.HOUSEMAID_ID = H.ID AND LOAN_DATE< ?1), 0) AS LOAN_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM HOUSEMAIDFORGIVENESS HF WHERE HF.HOUSEMAID_ID = H.ID AND FORGIVENESS_DATE< ?1), 0) AS FORGIVENESS_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM REPAYMENTS R WHERE R.HOUSEMAID_ID = H.ID AND PAID_REPAYMENT=TRUE AND REPAYMENT_DATE < ?1), 0) AS REPAYMENTS_AMOUNT "
                            + "FROM HOUSEMAIDS H "
                            + "INNER JOIN (SELECT * FROM HOUSEMAIDS_REVISIONS WHERE LAST_MODIFICATION_DATE < ?1) AS HR ON H.ID=HR.ID "
                            + "LEFT JOIN (SELECT * FROM HOUSEMAIDS_REVISIONS WHERE LAST_MODIFICATION_DATE < ?1) AS HR2 ON HR.ID = HR2.ID AND HR.LAST_MODIFICATION_DATE < HR2.LAST_MODIFICATION_DATE "
                            + "WHERE HR2.ID IS NULL AND HR.STATUS NOT IN ?2 ) AS TT "
                            + "WHERE (TT.LOAN_AMOUNT - TT.FORGIVENESS_AMOUNT - TT.REPAYMENTS_AMOUNT )  >= 1 AND TT.NAME LIKE ?3 ")
    public Page<Object[]> getHousmaidRevisionsNotInStatusesAndBeforeDate(Date date, List<String> statuses, String searchName, Pageable pageable);

    //Jirra ACC-462
    @Query(
            nativeQuery = true,
            value =
                    "SELECT distinct h.id as hid, hr.ID as hrid, hr.LAST_MODIFICATION_DATE as LAST_MODIFICATION_DATE, hr.STATUS as status, hr2.ID as hr2id "
                            + "FROM HOUSEMAIDS h "
                            + "INNER JOIN (select * from HOUSEMAIDS_REVISIONS where LAST_MODIFICATION_DATE >= ?1 and LAST_MODIFICATION_DATE < ?2) as hr on h.id=hr.ID "
                            + "LEFT JOIN (select * from HOUSEMAIDS_REVISIONS where LAST_MODIFICATION_DATE >= ?1 and LAST_MODIFICATION_DATE < ?2) as hr2 on hr.ID = hr2.ID and hr.LAST_MODIFICATION_DATE < hr2.LAST_MODIFICATION_DATE "
                            + "WHERE hr2.ID is null and hr.STATUS in ?3")
    public List<Object[]> getHousmaidRevisionsInStatusesAndBetweenDates(Date fromDate, Date toDate, List<String> statuses);

    //Jirra ACC-627
    @Query(
            nativeQuery = true,
            value =
                    "SELECT * FROM (SELECT DISTINCT  H.ID AS HID, H.NAME AS NAME, HR.ID AS HRID, HR.LAST_MODIFICATION_DATE AS LAST_MODIFICATION_DATE, HR.STATUS AS STATUS, HR2.ID AS HR2ID "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM EMPLOYEELOANS E WHERE E.HOUSEMAID_ID = H.ID AND LOAN_DATE< ?2), 0) AS LOAN_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM HOUSEMAIDFORGIVENESS HF WHERE HF.HOUSEMAID_ID = H.ID AND FORGIVENESS_DATE< ?2), 0) AS FORGIVENESS_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM REPAYMENTS R WHERE R.HOUSEMAID_ID = H.ID AND PAID_REPAYMENT=TRUE AND REPAYMENT_DATE < ?2), 0) AS REPAYMENTS_AMOUNT "
                            + "FROM HOUSEMAIDS H "
                            + "INNER JOIN (SELECT * FROM HOUSEMAIDS_REVISIONS WHERE LAST_MODIFICATION_DATE >= ?1 AND LAST_MODIFICATION_DATE < ?2) AS HR ON H.ID=HR.ID "
                            + "LEFT JOIN (SELECT * FROM HOUSEMAIDS_REVISIONS WHERE LAST_MODIFICATION_DATE >= ?1 AND LAST_MODIFICATION_DATE < ?2) AS HR2 ON HR.ID = HR2.ID AND HR.LAST_MODIFICATION_DATE < HR2.LAST_MODIFICATION_DATE "
                            + "WHERE HR2.ID IS NULL AND HR.STATUS IN ?3) AS TT "
                            + "WHERE (TT.LOAN_AMOUNT - TT.FORGIVENESS_AMOUNT - TT.REPAYMENTS_AMOUNT ) >= 1 AND TT.NAME LIKE ?4 "
                            + "ORDER BY  ?#{#pageable}",
            countQuery =
                    "SELECT COUNT(DISTINCT HID) FROM (SELECT DISTINCT  H.ID AS HID, H.NAME AS NAME, HR.ID AS HRID, HR.LAST_MODIFICATION_DATE AS LAST_MODIFICATION_DATE, HR.STATUS AS STATUS, HR2.ID AS HR2ID "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM EMPLOYEELOANS E WHERE E.HOUSEMAID_ID = H.ID AND LOAN_DATE< ?2), 0) AS LOAN_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM HOUSEMAIDFORGIVENESS HF WHERE HF.HOUSEMAID_ID = H.ID AND FORGIVENESS_DATE< ?2), 0) AS FORGIVENESS_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM REPAYMENTS R WHERE R.HOUSEMAID_ID = H.ID AND PAID_REPAYMENT=TRUE AND REPAYMENT_DATE < ?2), 0) AS REPAYMENTS_AMOUNT "
                            + "FROM HOUSEMAIDS H "
                            + "INNER JOIN (SELECT * FROM HOUSEMAIDS_REVISIONS WHERE LAST_MODIFICATION_DATE >= ?1 AND LAST_MODIFICATION_DATE < ?2) AS HR ON H.ID=HR.ID "
                            + "LEFT JOIN (SELECT * FROM HOUSEMAIDS_REVISIONS WHERE LAST_MODIFICATION_DATE >= ?1 AND LAST_MODIFICATION_DATE < ?2) AS HR2 ON HR.ID = HR2.ID AND HR.LAST_MODIFICATION_DATE < HR2.LAST_MODIFICATION_DATE "
                            + "WHERE HR2.ID IS NULL AND HR.STATUS IN ?3) AS TT "
                            + "WHERE (TT.LOAN_AMOUNT - TT.FORGIVENESS_AMOUNT - TT.REPAYMENTS_AMOUNT ) >= 1 AND TT.NAME LIKE ?4 ")
    public Page<Object[]> getHousmaidRevisionsInStatusesAndBetweenDates(Date fromDate, Date toDate, List<String> statuses, String searchName, Pageable pageable);

    //Jirra ACC-771
    @Query(
            nativeQuery = true,
            value =
                    "SELECT * FROM (SELECT DISTINCT H.ID AS HID, H.NAME AS NAME, H.STATUS AS STATUS "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM EMPLOYEELOANS E WHERE E.HOUSEMAID_ID = H.ID AND LOAN_DATE< ?1), 0) AS LOAN_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM HOUSEMAIDFORGIVENESS HF WHERE HF.HOUSEMAID_ID = H.ID AND FORGIVENESS_DATE< ?1), 0) AS FORGIVENESS_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM REPAYMENTS R WHERE R.HOUSEMAID_ID = H.ID AND PAID_REPAYMENT=TRUE AND REPAYMENT_DATE < ?1), 0) AS REPAYMENTS_AMOUNT "
                            + "FROM HOUSEMAIDS H "
                            + "WHERE (H.DATE_OF_TERMINATION IS NULL OR H.DATE_OF_TERMINATION >= ?1) "
                            + "AND (H.VISA_CANCELLATION_DATE IS NULL OR H.VISA_CANCELLATION_DATE >= ?1) "
                            + "AND (H.REJECT_DATE IS NULL OR H.REJECT_DATE >= ?1) ) AS TT "
                            + "WHERE (TT.LOAN_AMOUNT - TT.FORGIVENESS_AMOUNT - TT.REPAYMENTS_AMOUNT ) >= 1 AND TT.NAME LIKE ?2 "
                            + "ORDER BY  ?#{#pageable}",
            countQuery =
                    "SELECT count(distinct HID) "
                            + " FROM (SELECT DISTINCT H.ID AS HID, H.NAME AS NAME, H.STATUS AS STATUS "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM EMPLOYEELOANS E WHERE E.HOUSEMAID_ID = H.ID AND LOAN_DATE< ?1), 0) AS LOAN_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM HOUSEMAIDFORGIVENESS HF WHERE HF.HOUSEMAID_ID = H.ID AND FORGIVENESS_DATE< ?1), 0) AS FORGIVENESS_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM REPAYMENTS R WHERE R.HOUSEMAID_ID = H.ID AND PAID_REPAYMENT=TRUE AND REPAYMENT_DATE < ?1), 0) AS REPAYMENTS_AMOUNT "
                            + "FROM HOUSEMAIDS H "
                            + "WHERE (H.DATE_OF_TERMINATION IS NULL OR H.DATE_OF_TERMINATION >= ?1) "
                            + "AND (H.VISA_CANCELLATION_DATE IS NULL OR H.VISA_CANCELLATION_DATE >= ?1) "
                            + "AND (H.REJECT_DATE IS NULL OR H.REJECT_DATE >= ?1) ) AS TT "
                            + "WHERE (TT.LOAN_AMOUNT - TT.FORGIVENESS_AMOUNT - TT.REPAYMENTS_AMOUNT ) >= 1 AND TT.NAME LIKE ?2 ")
    public Page<Object[]> getNonTerminatedHousemaidsLoansOfTheMonthFirstBeforeDateAndName(Date date, String searchName, Pageable pageable);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT * FROM (SELECT DISTINCT  H.ID AS HID, H.NAME AS NAME, H.STATUS AS STATUS "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM EMPLOYEELOANS E WHERE E.HOUSEMAID_ID = H.ID), 0) AS LOAN_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM HOUSEMAIDFORGIVENESS HF WHERE HF.HOUSEMAID_ID = H.ID), 0) AS FORGIVENESS_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM REPAYMENTS R WHERE R.HOUSEMAID_ID = H.ID AND PAID_REPAYMENT=TRUE), 0) AS REPAYMENTS_AMOUNT "
                            + "FROM HOUSEMAIDS H "
                            + "WHERE (H.DATE_OF_TERMINATION IS NOT NULL AND H.DATE_OF_TERMINATION >= ?1 AND H.DATE_OF_TERMINATION < ?2) "
                            + "OR (H.VISA_CANCELLATION_DATE IS NOT NULL AND H.VISA_CANCELLATION_DATE >= ?1 AND H.VISA_CANCELLATION_DATE < ?2) "
                            + "OR (H.REJECT_DATE IS NOT NULL AND H.REJECT_DATE >= ?1 AND H.REJECT_DATE < ?2) ) AS TT "
                            + "WHERE (TT.LOAN_AMOUNT - TT.FORGIVENESS_AMOUNT - TT.REPAYMENTS_AMOUNT ) >= 1 AND TT.NAME LIKE ?3 "
                            + "ORDER BY  ?#{#pageable}",
            countQuery =
                    "SELECT COUNT(DISTINCT HID) FROM (SELECT DISTINCT  H.ID AS HID, H.NAME AS NAME, H.STATUS AS STATUS "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM EMPLOYEELOANS E WHERE E.HOUSEMAID_ID = H.ID AND LOAN_DATE< ?2), 0) AS LOAN_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM HOUSEMAIDFORGIVENESS HF WHERE HF.HOUSEMAID_ID = H.ID AND FORGIVENESS_DATE< ?2), 0) AS FORGIVENESS_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM REPAYMENTS R WHERE R.HOUSEMAID_ID = H.ID AND PAID_REPAYMENT=TRUE AND REPAYMENT_DATE < ?2), 0) AS REPAYMENTS_AMOUNT "
                            + ", H.DATE_OF_TERMINATION "
                            + ", H.VISA_CANCELLATION_DATE "
                            + ", H.REJECT_DATE "
                            + "FROM HOUSEMAIDS H "
                            + "WHERE (H.DATE_OF_TERMINATION IS NOT NULL AND H.DATE_OF_TERMINATION >= ?1 AND H.DATE_OF_TERMINATION < ?2) "
                            + "OR (H.VISA_CANCELLATION_DATE IS NOT NULL AND H.VISA_CANCELLATION_DATE >= ?1 AND H.VISA_CANCELLATION_DATE < ?2) "
                            + "OR (H.REJECT_DATE IS NOT NULL AND H.REJECT_DATE >= ?1 AND H.REJECT_DATE < ?2) ) AS TT "
                            + "WHERE (TT.LOAN_AMOUNT - TT.FORGIVENESS_AMOUNT - TT.REPAYMENTS_AMOUNT ) >= 1 AND TT.NAME LIKE ?3 ")
    public Page<Object[]> getLostLoansBecauseOfTerminationBetweenDatesAndName(Date fromDate, Date toDate, String searchName, Pageable pageable);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT * FROM (SELECT DISTINCT H.ID AS HID, H.NAME AS NAME, H.STATUS AS STATUS "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM EMPLOYEELOANS E WHERE E.HOUSEMAID_ID = H.ID AND LOAN_DATE< ?1), 0) AS LOAN_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM HOUSEMAIDFORGIVENESS HF WHERE HF.HOUSEMAID_ID = H.ID AND FORGIVENESS_DATE< ?1), 0) AS FORGIVENESS_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM REPAYMENTS R WHERE R.HOUSEMAID_ID = H.ID AND PAID_REPAYMENT=TRUE AND REPAYMENT_DATE < ?1), 0) AS REPAYMENTS_AMOUNT "
                            + "FROM HOUSEMAIDS H "
                            + "WHERE (H.DATE_OF_TERMINATION IS NULL OR H.DATE_OF_TERMINATION >= ?1) "
                            + "AND (H.VISA_CANCELLATION_DATE IS NULL OR H.VISA_CANCELLATION_DATE >= ?1) "
                            + "AND (H.REJECT_DATE IS NULL OR H.REJECT_DATE >= ?1) ) AS TT "
                            + "WHERE (TT.LOAN_AMOUNT - TT.FORGIVENESS_AMOUNT - TT.REPAYMENTS_AMOUNT ) >= 1 "
                            + "ORDER BY  ?#{#pageable}",
            countQuery =
                    "SELECT count(distinct HID) "
                            + " FROM (SELECT DISTINCT H.ID AS HID, H.NAME AS NAME, H.STATUS AS STATUS "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM EMPLOYEELOANS E WHERE E.HOUSEMAID_ID = H.ID AND LOAN_DATE< ?1), 0) AS LOAN_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM HOUSEMAIDFORGIVENESS HF WHERE HF.HOUSEMAID_ID = H.ID AND FORGIVENESS_DATE< ?1), 0) AS FORGIVENESS_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM REPAYMENTS R WHERE R.HOUSEMAID_ID = H.ID AND PAID_REPAYMENT=TRUE AND REPAYMENT_DATE < ?1), 0) AS REPAYMENTS_AMOUNT "
                            + "FROM HOUSEMAIDS H "
                            + "WHERE (H.DATE_OF_TERMINATION IS NULL OR H.DATE_OF_TERMINATION >= ?1) "
                            + "AND (H.VISA_CANCELLATION_DATE IS NULL OR H.VISA_CANCELLATION_DATE >= ?1) "
                            + "AND (H.REJECT_DATE IS NULL OR H.REJECT_DATE >= ?1) ) AS TT "
                            + "WHERE (TT.LOAN_AMOUNT - TT.FORGIVENESS_AMOUNT - TT.REPAYMENTS_AMOUNT ) >= 1 ")
    public Page<Object[]> getNonTerminatedHousemaidsLoansOfTheMonthFirstBeforeDate(Date date, Pageable pageable);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT * FROM (SELECT DISTINCT  H.ID AS HID, H.NAME AS NAME, H.STATUS AS STATUS "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM EMPLOYEELOANS E WHERE E.HOUSEMAID_ID = H.ID), 0) AS LOAN_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM HOUSEMAIDFORGIVENESS HF WHERE HF.HOUSEMAID_ID = H.ID), 0) AS FORGIVENESS_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM REPAYMENTS R WHERE R.HOUSEMAID_ID = H.ID AND PAID_REPAYMENT=TRUE), 0) AS REPAYMENTS_AMOUNT "
                            + "FROM HOUSEMAIDS H "
                            + "WHERE (H.DATE_OF_TERMINATION IS NOT NULL AND H.DATE_OF_TERMINATION >= ?1 AND H.DATE_OF_TERMINATION < ?2) "
                            + "OR (H.VISA_CANCELLATION_DATE IS NOT NULL AND H.VISA_CANCELLATION_DATE >= ?1 AND H.VISA_CANCELLATION_DATE < ?2) "
                            + "OR (H.REJECT_DATE IS NOT NULL AND H.REJECT_DATE >= ?1 AND H.REJECT_DATE < ?2) ) AS TT "
                            + "WHERE (TT.LOAN_AMOUNT - TT.FORGIVENESS_AMOUNT - TT.REPAYMENTS_AMOUNT ) >= 1 "
                            + "ORDER BY  ?#{#pageable}",
            countQuery =
                    "SELECT COUNT(DISTINCT HID) FROM (SELECT DISTINCT  H.ID AS HID, H.NAME AS NAME, H.STATUS AS STATUS "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM EMPLOYEELOANS E WHERE E.HOUSEMAID_ID = H.ID AND LOAN_DATE< ?2), 0) AS LOAN_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM HOUSEMAIDFORGIVENESS HF WHERE HF.HOUSEMAID_ID = H.ID AND FORGIVENESS_DATE< ?2), 0) AS FORGIVENESS_AMOUNT "
                            + ", IFNULL((SELECT SUM(AMOUNT) FROM REPAYMENTS R WHERE R.HOUSEMAID_ID = H.ID AND PAID_REPAYMENT=TRUE AND REPAYMENT_DATE < ?2), 0) AS REPAYMENTS_AMOUNT "
                            + ", H.DATE_OF_TERMINATION "
                            + ", H.VISA_CANCELLATION_DATE "
                            + ", H.REJECT_DATE "
                            + "FROM HOUSEMAIDS H "
                            + "WHERE (H.DATE_OF_TERMINATION IS NOT NULL AND H.DATE_OF_TERMINATION >= ?1 AND H.DATE_OF_TERMINATION < ?2) "
                            + "OR (H.VISA_CANCELLATION_DATE IS NOT NULL AND H.VISA_CANCELLATION_DATE >= ?1 AND H.VISA_CANCELLATION_DATE < ?2) "
                            + "OR (H.REJECT_DATE IS NOT NULL AND H.REJECT_DATE >= ?1 AND H.REJECT_DATE < ?2) ) AS TT "
                            + "WHERE (TT.LOAN_AMOUNT - TT.FORGIVENESS_AMOUNT - TT.REPAYMENTS_AMOUNT ) >= 1 ")
    public Page<Object[]> getLostLoansBecauseOfTerminationBetweenDates(Date fromDate, Date toDate, Pageable pageable);


    //Jirra ACC-874
    @Query(
            nativeQuery = true,
            value =
                    "SELECT DISTINCT H.ID, H.NAME, H.STATUS, H.START_DATE "
                            + "FROM HOUSEMAIDS H "
                            + "LEFT JOIN HOUSEMAIDS_REVISIONS HR ON HR.ID = H.ID AND HR.STATUS = 'WITH_CLIENT' "
                            + "LEFT JOIN FREEDOMOPERATORS F ON F.ID = H.FREEDOM_OPERATOR_ID AND F.NAME = 'Precy - Sama Agency Freedom Operator' "
                            + "WHERE "
                            + "(H.STATUS IN ('WITH_CLIENT' , 'ON_VACATION', 'PENDING_FOR_DISCIPLINE', 'PENDING_FOR_VIDEOSHOOT', 'SICK_WITHOUT_CLIENT') "
                            + "OR (H.STATUS IN ('RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS') AND (H.IS_AGENCY = TRUE OR F.ID IS NOT NULL)) "
                            + "OR (HR.ID IS NOT NULL AND (H.STATUS = 'AVAILABLE' OR (H.STATUS IN ('RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS') AND (H.IS_AGENCY = FALSE AND F.ID IS NULL)))) "
                            + ") "
                            + "AND H.START_DATE < ?1 "
                            + "ORDER BY H.NAME ")
    public List<Object[]> getHousemaidChecklistDataReport1(Date date);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT DISTINCT H.ID, H.NAME, H.STATUS, H.START_DATE "
                            + "FROM HOUSEMAIDS H "
                            + "INNER JOIN PICKLISTS_ITEMS PI ON PI.ID = H.NATIONALITY_ID "
                            + "LEFT JOIN HOUSEMAIDS_REVISIONS HR ON HR.ID = H.ID AND HR.STATUS = 'WITH_CLIENT'  "
                            + "LEFT JOIN FREEDOMOPERATORS F ON F.ID = H.FREEDOM_OPERATOR_ID AND F.NAME = 'Precy - Sama Agency Freedom Operator' "
                            + "WHERE "
                            + "(H.STATUS IN ('WITH_CLIENT' , 'ON_VACATION', 'PENDING_FOR_DISCIPLINE', 'PENDING_FOR_VIDEOSHOOT', 'SICK_WITHOUT_CLIENT') "
                            + "OR (H.STATUS IN ('RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS') AND (H.IS_AGENCY = TRUE OR F.ID IS NOT NULL)) "
                            + "OR (HR.ID IS NOT NULL AND (H.STATUS = 'AVAILABLE' OR (H.STATUS IN ('RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS') AND (H.IS_AGENCY = FALSE AND F.ID IS NULL)))) "
                            + ") "
                            + "AND H.HOUSEMAID_TYPE <> 'MAID_VISA' "
                            + "AND PI.CODE LIKE 'INDONESIA%' "
                            + "AND H.START_DATE < ?1 "
                            + "ORDER BY H.NAME ")
    public List<Object[]> getHousemaidChecklistDataReport2_a(Date date);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT DISTINCT H.ID, H.NAME, H.STATUS, H.START_DATE, PL.NAME AS VIP_OR_NOT "
                            + "FROM HOUSEMAIDS H "
                            + "LEFT JOIN HOUSEMAIDS_REVISIONS HR ON HR.ID = H.ID AND HR.STATUS = 'WITH_CLIENT' "
                            + "LEFT JOIN FREEDOMOPERATORS F ON F.ID = H.FREEDOM_OPERATOR_ID AND F.NAME = 'Precy - Sama Agency Freedom Operator' "
                            + "LEFT JOIN PICKLISTS_ITEMS PL ON PL.ID = H.PAYROLL_TYPE_ID "
                            + "WHERE "
                            + "(H.STATUS IN ('WITH_CLIENT' , 'ON_VACATION', 'PENDING_FOR_DISCIPLINE', 'PENDING_FOR_VIDEOSHOOT', 'SICK_WITHOUT_CLIENT', 'RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS') "
                            + "OR (HR.ID IS NOT NULL AND H.STATUS = 'AVAILABLE') "
                            + ") "
                            + "AND (H.IS_AGENCY = TRUE OR F.ID IS NOT NULL) "
                            + "AND H.START_DATE < ?1 "
                            + "ORDER BY H.NAME ")
    public List<Object[]> getHousemaidChecklistDataReport2_b(Date date);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT DISTINCT H.ID, H.NAME, H.STATUS, H.START_DATE, P.NAME as NATIONALITY, H.LANDED_IN_DUBAI_DATE, F.NAME FREEDOM_OPERATOR "
                            + "FROM HOUSEMAIDS H "
                            + "INNER JOIN PICKLISTS_ITEMS P ON P.ID = H.NATIONALITY_ID "
                            + "LEFT JOIN HOUSEMAIDS_REVISIONS HR ON HR.ID = H.ID AND HR.STATUS = 'WITH_CLIENT' "
                            + "LEFT JOIN FREEDOMOPERATORS F ON F.ID = H.FREEDOM_OPERATOR_ID "
                            + "WHERE "
                            + "(H.STATUS IN ('WITH_CLIENT' , 'ON_VACATION', 'PENDING_FOR_DISCIPLINE', 'PENDING_FOR_VIDEOSHOOT', 'SICK_WITHOUT_CLIENT') "
                            + "OR (HR.ID IS NOT NULL AND H.STATUS IN ('AVAILABLE', 'RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS')) "
                            + ") "
                            + "AND H.FREEDOM_MAID = TRUE "
                            + "AND H.HOUSEMAID_TYPE = 'FREEDOM_OPERATOR' "
                            + "AND F.NAME <> 'Precy - Sama Agency Freedom Operator' "
                            + "AND H.START_DATE < ?1 "
                            + "ORDER BY H.NAME ")
    public List<Object[]> getHousemaidChecklistDataReport2_c(Date date);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT DISTINCT H.ID, H.NAME, H.STATUS, H.START_DATE "
                            + "FROM HOUSEMAIDS H "
                            + "INNER JOIN TICKETS T ON T.HOUSEMAID_ID = H.ID "
                            + "LEFT JOIN HOUSEMAIDS_REVISIONS HR ON HR.ID = H.ID AND HR.STATUS = 'WITH_CLIENT' "
                            + "WHERE "
                            + "(H.STATUS IN ('WITH_CLIENT' , 'ON_VACATION', 'PENDING_FOR_DISCIPLINE', 'PENDING_FOR_VIDEOSHOOT', 'SICK_WITHOUT_CLIENT') "
                            + "OR (HR.ID IS NOT NULL AND H.STATUS IN ('AVAILABLE', 'RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS')) "
                            + ") "
                            + "AND (H.CLEAN_EXIT_MAID = 0 OR H.CLEAN_EXIT_MAID IS NULL) "
                            + "AND (H.FREEDOM_MAID = 0 OR H.FREEDOM_MAID IS NULL) "
                            + "AND (H.IS_AGENCY = 0 OR H.IS_AGENCY IS NULL) "
                            + "AND H.HOUSEMAID_TYPE NOT IN ('MAID_VISA','FREEDOM_OPERATOR') "
                            + "AND ((T.TICKET_TYPE = 'TO_EXIT' AND T.ARRIVAL_DATE >= '2017-11-22 00:00:00') OR (T.TICKET_TYPE = 'TO_DUBAI' AND T.ARRIVAL_DATE >= '2017-11-28 00:00:00')) "
                            + "AND H.START_DATE < ?1 "
                            + "ORDER BY H.NAME ")
    public List<Object[]> getHousemaidChecklistDataReport2_d(Date date);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT DISTINCT H.ID, H.NAME, H.STATUS, H.START_DATE "
                            + "FROM HOUSEMAIDS H "
                            + "LEFT JOIN HOUSEMAIDS_REVISIONS HR ON HR.ID = H.ID AND HR.STATUS = 'WITH_CLIENT' "
                            + "LEFT JOIN FREEDOMOPERATORS F ON F.ID = H.FREEDOM_OPERATOR_ID AND F.NAME = 'Clean Exit' "
                            + "WHERE "
                            + "(H.STATUS IN ('WITH_CLIENT' , 'ON_VACATION', 'PENDING_FOR_DISCIPLINE', 'PENDING_FOR_VIDEOSHOOT', 'SICK_WITHOUT_CLIENT') "
                            + "OR (HR.ID IS NOT NULL AND H.STATUS IN ('AVAILABLE', 'RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS')) "
                            + ") "
                            + "AND (H.CLEAN_EXIT_MAID = 1 OR F.ID IS NOT NULL) "
                            + "AND (H.IS_AGENCY = 0 OR H.IS_AGENCY IS NULL) "
                            + "AND H.HOUSEMAID_TYPE NOT IN ('MAID_VISA') "
                            + "AND H.FLIGHT_TO_EXITDATE >= '2017-11-22' "
                            + "AND H.START_DATE < ?1 "
                            + "ORDER BY H.NAME ")
    public List<Object[]> getHousemaidChecklistDataReport2_e(Date date);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT DISTINCT H.ID, H.NAME, H.STATUS, H.START_DATE, P.NAME AS NATIONALITY "
                            + "FROM HOUSEMAIDS H "
                            + "LEFT JOIN HOUSEMAIDS_REVISIONS HR ON HR.ID = H.ID AND HR.STATUS = 'WITH_CLIENT' "
                            + "LEFT JOIN FREEDOMOPERATORS F ON F.ID = H.FREEDOM_OPERATOR_ID AND F.NAME = 'Precy - Sama Agency Freedom Operator' "
                            + "LEFT JOIN PICKLISTS_ITEMS P ON P.ID = H.NATIONALITY_ID "
                            + "WHERE "
                            + "(H.STATUS IN ('WITH_CLIENT' , 'ON_VACATION', 'PENDING_FOR_DISCIPLINE', 'PENDING_FOR_VIDEOSHOOT', 'SICK_WITHOUT_CLIENT') "
                            + "OR (H.STATUS IN ('RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS') AND (H.IS_AGENCY = TRUE OR F.ID IS NOT NULL)) "
                            + "OR (HR.ID IS NOT NULL AND (H.STATUS = 'AVAILABLE' OR (H.STATUS IN ('RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS') AND (H.IS_AGENCY = FALSE AND F.ID IS NULL)))) "
                            + ") "
                            + "AND H.HOUSEMAID_TYPE = 'WALKIN' "
                            + "AND H.START_DATE < ?1 "
                            + "ORDER BY H.NAME ")
    public List<Object[]> getHousemaidChecklistDataReport2_f(Date date);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT DISTINCT H.ID, H.NAME, H.STATUS, H.START_DATE "
                            + "FROM HOUSEMAIDS H LEFT JOIN HOUSEMAIDS_REVISIONS HR ON HR.ID = H.ID AND HR.STATUS = 'WITH_CLIENT' "
                            + "LEFT JOIN FREEDOMOPERATORS F ON F.ID = H.FREEDOM_OPERATOR_ID AND F.NAME = 'Precy - Sama Agency Freedom Operator' "
                            + "WHERE "
                            + "(H.STATUS IN ('WITH_CLIENT' , 'ON_VACATION', 'PENDING_FOR_DISCIPLINE', 'PENDING_FOR_VIDEOSHOOT', 'SICK_WITHOUT_CLIENT') "
                            + "OR (H.STATUS IN ('RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS') AND (H.IS_AGENCY = TRUE OR F.ID IS NOT NULL)) "
                            + "OR (HR.ID IS NOT NULL AND (H.STATUS = 'AVAILABLE' OR (H.STATUS IN ('RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS') AND (H.IS_AGENCY = FALSE AND F.ID IS NULL)))) "
                            + ") "
                            + "AND H.HOUSEMAID_TYPE <> 'MAID_VISA' "
                            + "AND H.START_DATE >= ?1 "
                            + "AND H.START_DATE < ?2 "
                            + "ORDER BY H.NAME ")
    public List<Object[]> getHousemaidChecklistDataReport3(Date startDate, Date endDate);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT H.NAME AS HOUSEMAID_NAME, C.CONTRACT_TYPE, H.STATUS, C.START_OF_CONTRACT, "
                            + "CL.NAME AS CLIENT_NAME, P.AMOUNT_OF_PAYMENT, P.DATE_OF_PAYMENT, PI.NAME AS TYPE_OF_PAYMENT "
                            + "FROM PAYMENTS P "
                            + "INNER JOIN CONTRACTS C ON P.CONTRACT_ID = C.ID "
                            + "INNER JOIN CLIENTS CL ON CL.ID = C.CLIENT_ID "
                            + "LEFT JOIN HOUSEMAIDS H ON H.ID = C.HOUSEMAID_ID "
                            + "LEFT JOIN PICKLISTS_ITEMS PI ON P.TYPE_OF_PAYMENT_ID = PI.ID "
                            + "WHERE "
                            + "P.DATE_OF_PAYMENT BETWEEN ?1 AND ?2 "
                            + "AND P.STATUS = 'RECEIVED' "
                            + "AND C.LIVING = 'OUT' "
                            + "AND C.STATUS = 'ACTIVE' "
                            + "ORDER BY H.NAME ")
    public List<Object[]> getHousemaidChecklistDataReport4(Date startDate, Date endDate);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT DISTINCT H.ID, H.NAME, H.STATUS, H.START_DATE, W.DATE, W.DEDUCTION "
                            + "FROM HOUSEMAIDS H "
                            + "INNER JOIN WARNINGLETTERS W ON W.HOUSEMAID_ID = H.ID "
                            + "LEFT JOIN HOUSEMAIDS_REVISIONS HR ON HR.ID = H.ID AND HR.STATUS = 'WITH_CLIENT' "
                            + "LEFT JOIN FREEDOMOPERATORS F ON F.ID = H.FREEDOM_OPERATOR_ID AND F.NAME = 'Precy - Sama Agency Freedom Operator' "
                            + "WHERE "
                            + "(H.STATUS IN ('WITH_CLIENT' , 'ON_VACATION', 'PENDING_FOR_DISCIPLINE', 'PENDING_FOR_VIDEOSHOOT', 'SICK_WITHOUT_CLIENT') "
                            + "OR (H.STATUS IN ('RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS') AND (H.IS_AGENCY = TRUE OR F.ID IS NOT NULL)) "
                            + "OR (HR.ID IS NOT NULL AND (H.STATUS = 'AVAILABLE' OR (H.STATUS IN ('RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS') AND (H.IS_AGENCY = FALSE AND F.ID IS NULL)))) "
                            + ") "
                            + "AND W.DEDUCTION > 0 "
                            + "AND W.DATE >= ?1 AND W.DATE < ?2 "
                            + "AND H.START_DATE < ?2 "
                            + "ORDER BY H.NAME ")
    public List<Object[]> getHousemaidChecklistDataReport5(Date startDate, Date endDate);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT H.NAME AS HOUSEMAID_NAME, H.STATUS, H.START_DATE, C.START_OF_CONTRACT, CL.NAME AS CLIENT_NAME, P.ID PAYMENT_ID, P.AMOUNT_OF_PAYMENT, P.DATE_OF_PAYMENT, P.STATUS AS STATUS_OF_PAYMENT, "
                            + "PI.NAME AS TYPE_OF_PAYMENT, PP.ID AS NEXT_PAYMENT_ID, PP.AMOUNT_OF_PAYMENT AS NEXT_PAYMENT_AMOUNT, PP.DATE_OF_PAYMENT AS NEXT_PAYMENT_DATE, PP.STATUS AS NEXT_MONTH_PAYMENT_STATUS, "
                            + "PPI.NAME AS NEXT_PAYMENT_TYPE, (SELECT SUM(AMOUNT_OF_PAYMENT) FROM PAYMENTS PPT WHERE PPT.CONTRACT_ID = P.CONTRACT_ID AND PPT.STATUS = 'RECEIVED') AS TOTAL_PAYMENTS "
                            + "FROM PAYMENTS P "
                            + "LEFT JOIN CONTRACTS C ON P.CONTRACT_ID = C.ID "
                            + "LEFT JOIN CLIENTS CL ON CL.ID = C.CLIENT_ID "
                            + "LEFT JOIN HOUSEMAIDS H ON H.ID = C.HOUSEMAID_ID "
                            + "LEFT JOIN PICKLISTS_ITEMS PI ON P.TYPE_OF_PAYMENT_ID = PI.ID "
                            + "LEFT JOIN PAYMENTS PP ON PP.CONTRACT_ID = C.ID AND PP.STATUS NOT IN ('DELETED') AND PP.DATE_OF_PAYMENT BETWEEN ?3 AND ?4 "
                            + "LEFT JOIN PICKLISTS_ITEMS PPI ON PP.TYPE_OF_PAYMENT_ID = PPI.ID "
                            + "WHERE "
                            + "P.DATE_OF_PAYMENT BETWEEN ?1 AND ?2 "
                            + "AND P.STATUS NOT IN ('DELETED') "
                            + "AND C.STATUS = 'ACTIVE' "
                            + "AND C.CONTRACT_PROSPECT_TYPE_ID = ?5 "
                            + "ORDER BY CL.NAME, C.ID,  P.DATE_OF_PAYMENT ")
    public List<Object[]> getHousemaidChecklistDataReport6(Date startDate, Date endDate, Date startDate2, Date endDate2, Long piId);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT H.ID, H.NAME, H.STATUS, H.START_DATE, H.LANDED_IN_DUBAI_DATE "
                            + "FROM HOUSEMAIDS H "
                            + "WHERE "
                            + "H.STATUS IN ('AVAILABLE' , 'PENDING_FOR_DISCIPLINE', 'PENDING_FOR_VIDEOSHOOT', 'SICK_WITHOUT_CLIENT') "
                            + "AND H.LANDED_IN_DUBAI_DATE IS NOT NULL "
                            + "ORDER BY H.NAME ")
    public List<Object[]> getHousemaidChecklistDataReport7();

    @Query(
            nativeQuery = true,
            value =
                    "SELECT DISTINCT H.ID, H.NAME, H.STATUS, H.START_DATE, PI.NAME AS NATIONALITY, H.LANDED_IN_DUBAI_DATE, "
                            + "R.LABOR_CARD_EXPIRY_DATE, A.CREATION_DATE RENEWAL_DATE, PI2.NAME AS VIP_OR_NOT "
                            + "FROM HOUSEMAIDS H "
                            + "LEFT JOIN PICKLISTS_ITEMS PI ON PI.ID = H.NATIONALITY_ID "
                            + "LEFT JOIN PICKLISTS_ITEMS PI2 ON PI2.ID = H.PAYROLL_TYPE_ID "
                            + "LEFT JOIN RENEWREQUESTS R ON H.ID = R.HOUSEMAID_ID "
                            + "LEFT JOIN ATTACHMENTS A ON A.OWNER_ID = R.ID AND A.OWNER_TYPE = 'RenewRequest' AND A.TAG = 'Rvisa' "
                            + "WHERE "
                            + "A.CREATION_DATE >= '2017-11-04 00:00:00' "
                            + "AND H.START_DATE < ?1 "
                            + "ORDER BY A.CREATION_DATE DESC ")
    public List<Object[]> getHousemaidChecklistDataReport8(Date endDate);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT DISTINCT P.ID MANAGER_NOTE_ID, H.ID, H.NAME, H.STATUS, P.AMOUNT, P.NOTE_REASONE, P.NOTE_DATE, PI.NAME AS ADDITION_REASON "
                            + "FROM PAYROLLMANAGERNOTES P "
                            + "INNER JOIN HOUSEMAIDS H ON P.HOUSEMAID_ID = H.ID "
                            + "LEFT JOIN HOUSEMAIDS_REVISIONS HR ON HR.ID = H.ID AND HR.STATUS = 'WITH_CLIENT' "
                            + "LEFT JOIN FREEDOMOPERATORS F ON F.ID = H.FREEDOM_OPERATOR_ID AND F.NAME = 'Precy - Sama Agency Freedom Operator' "
                            + "LEFT JOIN PICKLISTS_ITEMS PI ON PI.ID = P.ADDITION_REASON_ID "
                            + "WHERE "
                            + "(H.STATUS IN ('WITH_CLIENT' , 'ON_VACATION', 'PENDING_FOR_DISCIPLINE', 'PENDING_FOR_VIDEOSHOOT', 'SICK_WITHOUT_CLIENT') "
                            + "OR (H.STATUS IN ('RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS') AND (H.IS_AGENCY = TRUE OR F.ID IS NOT NULL)) "
                            + "OR (HR.ID IS NOT NULL AND (H.STATUS = 'AVAILABLE' OR (H.STATUS IN ('RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS') AND (H.IS_AGENCY = FALSE AND F.ID IS NULL)))) "
                            + ") "
                            + "AND P.NOTE_TYPE = 'ADDITION' "
                            + "AND P.NOTE_DATE >= ?1 AND P.NOTE_DATE < ?2 "
                            + "AND H.START_DATE < ?2 "
                            + "ORDER BY H.NAME ")
    public List<Object[]> getHousemaidChecklistDataReport9(Date startDate, Date endDate);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT H.ID, H.NAME AS HOUSEMAID_NAME, PT.NAME AS NATIONALITY, COUNT(DISTINCT R.ID) AS count1, "
                            + "(SELECT COUNT(DISTINCT R2.ID) FROM REPLACEMENTS R2 "
                            + "INNER JOIN COMPLAINTS CMP2 ON CMP2.ID = R2.COMPLAINT_ID "
                            + "INNER JOIN COMPLAINTTYPES CT2 ON CMP2.PRIMARY_TYPE_ID = CT2.ID "
                            + "INNER JOIN MAIDMANAGERWORKORDERS MMW2 ON MMW2.TYPE = 'PAYROLL_MANAGER_NOTE_DEDUCTION' AND MMW2.REPLACEMENT_ID = R2.ID "
                            + "INNER JOIN PAYROLLMANAGERNOTES PMN2 ON MMW2.DEDUCTION_ID = PMN2.ID "
                            + "WHERE R2.CREATION_DATE >= ?1 AND R2.CREATION_DATE < ?2  AND R2.OLD_HOUSEMAID_ID = H.ID "
                            + "AND CT2.CODE like 'Refused_To_Work__c' AND PMN2.AMOUNT> 0) AS count12, "
                            + "(SELECT COUNT(DISTINCT R3.ID) FROM REPLACEMENTS R3 "
                            + "INNER JOIN COMPLAINTS T3 ON R3.COMPLAINT_ID = T3.ID "
                            + "INNER JOIN COMPLAINTTYPES CT3 ON T3.PRIMARY_TYPE_ID = CT3.ID "
                            + "WHERE R3.OLD_HOUSEMAID_ID = H.ID AND CT3.CAUSE_FAULT_REPLACEMENT = 1) AS FAULTY_REPLACEMENTS_BALANCE "
                            + "FROM HOUSEMAIDS H "
                            + "INNER JOIN REPLACEMENTS R ON H.ID = R.OLD_HOUSEMAID_ID "
                            + "INNER JOIN COMPLAINTS T ON R.COMPLAINT_ID = T.ID "
                            + "INNER JOIN COMPLAINTTYPES CT ON T.PRIMARY_TYPE_ID = CT.ID "
                            + "INNER JOIN MAIDMANAGERWORKORDERS MMW ON MMW.TYPE = 'PAYROLL_MANAGER_NOTE_DEDUCTION' AND MMW.REPLACEMENT_ID = R.ID "
                            + "INNER JOIN PAYROLLMANAGERNOTES PMN ON MMW.DEDUCTION_ID = PMN.ID "
                            + "LEFT JOIN PICKLISTS_ITEMS PT ON PT.ID = H.NATIONALITY_ID "
                            + "WHERE R.CREATION_DATE >= ?1 AND R.CREATION_DATE < ?2 "
                            + "AND CT.CAUSE_FAULT_REPLACEMENT = 1 "
                            + "AND PMN.AMOUNT > 0 "
                            + "GROUP BY H.ID "
                            + "ORDER BY H.NAME ")
    public List<Object[]> getHousemaidChecklistDataReport10(Date startDate, Date endDate);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT H.ID, H.NAME, H.STATUS, H.START_DATE, PL.NAME AS NATIONALITY, F.CREATION_DATE, F.REASON, F.FAILED_INTERVIEW_BALANCE, "
                            + "(SELECT M.FINAL_DECISION FROM MAIDMANAGERWORKORDERS M "
                            + "WHERE M.HOUSEMAID_ID = F.HOUSEMAID_ID AND M.FAILED_INTERVIEWS_COUNT = F.FAILED_INTERVIEW_BALANCE "
                            + "AND DATE(M.CREATION_DATE) >= DATE(F.CREATION_DATE) "
                            + "AND M.TYPE = 'PAYROLL_MANAGER_NOTE_DEDUCTION' "
                            + "AND M.REASON_ID = ?1 "
                            + "ORDER BY M.CREATION_DATE LIMIT 1) AS IS_FORGIVED "
                            + "FROM HOUSEMAIDS H "
                            + "INNER JOIN FAILEDINTERVIEWBALANCELOGS F ON F.HOUSEMAID_ID = H.ID "
                            + "LEFT JOIN PICKLISTS_ITEMS PL ON PL.ID = H.NATIONALITY_ID "
                            + "WHERE "
                            + "F.REASON = 'FAILED_INTERVIEW' "
                            + "AND F.CREATION_DATE >= ?2 AND F.CREATION_DATE < ?3 "
                            + "AND F.FAILED_INTERVIEW_BALANCE >= 10 "
                            + "AND H.DATE_OF_TERMINATION IS NULL "
                            + "ORDER BY H.NAME, F.CREATION_DATE ")
    public List<Object[]> getHousemaidChecklistDataReport11(Long piId, Date startDate, Date endDate);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT H.ID, H.NAME, A.CREATION_DATE, (SELECT CASE WHEN A.ID IS NULL THEN 'NO' ELSE 'YES' END) AS RVISA_ISSUED "
                            + "FROM HOUSEMAIDS H "
                            + "INNER JOIN RENEWREQUESTS R ON H.ID = R.HOUSEMAID_ID "
                            + "LEFT JOIN ATTACHMENTS A ON A.OWNER_ID = R.ID AND A.OWNER_TYPE = 'RenewRequest' AND A.TAG LIKE 'Rvisa' "
                            + "WHERE "
                            + "R.STOPPED = 0 AND H.STATUS = 'ON_VACATION' "
                            + "ORDER BY H.NAME, A.CREATION_DATE ")
    public List<Object[]> getHousemaidChecklistDataReport12();

    @Query(
            nativeQuery = true,
            value =
                    "SELECT H.ID, H.NAME, PL.NAME AS NATIONALITY, C.ID AS CONTRACT_NAME, CL.NAME AS CLIENT_NAME, "
                            + "C.CONTRACT_TYPE, P.ID PAYMENT_ID, P.AMOUNT_OF_PAYMENT "
                            + "FROM HOUSEMAIDS H "
                            + "LEFT JOIN PICKLISTS_ITEMS PL ON PL.ID = H.NATIONALITY_ID "
                            + "LEFT JOIN CONTRACTS C ON C.HOUSEMAID_ID = H.ID AND C.STATUS = 'ACTIVE' "
                            + "LEFT JOIN CLIENTS CL ON CL.ID = C.CLIENT_ID "
                            + "LEFT JOIN PAYMENTS P ON P.CONTRACT_ID = C.ID AND P.TYPE_OF_PAYMENT_ID = ?1 AND P.STATUS = 'RECEIVED' AND P.DATE_OF_PAYMENT BETWEEN ?2 AND ?3 "
                            + "WHERE "
                            + "H.STATUS = 'WITH_CLIENT' "
                            + "AND H.HOUSEMAID_TYPE <> 'MAID_VISA' "
                            + "AND H.START_DATE < ?4 "
                            + "ORDER BY H.NAME ")
    public List<Object[]> getHousemaidChecklistDataReport13(Long piId, Date startDate, Date endDate, Date date);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT H.ID, H.NAME, H.STATUS, H.START_DATE, H.LANDED_IN_DUBAI_DATE,  PL.NAME AS nationality, "
                            + "(SELECT LOG.CREATION_DATE FROM LIVEINOUTLOGS LOG WHERE H.ID = LOG.HOUSEMAID_ID AND LOG.REASON IN ('END_OF_CONTRACT' , 'REPLACEMENT_OLD_HOUSEMAID') ORDER BY LOG.CREATION_DATE DESC LIMIT 1) AS LAST_DATE, "
                            + "(SELECT F.FAILED_INTERVIEW_BALANCE FROM FAILEDINTERVIEWBALANCELOGS F WHERE F.HOUSEMAID_ID = H.ID ORDER BY F.CREATION_DATE DESC LIMIT 1) AS FAILED_INTERVIEWS_BALANCE "
                            + "FROM HOUSEMAIDS H "
                            + "LEFT JOIN PICKLISTS_ITEMS PL ON PL.ID = H.NATIONALITY_ID  "
                            + "WHERE "
                            + "H.STATUS NOT IN ('WITH_CLIENT') "
                            + "AND H.HOUSEMAID_TYPE <> 'MAID_VISA' "
                            + "AND DATE(H.START_DATE) < ?1 "
                            + "ORDER BY H.NAME ")
    public List<Object[]> getHousemaidChecklistDataReport14(Date endDate);

    @Query(
            nativeQuery = true,
            value =
                    "SELECT DISTINCT P.ID MANAGER_NOTE_ID, H.ID, H.NAME, H.STATUS, P.AMOUNT, P.NOTE_REASONE, P.NOTE_DATE, PI.NAME AS DEDUCTION_REASON "
                            + "FROM PAYROLLMANAGERNOTES P "
                            + "INNER JOIN HOUSEMAIDS H ON P.HOUSEMAID_ID = H.ID "
                            + "LEFT JOIN HOUSEMAIDS_REVISIONS HR ON HR.ID = H.ID AND HR.STATUS = 'WITH_CLIENT' "
                            + "LEFT JOIN FREEDOMOPERATORS F ON F.ID = H.FREEDOM_OPERATOR_ID AND F.NAME = 'Precy - Sama Agency Freedom Operator' "
                            + "LEFT JOIN PICKLISTS_ITEMS PI ON PI.ID = P.DEDUCTION_REASON_ID "
                            + "WHERE "
                            + "(H.STATUS IN ('WITH_CLIENT' , 'ON_VACATION', 'PENDING_FOR_DISCIPLINE', 'PENDING_FOR_VIDEOSHOOT', 'SICK_WITHOUT_CLIENT') "
                            + "OR (H.STATUS IN ('RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS') AND (H.IS_AGENCY = TRUE OR F.ID IS NOT NULL)) "
                            + "OR (HR.ID IS NOT NULL AND (H.STATUS = 'AVAILABLE' OR (H.STATUS IN ('RESERVED_FOR_PROSPECT' , 'RESERVED_FOR_REPLACEMENT', 'VIP_RESERVATIONS') AND (H.IS_AGENCY = FALSE AND F.ID IS NULL)))) "
                            + ") "
                            + "AND P.NOTE_TYPE = 'DEDUCTION' "
                            + "AND P.NOTE_DATE >= ?1 AND P.NOTE_DATE < ?2 "
                            + "AND H.START_DATE < ?2 "
                            + "ORDER BY H.NAME, P.CREATION_DATE")
    public List<Object[]> getHousemaidChecklistDataReport15(Date startDate, Date endDate);

    @Query("select  h from Housemaid  h " +
            "where h in ?1 and h.withMolNumber = false and " +
            "((h.phoneNumber is null or h.phoneNumber ='') " +
                "or(h.name is null or h.name = ''))")
    List<Housemaid> getHousemaidMissingFieldsByPaymentMethodLocalTransfer(List<Housemaid> housemaids);

    @Query("select  h from Housemaid  h left join h.visaNewRequest " +
            "where h in ?1 and h.withMolNumber = true and " +
            "((h.name is null or h.name ='') " +
                "or(h.visaNewRequest is null )" +
                "or(h.visaNewRequest.employeeUniqueId is null or h.visaNewRequest.employeeUniqueId = '') " +
                "or(h.visaNewRequest.employeeAccountWithAgent is null or h.visaNewRequest.employeeAccountWithAgent = '') " +
                "or(h.visaNewRequest.agentId is null or h.visaNewRequest.agentId = ''))")
    List<Housemaid> getHousemaidMissingFieldsByPaymentMethodWPS(List<Housemaid> housemaids);

    @Query("select h from  SicknessLeave l inner join  l.housemaid h " +
            "where l.fromDate  >= ?2 and l.toDate <= ?3 and h in ?4 and l.confirmedPaidDurationByAuditor = false " +
            "group by h " +
            "having SUM( function('DATEDIFF ',l.toDate,l.fromDate) + 1 ) > ?1 ")
    public List<Housemaid> findSickMaidByDurationMoreThanAndDateBetween(long sickLeaveDaysLimit, Date lastPayrollLockDate, Date currentMonthLockDate,List<Housemaid> housmaids);

    @Query(
            nativeQuery = true,
            value = "select ID from ( "+
                    "select  h.ID, " +
                    "case " +
                        "when ?2 is not null  and  ?3 is not null  and l.LOAN_DATE < ?2 and l.LOAN_DATE >= ?3 then 1 " +
                        "when ?3 is not null  and  ?4 is not null  and l.LOAN_DATE < ?3 and l.LOAN_DATE >= ?4 then 2 " +
                        "when ?4 is not null  and  ?5 is not null  and l.LOAN_DATE < ?4 and l.LOAN_DATE >= ?5 then 3 " +
                        "when ?5 is not null  and  ?6 is not null  and l.LOAN_DATE < ?5 and l.LOAN_DATE >= ?6 then 4 " +
                        "when ?6 is not null  and  ?7 is not null  and l.LOAN_DATE < ?6 and l.LOAN_DATE >= ?7 then 5 " +
                        "else 0 "+
                    "end as freq " +
                    "from  EMPLOYEELOANS l " +
                    "inner join HOUSEMAIDS h on h.ID = l.HOUSEMAID_ID where l.SKIP_LOAN_REPAYMENT_FOR_CURRENT_PAYROLL = 1 and l.IS_SHOWN = 1 and h.ID in ?1  ) as a " +
                    "group by ID having max(freq) > 0 and (count(distinct freq) > 1 or ( count( freq) > 1 and ?4 is null ))")
    List<BigInteger> findByRepetitiveSkippedLoanLimit(List<Long> housemaidIds,
                                                      Date endLockDateCurrentMonth,
                                                      Date endLockDatePrevOneMonth,
                                                      Date endLockDatePrevTowMonth,
                                                      Date endLockDatePrevThreeMonth,
                                                      Date endLockDatePrevFourMonth,
                                                      Date endLockDatePrevFiveMonth);

    @Query("SELECT h FROM Housemaid h INNER JOIN h.visaNewRequest v WHERE h.housemaidType IN ?2 AND TRIM(LEADING '0' FROM v.employeeUniqueId) IN ?1")
    List<Housemaid> findByUniqueIdAndHousemaidType(List<String> uniqueIdsList, List<HousemaidType> housemaidTypeList);

    @Query("SELECT h.id FROM Housemaid h INNER JOIN h.visaNewRequest v WHERE TRIM(LEADING '0' FROM v.employeeUniqueId) NOT IN ?1")
    List<Long> findNotInMOLFile(List<String> uniqueIds);

    @Modifying
    @Query("UPDATE Housemaid h SET h.withMolNumber = false WHERE h.id IN ?1")
    int updateNotInMOLFile(List<Long> notInFileHousemaids);

    @Query("SELECT h.id FROM Housemaid h INNER JOIN h.visaNewRequest v WHERE TRIM(LEADING '0' FROM v.employeeUniqueId) IN ?1")
    List<Long> findInMOLFile(List<String> uniqueIds);

    @Modifying
    @Query("UPDATE Housemaid h SET h.withMolNumber = true WHERE h.id IN ?1")
    int updateInMOLFile(List<Long> inFileHousemaids);

    @Query("SELECT h FROM Housemaid h WHERE h IN ?1 AND h NOT IN ?2")
    List<Housemaid> findInFirstAndNotInSecond(List<Housemaid> firstList, List<Housemaid> secondList);

    @Query("select new com.magnamedia.extra.payroll.init.HousemaidFieldProjection(h.id, nationality.name) from Housemaid h join h.nationality nationality where h.id in ?1")
    List<HousemaidFieldProjection> findNationalities(List<Long> housemaids);

    @Query("select nationality.name from Housemaid h join h.nationality nationality where h = ?1 group by h")
    String findNationalityByHousemaid(Housemaid housemaid);

    @Query("select new com.magnamedia.extra.payroll.init.HousemaidFieldProjection(h.id, operator.name) from Housemaid h join h.freedomOperator operator where h.id in ?1")
    List<HousemaidFieldProjection> findFreedomNames(List<Long> housemaids);

    @Query("select operator.name from Housemaid h join h.freedomOperator operator where h = ?1 group by h")
    String findFreedomNameByHousemaid(Housemaid housemaid);

    @Query("select new com.magnamedia.extra.payroll.init.HousemaidVisaInfoProjection(h.id, visa.employeeUniqueId, visa.employeeAccountWithAgent, visa.agentId) from Housemaid h join h.visaNewRequest visa where h.id in ?1")
    List<HousemaidVisaInfoProjection> findVisaInfo(List<Long> housemaids);

    @Query("select visa.employeeUniqueId from Housemaid h join h.visaNewRequest visa where h = ?1 group by h")
    String findEmployeeUniqueIdByHousemaid(Housemaid housemaid);

    @Query("select visa.employeeAccountWithAgent from Housemaid h join h.visaNewRequest visa where h = ?1 group by h")
    String findEmployeeAccountWithAgentByHousemaid(Housemaid housemaid);

    @Query("SELECT DISTINCT h FROM Housemaid h "
            + "JOIN h.visaNewRequest visa "
            + "WHERE h IN :housemaids "
            + "AND EXISTS ("
            + "  SELECT 1 FROM Housemaid other "
            + "  JOIN other.visaNewRequest v "
            + "  WHERE other <> h "
            + "  AND v.employeeAccountWithAgent = visa.employeeAccountWithAgent"
            + ")")
    List<Housemaid> findHousemaidsWithNotUniqueEmployeeAccountWithAgent(@Param("housemaids") List<Housemaid> housemaids);

    @Query("SELECT DISTINCT h FROM Housemaid h "
            + "JOIN h.visaNewRequest visa "
            + "WHERE h IN :housemaids "
            + "AND EXISTS ("
            + "  SELECT 1 FROM Housemaid other "
            + "  JOIN other.visaNewRequest v "
            + "  WHERE other <> h "
            + "  AND v.employeeUniqueId = visa.employeeUniqueId"
            + ")")
    List<Housemaid> findHousemaidsWithNotUniqueAnsariUniqueId(@Param("housemaids") List<Housemaid> housemaids);



    @Query("select visa.agentId from Housemaid h join h.visaNewRequest visa where h = ?1 group by h")
    String findAgentIdByHousemaid(Housemaid housemaid);

    @Query("select count(h) from Housemaid h WHERE h in ?1 and h.withMolNumber = ?2")
    Integer countWithMolFile(List<Housemaid> targetList, Boolean withMolNumber);

    @Query("select h from Housemaid h WHERE h in ?1 and h.withMolNumber = false")
    List<Housemaid> getWithoutMolNumber(List<Housemaid> targetList);

    @Query(value = "SELECT MIN(H.REVISION) FROM HOUSEMAIDS_REVISIONS H WHERE H.ID IN ?1 AND " +
            " H.LAST_MODIFICATION_DATE < ?2 GROUP BY H.ID",
            nativeQuery = true)
    List<Long> getFirstRevisions(List<Long> housemaids, Date maxDate);

    @Query(value = "SELECT MIN(H.REVISION) FROM HOUSEMAIDS_REVISIONS H WHERE H.ID = ?1 AND " +
            " H.LAST_MODIFICATION_DATE < ?2",
            nativeQuery = true)
    Long getFirstRevisionsByHousemaid(Long housemaidId, Date maxDate);

    @Query(value = "SELECT MAX(H.REVISION) FROM HOUSEMAIDS_REVISIONS H WHERE H.ID = ?1 AND " +
            " H.LAST_MODIFICATION_DATE < ?2 AND H.STATUS_MODIFIED = TRUE ",
            nativeQuery = true)
    Long getLastRevisionsByHousemaid(Long housemaidId, Date yesterday);


    @Query(value = "SELECT H.ID as id, H.LAST_MODIFICATION_DATE as lastModificationDate, H.STATUS as status FROM HOUSEMAIDS_REVISIONS H WHERE H.ID IN ?1 AND " +
            "(H.STATUS_MODIFIED=TRUE OR H.PENDING_STATUS_MODIFIED=TRUE OR H.REVISION IN ?2) AND H.LAST_MODIFICATION_DATE < ?3 ORDER BY LAST_MODIFICATION_DATE DESC ",
            nativeQuery = true)
    List<HousemaidStatusDateProjection> getRevisions(List<Long> housemaids, List<Long> firstRevisions, Date maxDate, Pageable pageable);

    @Query(value = "SELECT H.ID as id, H.LAST_MODIFICATION_DATE as lastModificationDate, H.STATUS as status FROM HOUSEMAIDS_REVISIONS H WHERE H.ID = ?1 AND " +
            "(H.STATUS_MODIFIED=TRUE OR H.PENDING_STATUS_MODIFIED=TRUE OR H.REVISION = ?2) AND H.LAST_MODIFICATION_DATE < ?3 ORDER BY LAST_MODIFICATION_DATE DESC ",
            nativeQuery = true)
    List<HousemaidStatusDateProjection> getRevisionsByHousemaid(Long housemaid, Long firstRevision, Date maxDate, Pageable pageable);

    @Query(value = "SELECT H.ID as id, H.LAST_MODIFICATION_DATE as lastModificationDate, H.STATUS as status , H.HOUSEMAID_TYPE as housemaidType, H.LIVE_OUT as liveOut, H.PENDING_STATUS as pendingStatus " +
            "FROM HOUSEMAIDS_REVISIONS H WHERE H.ID = ?1 AND " +
            "(H.REVISION = ?2 OR ((H.STATUS_MODIFIED=TRUE OR H.PENDING_STATUS_MODIFIED=TRUE OR H.LIVE_OUT_MODIFIED=TRUE) AND H.REVISION > ?2 AND H.LAST_MODIFICATION_DATE < ?3))  ",
            nativeQuery = true)
    List<HousemaidSalaryProjection> getSalaryRevisionsByHousemaid(Long housemaid, Long yesterdayRevision, Date date);

    @Query(value = "SELECT H.BASIC_SALARY " +
            "FROM HOUSEMAIDS_REVISIONS H WHERE H.ID = ?1 AND H.LAST_MODIFICATION_DATE < ?2 AND H.BASIC_SALARY > 0.0 AND BASIC_SALARY_MODIFIED = TRUE AND LIVE_OUT = ?3 " +
            "ORDER BY H.LAST_MODIFICATION_DATE desc LIMIT 1  ",
            nativeQuery = true)
    Double getHousemaidBasicSalaryComponent(Long housemaidId, Date date, Boolean liveOut);

    @Query(value = "SELECT H.PRIMARY_SALARY " +
            "FROM HOUSEMAIDS_REVISIONS H WHERE H.ID = ?1 AND H.LAST_MODIFICATION_DATE < ?2 AND PRIMARY_SALARY_MODIFIED = TRUE AND LIVE_OUT = ?3 " +
            "ORDER BY H.LAST_MODIFICATION_DATE desc LIMIT 1  ",
            nativeQuery = true)
    Double getHousemaidPrimarySalaryComponent(Long housemaidId, Date date, Boolean liveOut);

    @Query(value = "SELECT H.ACCOMMODATION_SALARY " +
            "FROM HOUSEMAIDS_REVISIONS H WHERE H.ID = ?1 AND H.LAST_MODIFICATION_DATE < ?2 AND ACCOMMODATION_SALARY_MODIFIED = TRUE AND LIVE_OUT = ?3 " +
            "ORDER BY H.LAST_MODIFICATION_DATE desc LIMIT 1  ",
            nativeQuery = true)
    Double getHousemaidAccommodationSalaryComponent(Long housemaidId, Date date, Boolean liveOut);



    @Query("Select h.id from Housemaid h where h.housemaidType = 'MAID_VISA' and h.withMolNumber = ?1 and h.id NOT IN ?2 and (h.excludedFromPayroll is null or h.excludedFromPayroll = false)")
    List<Long> getHousemaidsWithNoActiveContract(boolean withMOl, List<Long> activeContractMaidsIds);

    @Query("Select h.id from Housemaid h where h.housemaidType = 'MAID_VISA' and (h.excludedFromPayroll is null or h.excludedFromPayroll = false) and h.id NOT IN ?1")
    List<Long> getHousemaidsWithNoActiveContract(List<Long> activeContractMaidsIds);

    @Query("SELECT h.id FROM Housemaid h WHERE h.housemaidType = 'MAID_VISA' and h.status = ?1 and h.dateOfTermination < ?2")
    List<Long> getOldTerminatedMVMaids(HousemaidStatus housemaidStatus, java.sql.Date dateOfTermination);

    @Query("SELECT h FROM Housemaid h WHERE h.housemaidType = 'MAID_VISA' " +
            " and (?3 is null or h.withMolNumber = ?3) and " +
            " ( (h.status = ?1 and h.dateOfTermination >= ?2) " +
            " or (h.status <>  ?1))")
    List<Housemaid> getTargetMVMaids(HousemaidStatus housemaidStatus, java.sql.Date dateOfTermination, Boolean withMol);

    @Query("SELECT h FROM Housemaid h WHERE h.housemaidType = 'MAID_VISA' " +
            " and (?3 is null or h.withMolNumber = ?3) and " +
            " ( (h.status = ?1 and h.dateOfTermination >= ?2) " +
            " or (h.status <>  ?1)) " +
            " and h.id not in ?4")
    List<Housemaid> getTargetMVMaidsWithoutTransferred(HousemaidStatus housemaidStatus, java.sql.Date dateOfTermination, Boolean withMol, List<Long> transferredIds);

    @Query("select h.id as housemaidId, c.id as contractId, h.basicSalary as basicSalary, " +
            "(case when ba.infoValue = 'true' then 1 else 0 end) as preCollectedSalary " +
            "FROM Contract c join c.housemaid h " +
            "left join BaseAdditionalInfo ba on ba.ownerId = c.id " +
            "    and ba.ownerType = c.entityType " +
            "    and ba.infoKey = 'preCollectedSalary' " +
            "WHERE h.housemaidType = 'MAID_VISA' " +
            " and (?3 is null or h.withMolNumber = ?3) and " +
            " ( (h.status = ?1 and h.dateOfTermination >= ?2) " +
            " or (h.status <>  ?1)) " +
            " and c.status = ?4")
    List<HousemaidContractProjection> getTargetMVMaidsV2(HousemaidStatus housemaidStatus, java.sql.Date dateOfTermination, Boolean withMol, ContractStatus contractStatus);

    @Query("SELECT h.id as housemaidId, c.id as contractId, h.basicSalary as basicSalary, " +
            "(case when ba.infoValue = 'true' then 1 else 0 end) as preCollectedSalary " +
            "FROM Contract c join c.housemaid h " +
            "left join BaseAdditionalInfo ba on ba.ownerId = c.id " +
            "   and ba.ownerType = c.entityType " +
            "   and ba.infoKey = 'preCollectedSalary' " +
            "WHERE h.housemaidType = 'MAID_VISA' " +
            " and (?3 is null or h.withMolNumber = ?3) and " +
            " ( (h.status = ?1 and h.dateOfTermination >= ?2) " +
            " or (h.status <>  ?1)) " +
            " and h.id not in ?4 and c.status = ?5")
    List<HousemaidContractProjection> getTargetMVMaidsWithoutTransferredV2(HousemaidStatus housemaidStatus, java.sql.Date dateOfTermination, Boolean withMol, List<Long> transferredIds, ContractStatus contractStatus);

    @Query(nativeQuery = true,
            value = "SELECT " +
                    "h.ID as housemaidId, cr.ID as contractId, h.BASIC_SALARY as basicSalary, " +
                    "CASE " +
                    "WHEN ba.INFO_VALUE = 'true' THEN 1 " +
                    "ELSE 0 " +
                    "END " +
                    "as preCollectedSalary " +
                    "FROM " +
                    "    HOUSEMAIDS h " +
                    "        LEFT JOIN " +
                    "    CONTRACTS_REVISIONS cr ON cr.HOUSEMAID_ID = h.ID " +
                    "        AND cr.REVISION = (SELECT " +
                    "            ccr.REVISION " +
                    "        FROM " +
                    "            CONTRACTS_REVISIONS ccr " +
                    "        WHERE " +
                    "            ccr.HOUSEMAID_ID = h.ID " +
                    "                AND ccr.STATUS IN ('CANCELLED' , 'EXPIRED') " +
                    "        ORDER BY ccr.LAST_MODIFICATION_DATE DESC " +
                    "        LIMIT 1) " +
                    "        LEFT JOIN BASEADDITIONALINFOS ba ON ba.OWNER_ID = cr.ID AND ba.OWNER_TYPE = cr.ENTITY_TYPE AND ba.INFO_KEY = 'preCollectedSalary' " +
                    "WHERE " +
                    "    h.HOUSEMAID_TYPE = 'MAID_VISA' " +
                    " AND (?3 IS NULL OR h.WITH_MOL_NUMBER = ?3)" +
                    " AND ( (h.STATUS = ?1 and h.DATE_OF_TERMINATION >= ?2) OR (h.STATUS <> ?1)) " +
                    " AND h.ID NOT IN ?4")
    List<HousemaidContractProjection> getTargetMVMaidsFromContractRevision(String housemaidStatus, java.sql.Date dateOfTermination, Boolean withMol, List<Long> notNeededMaidsIds);

    @Query("Select h.id from Housemaid h left join HousemaidPayrollLog log on h = log.housemaid and log.monthlyPaymentRule in ?2 left join PayrollManagerNote p on h = p.housemaid and p.lastModificationDate > log.creationDate " +
            "left join ScheduledAnnualVacation sav on h = sav.housemaid and sav.lastModificationDate > log.creationDate left join HousemaidVacation hv on h = hv.housemaid and hv.lastModificationDate > log.creationDate " +
            "left join Repayment r on h = r.housemaid and r.lastModificationDate > log.creationDate " +
            "left join HousemaidAccommodation ha on h = ha.housemaid and ha.lastModificationDate > log.creationDate left join EmployeeLoan el on h = el.housemaid and el.lastModificationDate > log.creationDate " +
            "left join HousemaidUnpaidDay ud on h = ud.housemaid and ud.lastModificationDate > log.creationDate " +
            "left join Contract c on h = c.housemaid and c.lastModificationDate > log.creationDate left join Client cl on c.client = cl and cl.lastModificationDate > log.creationDate " +
            "left join NewRequest v on h.visaNewRequest = v and v.lastModificationDate > log.creationDate " +
            "where h in ?1 " +
            "and ((log is null) or (" +
            "   (?3 is NULL or log.payrollAuditTodo = ?3) " +
            "   and (" +
            "   (h.lastModificationDate > log.creationDate) " +
            "   or (p is not null) " +
            "   or (sav is not null) " +
            "   or (hv is not null) " +
            "   or (r is not null) " +
            "   or (ha is not null) " +
            "   or (el is not null) " +
            "   or (ud is not null) " +
            "   or (c is not null) " +
            "   or (cl is not null) " +
            "   or (v is not null) " +
            "   )" +
            ")) "
    )
    List<Long> getChangedHousemaids(List<Housemaid> housemaids, List<MonthlyPaymentRule> monthlyPaymentRules, PayrollAuditTodo auditTodo);

    @Query("Select h.id from Housemaid h left join HousemaidPayrollLog log on h = log.housemaid and log.monthlyPaymentRule in ?2 " +
            "left join HousemaidPayrollAttendanceLog attendance on (h = attendance.housemaid and attendance.payrollMonth = ?4) " +
            "where h in ?1 " +
            "and ((log is null) or (" +
            "   (log.monthlyPaymentRule in ?2) and " +
            "   (?3 is NULL or log.payrollAuditTodo = ?3) " +
            "   and (" +
            "   (attendance.lastModificationDate > log.creationDate) " +
            "   )" +
            ")) "
    )
    List<Long> getChangedHousemaidGroups(List<Housemaid> housemaids, List<MonthlyPaymentRule> monthlyPaymentRules, PayrollAuditTodo auditTodo, java.sql.Date payrollMonth);

    @Query("select h.id from Housemaid h where " +
            "h.nationality = ?1 and h.housemaidType in ?2 " +
            "and h in ?3")
    List<Long> findHousemaidsByNationalityAndType(PicklistItem nationality, List<HousemaidType> housemaidTypes, List<Housemaid> housemaidList);

    @Query("select h.id from Housemaid h inner join RenewRequest r on h = r.housemaid where " +
            "h.nationality = ?1 and h.housemaidType in ?2 " +
            "and h in ?3 and r.completed = true")
    List<Long> findHousemaidsByNationalityAndTypeRenewed(PicklistItem nationality, List<HousemaidType> housemaidTypes, List<Housemaid> housemaidList);

    @Query("select h.id from Housemaid h left join RenewRequest r on h = r.housemaid where " +
            "h.nationality = ?1 and h.housemaidType in ?2 " +
            "and h in ?3 and h.id not in ?4 and (r is null or r.completed = false) ")
    List<Long> findHousemaidsByNationalityAndTypeNotRenewed(PicklistItem nationality, List<HousemaidType> housemaidTypes, List<Housemaid> housemaidList, List<Long> renewedMaids);

    @Query("select h.id from Housemaid h inner join h.nationality n where " +
            "?1 MEMBER OF n.tags and h.housemaidType in ?2 " +
            "and h in ?3")
    List<Long> findAfricanHousemaids(Tag africanTag, List<HousemaidType> housemaidTypes, List<Housemaid> housemaidList);

    @Query("select h.id from Housemaid h where h.id in ?1 and h.basicSalary > ?2")
    List<Long> findHousemaidWithSalaryOverNationality(List<Long> housemaids, Double normalSalary);

    @Query("select h.id as housemaidId, h.name as housemaidName, h.startDate as startDate, h.housemaidLastExcludeDetails.notes as reason, h.housemaidLastExcludeDetails.lastExclusionManuallyDate as lastExclusionManuallyDate from Housemaid h where h in ?1 order by h.name")
    List<HousemaidIncludedExcludedMaidsProjection> getExcludedIncludedMaidsInfos(List<Housemaid> housemaids);

    @Query("select h.id as housemaidId, h.name as maidName, " +
            " case when h.housemaidType = 'MAID_VISA' then 'MV' else 'CC' end as maidType,  " +
            " case when h.nationality is null then '' else h.nationality.name end as nationality, " +
            " h.landedInDubaiDate as joiningDate, h.startDate as salaryStartDate " +
            " from Housemaid h WHERE h in ?1 and h.withMolNumber = ?2 order by h.name asc")
    List<CheckListActiveMaidsInERPProjection> getActiveMaidsInERP(List<Housemaid> targetList, Boolean withMolNumber);

    @Query("select h.id from Contract c inner join c.housemaid h where h in ?1 and h.status = ?2 and c.status = ?3")
    List<Long> findWithClientHousemaidWithActiveContract(List<Housemaid> housemaids, HousemaidStatus status, ContractStatus contractStatus);

    @Query("SELECT h from Housemaid h " +
            " where h in ?1 and h.id not in ?2 and h.status = ?3 ")
    List<Housemaid> findHousemaidByStatusAndContractStatus(List<Housemaid> housemaids, List<Long> housemaidsWithActiveContracts, HousemaidStatus status);

    @Query("SELECT  h from Housemaid h where (h.status = ?1 or (h.status = ?2 and h.pendingStatus = ?3)) and h.housemaidType <> ?4")
//            "SELECT hr.ID, hr.STATUS, DATE(hr.LAST_MODIFICATION_DATE) " +
//            "FROM HOUSEMAIDS_REVISIONS hr  INNER JOIN " +
//            "    (SELECT  hr2.ID, MAX(hr2.REVISION) as REVISION " +
//            "        FROM    HOUSEMAIDS_REVISIONS hr2 " +
//            "        WHERE hr2.STATUS_MODIFIED = 1 and DATE(hr2.LAST_MODIFICATION_DATE) <= ?1 " +
//            "        GROUP BY hr2.ID " +
//            "    ) as MaxRevisions ON hr.ID = MaxRevisions.ID and hr.REVISION = MaxRevisions.REVISION WHERE hr.STATUS in ?2 "
    List<Housemaid> findHousemaidsNoShowOrPendingTermination(HousemaidStatus noShow, HousemaidStatus pendingForDiscipline, PendingStatus pendingForTermination, HousemaidType maidVisa);

    @Query("select h from Housemaid h left join Contract c on c.housemaid = h where (c.status = ?1 or (h.status = ?2 and h.dateOfTermination >= ?3)) and h.housemaidType = ?4 " +
            "and (h.startDate < ?5 and (h.replacementSalaryStartDate is null or h.replacementSalaryStartDate < ?5)) " +
            "and h.excludedFromPayroll = false")
    List<Housemaid> getEligibleListForMVMaids(ContractStatus status, HousemaidStatus housemaidStatus, Date dateOfTermination, HousemaidType housemaidType, java.sql.Date dayOf27th);

    @Query("SELECT  h.id from Housemaid h where h.status = ?1 ")
    List<Long> findHousemaidsIdsByStatus(HousemaidStatus status);

    @Query(value =
            "SELECT DISTINCT o.ID as id " +
                    "FROM HOUSEMAIDS_REVISIONS o " +
                    "WHERE o.ID = ?1 AND o.HOUSEMAID_TYPE != 'MAID_VISA' AND o.LAST_MODIFICATION_DATE < ?2 ",
            nativeQuery = true)
    List<Long> getHousemaidsRevisionForMaidByIdAndHousemaidTypeNotEqualAndModificationDateBefore(Long id, Date date);

    @Query(value =
            "SELECT DISTINCT o.ID as id " +
                    "FROM HOUSEMAIDS_REVISIONS o " +
                    "WHERE o.ID = ?1 AND o.HOUSEMAID_TYPE = 'MAID_VISA' AND o.LAST_MODIFICATION_DATE < ?2 ",
            nativeQuery = true)
    List<Long> getHousemaidsRevisionForMaidByIdAndHousemaidTypeEqualAndModificationDateBefore(Long id, Date date);

    List<Housemaid> findByOldHousemaidTypeAndHousemaidTypeNotAndReplacementSalaryStartDateIsNull(HousemaidType oldType, HousemaidType newType);

    @Query("select h from Housemaid h " +
            "left join h.visaNewRequest n " +
            "where (n.entryVisaIssuanceDate is null or " +
            "       n.entryVisaIssuanceDate >= ?3) " +
            "and h in ?1 " +
            "and h.housemaidType = ?2 " +
            "order by h.creationDate desc")
    List<Housemaid> findByHousemaidsAndEntryVisaExpiryDate(
            List<Housemaid> housemaids, HousemaidType housemaidType, Date previousMonth);

    @Query("select h from Housemaid h " +
            "where (h.normalizedPhoneNumber = ?1 or h.phoneNumber = ?2 or h.normalizedWhatsAppPhoneNumber = ?3 or h.whatsAppPhoneNumber = ?4)")
    List<Housemaid> findByNormalizedPhoneNumberOrPhoneNumberOrNormalizedWhatsAppPhoneNumberOrWhatsAppPhoneNumber(String normalizedPhoneNumber, String phoneNumber, String normalizedWhatsAppPhoneNumber, String whatsAppPhoneNumber);

    @Query("SELECT h FROM Housemaid h " +
            "JOIN HousemaidPayrollLog log ON h = log.housemaid " +
            "LEFT JOIN RenewRequest rr ON h = rr.housemaid " +
            "WHERE h IN ?1 " +
            "AND log.payrollMonth = ?2 " +
            "AND ( " +
            "    (h.housemaidType <> 'MAID_VISA' AND rr IS NULL AND log.totalSalary > (h.basicSalary * ?3)) " + // When RenewRequest is null (cc maids)
            "    OR " +
            "    (h.housemaidType <> 'MAID_VISA' AND rr IS NOT NULL AND log.totalSalary > (h.basicSalary * ?4)) " + // When RenewRequest is not null (cc maids)
            "    OR " +
            "    (h.housemaidType = 'MAID_VISA' AND log.totalSalary > (h.basicSalary * ?5)) " + // (visa maids)
            ")"
    )
    List<Housemaid> findMaidsWithExcessNetPayout(List<Housemaid> housemaidList,
                                                 Date payrollMonth,
                                                 Double ccNewMultiplierValue,
                                                 Double ccRenewalMultiplierValue,
                                                 Double mvMultiplierValue);

    @Query("select h from PayrollAccountantTodo p inner join p.includedHousemaids h where p = ?1 and h.housemaidType = ?2")
    List<Housemaid> findIncludedHousemaidsByAccountantTodoAndHousemaidType(PayrollAccountantTodo todo, HousemaidType housemaidType);

    @Query("select h from PayrollAccountantTodo p inner join p.includedHousemaids h where p = ?1 and h.housemaidType <> ?2")
    List<Housemaid> findIncludedHousemaidsByAccountantTodoAndHousemaidTypeNot(PayrollAccountantTodo todo, HousemaidType housemaidType);

    @Query("select h from Housemaid h left join h.visaNewRequest v left join Attachment a on a.ownerType = 'NewRequest' and a.ownerId = v.id and a.tag = ?2 where h in ?1 and  (v is null or (v.completed = false and a is null)) and h.housemaidType = ?3 ")
    List<Housemaid> findMVMaidsWithNoMedicalCertificate(List<Housemaid> housemaidList, String tag, HousemaidType housemaidType);

    @Query(value = "SELECT h.ID " +
            "FROM HOUSEMAIDS h " +
            "LEFT JOIN NEWREQUESTS v ON h.VISA_NEW_REQUEST_ID = v.ID " +
            "WHERE REGEXP_REPLACE(v.NEW_EID_NUMBER, '[^0-9]', '') = REGEXP_REPLACE(:eidNumber, '[^0-9]', '') " +
            "ORDER BY v.CREATION_DATE DESC", nativeQuery = true)
    List<BigInteger> findByNewEidNumberOrderByCreationDateDesc(@Param("eidNumber") String eidNumber);

    @Query("SELECT h " +
            "FROM Housemaid h " +
            "WHERE h.name LIKE ?1")
    List<Housemaid> getHousemaidByNameLike(String name);

}
