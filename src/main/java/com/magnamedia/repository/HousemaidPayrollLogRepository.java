package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.entity.projection.payrollAudit.NonWithClientMaidsProjection;
import com.magnamedia.entity.projection.payrollAudit.TotalHousemaidSalariesPaymentsProjection;
import com.magnamedia.extra.HousemaidPayrollLogProjection;
import com.magnamedia.extra.payroll.init.HousemaidAmountProjection;
import com.magnamedia.extra.payroll.init.HousemaidIntegerAmountProjection;
import com.magnamedia.module.type.HousemaidUnpaidStatus;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

public interface HousemaidPayrollLogRepository extends BaseRepository<HousemaidPayrollLog> {

    HousemaidPayrollLog findFirstByHousemaidAndPayrollAccountantTodo(Housemaid housemaid, PayrollAccountantTodo todo);

    List<HousemaidPayrollLog> findByPayrollAccountantTodoAndTransferredTrue(PayrollAccountantTodo accountantTodo);

    @Query("select l.id from HousemaidPayrollLog l where l.payrollAccountantTodo = ?1 and l.transferred = false and l.willBeIncluded = true")
    List<Long> findByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrue(PayrollAccountantTodo accountantTodo);

    List<HousemaidPayrollLog> findByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonthOrderByHousemaidName(PayrollAccountantTodo accountantTodo, java.sql.Date payrollMonth);

    @Query("select distinct log.housemaid.id from HousemaidPayrollLog log where log.payrollAccountantTodo = ?1 and log.transferred = false and log.willBeIncluded = true and log.payrollMonth = ?2 order by log.housemaid.name ")
    List<Long> findDistinctHousemaidIdByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonthOrderByHousemaidName(PayrollAccountantTodo accountantTodo, java.sql.Date payrollMonth);

    @Query("select sum(log.totalSalary) from HousemaidPayrollLog log where log.payrollAccountantTodo = ?1 and log.transferred = false and log.willBeIncluded = true and log.payrollMonth = ?2")
    Double sumByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonth(PayrollAccountantTodo accountantTodo, java.sql.Date payrollMonth);

    List<HousemaidPayrollLog> findByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonthLessThanAndHousemaidUnpaidStatusOrderByHousemaidName(PayrollAccountantTodo accountantTodo, java.sql.Date payrollMonth, HousemaidUnpaidStatus housemaidUnpaidStatus);

    @Query("select distinct log.housemaid.id from HousemaidPayrollLog log where log.payrollAccountantTodo = ?1 and log.transferred = false and log.willBeIncluded = true and log.payrollMonth < ?2 and log.housemaidUnpaidStatus = ?3 order by log.housemaid.name ")
    List<Long> findDistinctHousemaidIdByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonthLessThanAndHousemaidUnpaidStatusOrderByHousemaidName(PayrollAccountantTodo accountantTodo, java.sql.Date payrollMonth, HousemaidUnpaidStatus housemaidUnpaidStatus);

    @Query("select distinct log.housemaid.id from HousemaidPayrollLog log where log.payrollAccountantTodo = ?1 and log.transferred = false and log.payrollMonth = ?2 and log.logStatus = ?3")
    List<Long> findDistinctHousemaidIdByPayrollAccountantTodoAndTransferredFalseAndPayrollMonthAndLogStatus(PayrollAccountantTodo accountantTodo, java.sql.Date payrollMonth, HousemaidPayrollLog.HousemaidPayrollLogStatus housemaidPayrollLogStatus);

    @Query("select sum(log.totalSalary) from HousemaidPayrollLog log where log.payrollAccountantTodo = ?1 and log.transferred = false and log.willBeIncluded = true and log.payrollMonth < ?2 and log.housemaidUnpaidStatus = ?3")
    Double sumByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonthLessThanAndHousemaidUnpaidStatus(PayrollAccountantTodo accountantTodo, java.sql.Date payrollMonth, HousemaidUnpaidStatus housemaidUnpaidStatus);

    @Query(nativeQuery = true,
            value="select l.STATUS as maidStatus, " +
                    "     sum(case WHEN l.PAYROLL_MONTH >= ?4 and l.PAYROLL_MONTH < ?3 THEN l.TOTAL_SALARY " +
                    "              else 0 " +
                    "         end) as prevOneMonthAmount, " +
                    "     sum(case WHEN l.PAYROLL_MONTH >= ?5 and l.PAYROLL_MONTH < ?4 THEN l.TOTAL_SALARY " +
                    "              else 0 " +
                    "         end) as prevTowMonthAmount, " +
                    "     sum(case WHEN l.PAYROLL_MONTH >= ?4 and l.PAYROLL_MONTH < ?3 THEN 1 " +
                    "              else 0 " +
                    "         end) as prevOneMonthCount, " +
                    "     sum(case WHEN l.PAYROLL_MONTH >= ?5 and l.PAYROLL_MONTH < ?4 THEN 1 " +
                    "              else 0 " +
                    "         end) as prevTowMonthCount " +
                    "from HOUSEMAIDPAYROLLLOGS l " +
                    "where l.STATUS != 'WITH_CLIENT' and l.HOUSEMAID_ID in ?1 and l.PAYROLL_MONTH >= ?5 and l.PAYROLL_MONTH < ?2 " +
                    "group by l.STATUS")
    List<NonWithClientMaidsProjection> findByHousemaidInAndPayrollMonthBetween(List<Long> housemaids, Date currentLockDate, Date prevOneMonthLockDate, Date prevTowLockDate, Date prevThreeLockDate);

    @Query("select hl.housemaid.id from HousemaidPayrollLog hl where hl.payrollMonth=?1 and hl.transferred=true")
    List<Long> findByPayrollMonthAndTransferredTrue(java.sql.Date payrollMonth);

    @Query("select hl from HousemaidPayrollLog hl where hl.housemaid = ?1 and hl.payrollMonth=?2 and hl.transferred=true")
    List<HousemaidPayrollLog> findByPayrollMonthAndTransferredTrueForHousemaid(Housemaid housemaid, java.sql.Date payrollMonth);

    Integer countByPayrollMonthAndTransferredTrueAndHousemaid(java.sql.Date payrollMonth, Housemaid housemaid);

    Integer countByMaidParentLog(HousemaidPayrollLog housemaidPayrollLog);

    @Query("select h.id from HousemaidPayrollLog hl inner join hl.housemaid h where hl.payrollMonth=?1 and hl.transferred=false")
    List<Long> findByPayrollMonthAndTransferredFalse(java.sql.Date payrollMonth);

    @Query("select hl.housemaid.id from HousemaidPayrollLog hl where hl.payrollMonth=?1 and hl.housemaid.id not in ?2 and hl.transferred=true")
    List<Long> findByPayrollMonthAndTransferredTrue(java.sql.Date payrollMonth, List<Long> excludedMaids);

    @Query("select hl from HousemaidPayrollLog hl where hl.payrollMonth=?1 and hl.transferred=true")
    List<HousemaidPayrollLog> findLogByPayrollMonthAndTransferredTrue(java.sql.Date payrollMonth);

    @Query("select hl from HousemaidPayrollLog hl where hl.payrollMonth=?1 and hl.transferred=false and hl.monthlyPaymentRule <> ?2")
    List<HousemaidPayrollLog> findLogByPayrollMonthAndTransferredFalseAndMonthlyPaymentRuleNot(java.sql.Date payrollMonth, MonthlyPaymentRule rule);

    @Query("select hl from HousemaidPayrollLog hl where hl.payrollMonth=?1 and hl.transferred=true and hl.housemaid=?2")
    List<HousemaidPayrollLog> findLogByPayrollMonthAndTransferredTrueAndHousemaid(java.sql.Date payrollMonth, Housemaid housemaid);


    @Query("select count(log) from HousemaidPayrollLog log where log.payrollAccountantTodo = ?1 and " +
            "log.transferred = false and log.transferred = true")
    int countUnCompletedHousemaidTransfer(PayrollAccountantTodo accountantTodo);

    @Query(nativeQuery = true,
            value = "select a.TASK_NAME as paymentMethod," +
                    "     sum(case WHEN p.PAYROLL_MONTH = ?1 THEN p.TOTAL_SALARY " +
                    "              else 0 " +
                    "         end) as currentMonthAmount, " +
                    "     sum(case WHEN p.PAYROLL_MONTH = DATE_SUB(?1,INTERVAL 1 MONTH) THEN p.TOTAL_SALARY " +
                    "              else 0 " +
                    "         end) as prevOneMonthAmount, " +
                    "     sum(case WHEN p.PAYROLL_MONTH = DATE_SUB(?1,INTERVAL 2 MONTH) THEN p.TOTAL_SALARY " +
                    "              else 0 " +
                    "         end) as prevTowMonthAmount, " +
                    "     sum(case WHEN p.PAYROLL_MONTH = ?1  THEN 1 " +
                    "              else 0 " +
                    "         end) as currentMonthCount, " +
                    "     sum(case WHEN p.PAYROLL_MONTH = DATE_SUB(?1,INTERVAL 1 MONTH)  THEN 1 " +
                    "              else 0 " +
                    "         end) as prevOneMonthCount, " +
                    "     sum(case WHEN p.PAYROLL_MONTH = DATE_SUB(?1,INTERVAL 2 MONTH) THEN 1 " +
                    "              else 0 " +
                    "         end) as prevTowMonthCount " +
                    "from  HOUSEMAIDPAYROLLLOGS p " +
                    "inner join PAYROLLACCOUNTANTTODOS a on a.ID = p.PAYROLL_ACCOUNTANT_TODO_ID " +
                    "where p.PAYROLL_MONTH >= DATE_SUB(?1,INTERVAL 2 MONTH) AND p.PAYROLL_MONTH <= ?1 " +
                    "GROUP BY a.TASK_NAME")
    List<TotalHousemaidSalariesPaymentsProjection> getTotalHousemaidSalaries(java.sql.Date payrollMonth);


    @Query("select log from HousemaidPayrollLog log " +
            "where log.transferred=true and log.payslipGenerated=false and log.payslipSent=false and log.payrollAccountantTodo is not null")
    List<HousemaidPayrollLog> findReadyToGeneratePayslips();

    @Query("select log.id from HousemaidPayrollLog log " +
            "where log.transferred=true and log.payslipSent=false " +
            "and log.payrollAccountantTodo is not null and log.payrollMonth >= ?1")
    List<Long> findReadyToSentPayslips(java.sql.Date payrollMonth);

    @Query("select count(log) from HousemaidPayrollLog log " +
            "where log.transferred=true " +
            "and log.payrollAccountantTodo is not null and log.payrollMonth >= ?1")
    Integer countGeneratedPayslips(java.sql.Date payrollMonth);

    @Query("select log from HousemaidPayrollLog log " +
            "where log.transferred=true and log.payslipGenerated=true " +
            "and log.payrollAccountantTodo is not null and log.payrollMonth = ?1")
    List<HousemaidPayrollLog> findByPayslipGenerated(java.sql.Date payrollMonth);

    @Query("select min(log.payrollMonth) from HousemaidPayrollLog log")
    java.sql.Date findMinimumLogDate();

    HousemaidPayrollLog findFirstByPayrollMonthAndHousemaid(java.sql.Date payrollMonth, Housemaid housemaid);

    HousemaidPayrollLog findFirstByHousemaidAndTransferredOrderByIdDesc(Housemaid housemaid, Boolean transferred);

    @Query("select l from HousemaidPayrollLog l where l.payrollMonth = ?1 and l.housemaid = ?2 and l.transferred = ?3 and l.housemaidUnpaidStatus = ?4 ")
    List<HousemaidPayrollLog> findByHousemaid(java.sql.Date payrollMonth, Housemaid housemaid, Boolean transferred, HousemaidUnpaidStatus housemaidUnpaidStatus);

    @Query("select l from HousemaidPayrollLog l where l.housemaid = ?1 and l.transferred = ?2")
    List<HousemaidPayrollLog> findByHousemaid(Housemaid housemaid, Boolean transferred);

    @Query("select log" +
            "  from HousemaidPayrollLog log" +
            "  where log.housemaid.id = ?1" +
            "  AND (?2 is null or log.payrollMonth >= ?2)" +
            "  order by log.payrollMonth ")
    List<HousemaidPayrollLog> getHousemaidPayrollLogBy_Id(Long id, Date beforeMonthsCountDate);

    //PAY-801
    @Query("select log from HousemaidPayrollLog log inner join log.housemaid h " +
            "  where h.id = ?1 " +
            "  AND log.payrollMonth >= ?2 "+
            "  AND log.transferred = false "+
            "  AND log.willBeIncluded = false "+
            "  order by log.payrollMonth ")
    List<HousemaidPayrollLog> getHousemaidPendingPayrollMonths(Long housemaidId, Date fromDate);


    HousemaidPayrollLog findTopByHousemaidAndPayrollMonthAndTransferredTrue(Housemaid housemaid, java.sql.Date payrollMonth);

    HousemaidPayrollLog findTopByHousemaidAndPayrollMonth(Housemaid housemaid, java.sql.Date payrollMonth);

    HousemaidPayrollLog findTopByHousemaidAndPayrollMonthLessThanEqualAndTransferredTrueOrderByCreationDateDesc(Housemaid housemaid, java.sql.Date payrollMonth);

    HousemaidPayrollLog findTopByHousemaidAndPayrollMonthAndMonthlyPaymentRuleNot(Housemaid housemaid, java.sql.Date payrollMonth, MonthlyPaymentRule rule);

    @Query("SELECT new com.magnamedia.extra.payroll.init.HousemaidIntegerAmountProjection(h.id, SUM (e.onVacationDays)) FROM HousemaidPayrollLog e inner join e.housemaid h " +
            "where h.id in ?1 and e.transferred=false and e.housemaidUnpaidStatus=?2 group by h.id")
    List<HousemaidIntegerAmountProjection> housemaidOnVacationDays(List<Long> housemaids, HousemaidUnpaidStatus status);

    @Query("SELECT SUM(e.onVacationDays) FROM HousemaidPayrollLog e " +
            "where e.housemaid = ?1 and e.housemaidUnpaidStatus = ?2 and e.payrollMonth = ?3")
    Long housemaidOnVacationDaysByHousemaid(Housemaid housemaid, HousemaidUnpaidStatus status, java.sql.Date payrollMonth);

    @Query("SELECT new com.magnamedia.extra.payroll.init.HousemaidAmountProjection(h.id, SUM (e.totalSalary)) FROM HousemaidPayrollLog e inner join e.housemaid h " +
            "where h.id in ?1 and e.transferred=false and e.housemaidUnpaidStatus=?2 group by h.id")
    List<HousemaidAmountProjection> housemaidExcludedSalariesAmount(List<Long> housemaids, HousemaidUnpaidStatus status);

    @Query("SELECT SUM (e.totalSalary) FROM HousemaidPayrollLog e " +
            "where e.housemaid = ?1 and e.transferred=false and e.housemaidUnpaidStatus=?2 and e.payrollMonth < ?3 group by e.housemaid")
    Double housemaidExcludedSalariesAmount(Housemaid housemaid, HousemaidUnpaidStatus status, Date payrollMonth);

    @Query("SELECT e FROM HousemaidPayrollLog e inner join e.housemaid h " +
            "where h.id in ?1 and e.transferred=false and e.housemaidUnpaidStatus=?2 and e.payrollMonth < ?3")
    List<HousemaidPayrollLog> getOldExcludedMaidSalaries(List<Long> ids, HousemaidUnpaidStatus status, java.sql.Date payrollMonth);

    @Query("SELECT e FROM HousemaidPayrollLog e " +
            "where e.maidParentLog = ?1")
    List<HousemaidPayrollLog> getOldRelatedMVLogs(HousemaidPayrollLog log);

    @Query("SELECT e FROM HousemaidPayrollLog e inner join e.housemaid h " +
            "where e.payrollAccountantTodo = ?1 and e.transferred=false and e.housemaidUnpaidStatus=?2 and e.willBeIncluded = ?3 and e.payrollMonth < ?4")
    List<HousemaidPayrollLog> getOldUnpaidMaidVisaSalaries(PayrollAccountantTodo todo, HousemaidUnpaidStatus status, boolean willBeIncluded, java.sql.Date payrollMonth);

    List<HousemaidPayrollLog> findByMaidParentLog(HousemaidPayrollLog parentLog);

    HousemaidPayrollLog findTop1ByHousemaidAndTransferredTrueOrderByCreationDateDesc(Housemaid housemaid);

    @Query("SELECT new com.magnamedia.extra.payroll.init.HousemaidIntegerAmountProjection(h.id, SUM (e.onVacationDays)) FROM HousemaidPayrollLog e inner join e.housemaid h " +
            "where h.id = ?1 and e.transferred=false and e.housemaidUnpaidStatus=?2 and e.payrollMonth < ?3 group by h.id")
    HousemaidIntegerAmountProjection getHousemaidVacationDays(Long housemaid, HousemaidUnpaidStatus status, java.sql.Date payrollMonth);

    Integer countByHousemaid(Housemaid housemaid);

    HousemaidPayrollLog findFirstByHousemaidAndPayrollAccountantTodoAndCreationDateGreaterThanEqual(Housemaid housemaid, PayrollAccountantTodo todo, java.sql.Date lockDate);

    @Query("SELECT log from HousemaidPayrollLog log where log.housemaid = ?1 and " +
            "(log.monthlyPaymentRule in ?2) and " +
            "(?3 is NULL or log.payrollAuditTodo = ?3 or log.payrollAuditTodo is null) and " +
            "(?4 is NULL or log.payrollAccountantTodo = ?4 or log.payrollAccountantTodo is null) and " +
            "log.payrollMonth = ?5")
    List<HousemaidPayrollLog> findByHousemaidAndPayrollMonth(Housemaid housemaid, List<MonthlyPaymentRule> monthlyPaymentRules, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, java.sql.Date payrollMonth);

    List<HousemaidPayrollLog> findByMonthlyPaymentRuleAndLogStatus(MonthlyPaymentRule rule, HousemaidPayrollLog.HousemaidPayrollLogStatus logStatus);

//    @Query("SELECT log from HousemaidPayrollLog log where log.housemaid in ?1 and " +
//            "(log.monthlyPaymentRule in ?2) and " +
//            "(?3 is NULL or log.payrollAuditTodo = ?3 or log.payrollAuditTodo is null) and " +
//            "(?4 is NULL or log.payrollAccountantTodo = ?4 or log.payrollAccountantTodo is null) and " +
//            "log.payrollMonth = ?5")
//    List<HousemaidPayrollLog> findByHousemaidAndPayrollMonth(List<Housemaid> housemaids, List<MonthlyPaymentRule> monthlyPaymentRules, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, java.sql.Date payrollMonth);

    @Query("select distinct h.payrollAccountantTodo.id from HousemaidPayrollLog h where h.id in ?1")
    List<Long> findDistinctAccountantTodosForLogs(List<Long> housemaidPayrollLogsIds);

    int countByHousemaidAndPayrollMonth(Housemaid housemaid, java.sql.Date payrollMonth);

    HousemaidPayrollLog findTopByHousemaidOrderByIdDesc(Housemaid housemaid);

    @Query("select new com.magnamedia.extra.HousemaidPayrollLogProjection(h.id, log.id) from " +
            "HousemaidPayrollLog log inner join log.housemaid h " +
            "where h.id in ?1 and log.willBeIncluded = false and log.transferred = false and log.payrollMonth = ?2 " +
            "and h.housemaidType = 'MAID_VISA' ")
    List<HousemaidPayrollLogProjection> findByHousemaidIds(List<Long> housemaidIds, java.sql.Date payrollMonth);

    @Query("select sum(log.totalSalary) from HousemaidPayrollLog log inner join log.housemaid h where log.payrollMonth = ?1 and h.housemaidType <> 'MAID_VISA' and h in ?2")
    Double sumOfCcMaidsTotalSalaryByPayrollMonth(Date payrollMonth, List<Housemaid> housemaidList);

    @Query("select sum(log.totalSalary) from HousemaidPayrollLog log inner join log.housemaid h where log.payrollMonth = ?1 and h.housemaidType <> 'MAID_VISA' and log.transferred = true")
    Double sumOfCcMaidsTransferredSalaryByPayrollMonth(Date payrollMonth);

    @Query("select sum(log.totalSalary) from HousemaidPayrollLog log inner join log.housemaid h where log.payrollMonth = ?1 and h.housemaidType = 'MAID_VISA' and h in ?2")
    Double sumOfMvMaidsTotalSalaryByPayrollMonth(Date payrollMonth, List<Housemaid> mVMaidsList);

    @Query("select sum(log.totalProRatedSalary) from HousemaidPayrollLog log inner join log.housemaid h where log.payrollMonth = ?1 and h.housemaidType <> 'MAID_VISA' and h in ?2")
        // TODO: Check w/analysis team, != maid_visa check (cause we always pay for maid_visa housemaids)
    Double sumOfTotalProratedSalaryByPayrollMonth(Date payrollMonth, List<Housemaid> housemaidList); //group1

    @Query("select sum(log.mohreProRatedSalary) from HousemaidPayrollLog log inner join log.housemaid h where log.payrollMonth = ?1 and h.housemaidType <> 'MAID_VISA' and h in ?2")
    Double sumMohreProratedSalaryByPayrollMonth(Date payrollMonth, List<Housemaid> housemaidList); //group2

    @Query("SELECT log FROM HousemaidPayrollLog log WHERE log.housemaid IN ?2 "
            + "AND log.payrollMonth = ?1 " +
            "AND log.remainingLoan > 0 " +
            "AND (log.loanRepayment IS NULL OR log.loanRepayment = 0)")
    List<HousemaidPayrollLog> findLogsWithLoanBalanceAndNoRepayment(Date payrollMonth, List<Housemaid> housemaidList);

    List<HousemaidPayrollLog> findTop2ByHousemaidOrderByCreationDateDesc(Housemaid housemaid);

    HousemaidPayrollLog findTopByHousemaidAndPayrollMonthBetweenOrderByCreationDateDesc(Housemaid housemaid, java.sql.Date payrollMonth1stDay, java.sql.Date payrollMonthLastDay);


    @Query("select sum(log.totalLiveOutProRatedSalary) from HousemaidPayrollLog log inner join log.housemaid h where log.payrollMonth = ?1 and h.housemaidType <> 'MAID_VISA' and h in ?2")
    Double sumOfTotalLiveOutProratedSalaryByPayrollMonth(Date payrollMonth, List<Housemaid> housemaidList); //group5

    @Query("select sum(log.mohreLiveOutProRatedSalary) from HousemaidPayrollLog log inner join log.housemaid h where log.payrollMonth = ?1 and h.housemaidType <> 'MAID_VISA' and h in ?2")
    Double sumMohreLiveOutProratedSalaryByPayrollMonth(Date payrollMonth, List<Housemaid> housemaidList); //group6


    @Query("select (count(h.id) >0) from HousemaidPayrollLog h where h.transferred = true and h.payrollAccountantTodo is not null and h.payrollMonth >= ?1")
    boolean existTransferredLog(java.sql.Date payrollMonth);

    @Query("select (count(h.id) >0) from HousemaidPayrollLog h where h.transferred = true and h.payslipSent = false and h.payrollAccountantTodo is not null and h.payrollMonth >= ?1")
    boolean existTransferredAndPayslipNotSentLog(java.sql.Date payrollMonth);

    boolean existsByHousemaidAndPayrollMonthAndTransferredTrue(Housemaid housemaid, java.sql.Date payrollMonth);


    @Query("select h from HousemaidPayrollLog h where h.id in ?1")
    List<HousemaidPayrollLog> findDistinctHousemaidPayrollLogByIds(List<Long> housemaidPayrollLogsIds);

    @Query("SELECT log FROM HousemaidPayrollLog log " +
            "LEFT JOIN log.housemaid h " +
            "LEFT JOIN Contract c ON h = c.housemaid " +
            "LEFT JOIN BaseAdditionalInfo ba ON ba.ownerId = c.id AND ba.ownerType = c.entityType AND ba.infoKey = 'preCollectedSalary' " +
            "WHERE h IN ?1 AND log.transferred = false AND log.payrollMonth >= ?2 AND log.payrollMonth < ?3 AND ba.infoValue = ?4")
    List<HousemaidPayrollLog> findByHousemaidsAndTransferredFalseAndPayrollMonthBetweenAndPreCollected(List<Housemaid> housemaids, java.sql.Date fromDate, java.sql.Date toDate, String preCollected);

}
